# Stage 1: Build the React app (Universal Runtime Configuration)
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json yarn.lock ./
RUN npm install

# Copy source code
COPY . .

# Build arguments for universal configuration
ARG ENV_FILE=.env.production
ARG BUILD_SCRIPT=build:prod

# Copy environment file (used only for build-time variables)
# Runtime configuration will be provided via ConfigMap
COPY ${ENV_FILE} .env

# Build universal React app with runtime configuration support
RUN echo "🔨 Building ATS React app with runtime configuration..." && \
    echo "📁 Using ENV_FILE: ${ENV_FILE} (build-time only)" && \
    echo "🚀 Using BUILD_SCRIPT: ${BUILD_SCRIPT} (universal)" && \
    npm run $BUILD_SCRIPT && \
    echo "✅ ATS universal build completed successfully!"

# Stage 2: Serve the universal app with nginx
FROM nginx:alpine

# Copy built assets from builder (includes universal env-config.js)
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Note: env-config.js will be mounted from ConfigMap at runtime
# This allows dynamic backend switching without rebuilding the image

EXPOSE 80

# Create a non-root user to run nginx
RUN getent group nginx || addgroup -g 1001 -S nginx && \
    id -u nginx 2>/dev/null || adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Change ownership of nginx directories to the nginx user
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Switch to nginx user for security
USER nginx

CMD ["nginx", "-g", "daemon off;"]
 