// import { useEffect, useRef } from 'react';

// // ==============================|| HOOKS - FORMS REFERENCE  ||============================== //

// export default function useScriptRef() {
//   const scripted = useRef(true);

//   useEffect(() => {
//     scripted.current = false;
//   }, []);

//   return scripted;
// }

import { useEffect, useRef } from 'react';

// ==============================|| HOOKS - FORMS REFERENCE  ||============================== //

export default function useScriptRef() {
  const scripted = useRef(true);

  useEffect(() => {
    // Set scripted.current to true when the component mounts
    scripted.current = true;

    return () => {
      // Set scripted.current to false when the component unmounts
      scripted.current = false;
    };
  }, []); // The empty dependency array ensures this runs only on mount and unmount

  return scripted;
}