// // import * as React from 'react';
// import Button from '@mui/material/Button';
// import Dialog from '@mui/material/Dialog';
// import DialogActions from '@mui/material/DialogActions';
// import DialogContent from '@mui/material/DialogContent';
// import DialogContentText from '@mui/material/DialogContentText';
// import DialogTitle from '@mui/material/DialogTitle';
// import NameTextField from 'custom-components/Nametextfield';
// import {
//   Grid,
//   FormControl,
//   TextField,

//   MenuItem,
//   InputLabel,
//   Select,
//   Typography
// } from '@mui/material';
// // import { openSnackbar } from 'api/snackbar';
// import { useForm, Controller } from 'react-hook-form';
// import FormHelperText from '@mui/material/FormHelperText';
// import axios from 'axios';

// import React, { useState, useEffect} from 'react';

// export default function FormDialog({ open, handleClose, selectedRow , onUpdate, fetchData }) {
//   // const { control, handleSubmit, formState: { errors }, reset  } = useForm();
//   //  const onSubmit = (data) => {
//   //   openSnackbar({
//   //     open: true,
//   //     message: 'Submit Success',
//   //     variant: 'alert',
//   //     alert: {
//   //       color: 'success'
//   //     }
//   //   });
//   //   console.log(data);
//   // };
//   // const onSubmit = async (formData) => {
//   //   try {
//   //     const response = await axios.post('http://localhost:8000/users', formData);
//   //     console.log(response.data); // Handle successful form submission response
//   //     reset(); // Optionally reset the form after successful submission
//   //   } catch (error) {
//   //     console.error('Error submitting form:', error);
//   //   }
//   // };


//   const { control, handleSubmit, formState: { errors }, reset } = useForm();
//   const [rows, setRows] = useState([]);

//   // useEffect(() => {
//   //   if (selectedRow) {
//   //     // Set form default values when selectedRow changes
//   //     reset(selectedRow);
//   //   }
//   // }, [selectedRow, reset]);
//     useEffect(() => {
//       if (selectedRow) {
//         reset(selectedRow);
//       } else {
//         reset({});
//       }
//     }, [selectedRow, reset]);

    
    


//     // const onSubmit = async (formData) => {
//     //   try {
//     //     let response;
//     //     if (selectedRow) {
//     //       // Edit mode
//     //       response = await axios.put(`http://localhost:8000/edit_graduate/${selectedRow.id}`, formData);
//     //     } else {
//     //       // Add mode
//     //       response = await axios.post('http://localhost:8000/users', formData);
//     //     }
//     //     onUpdate(response.data);
//     //     reset(); // Reset the form after submission
//     //     handleClose(); // Close dialog on success
//     //   } catch (error) {
//     //     console.error('Error submitting form:', error);
//     //   }
//     // };

//     // newone
//   // const onSubmit = async (formData) => {
//   //   try {
//   //     if (selectedRow) {
//   //       // Edit mode
//   //       await axios.put(`http://localhost:8000/edit_graduate/${selectedRow.id}`, formData);
//   //     } else {
//   //       // Add mode
//   //       await axios.post('http://localhost:8000/users', formData);
//   //     }
//   //     // await fetchData(); 
//   //     onUpdate(response.data);
//   //     handleClose(); // Close dialog on success
//   //   } catch (error) {
//   //     console.error('Error submitting form:', error);
//   //   }
//   // };
//   // const onSubmit = async (formData) => {
//   //   try {
//   //     if (selectedRow) {
//   //       // Edit mode
//   //       await axios.put(`http://localhost:8000/edit_graduate/${selectedRow.id}`, formData);
//   //     } else {
//   //       // Add mode
//   //       await axios.post('http://localhost:8000/users', formData);
//   //     }
      
//   //     // Fetch updated data after successful submission
//   //     await fetchData(); 
      
//   //     onUpdate(rows); // Update parent component with the new data
//   //     handleClose(); // Close dialog on success
//   //   } catch (error) {
//   //     console.error('Error submitting form:', error);
//   //   }
//   // };
//   const onSubmit = async (formData) => {
//     try {
//       let config = {
//         method: 'post',
//         url: 'http://localhost:8000/users',
//         headers: {
//           'Content-Type': 'application/json'
//         },
//         data: formData
//       };

//       if (selectedRow) {
//         // Edit mode
//         config.method = 'put';
//         config.url = `http://localhost:8000/edit_graduate/${selectedRow.id}`;
//       }

//       // Perform the Axios request
//       await axios(config).then((response)=>{
//       fetchData(); 
//       }).catch((error)=>{

//       });
      
//       // Fetch updated data after successful submission
      
      
//       onUpdate(rows); // Update parent component with the new data
//       handleClose(); // Close dialog on success
//     } catch (error) {
//       console.error('Error submitting form:', error);
//     }
//   };




//   // const onSubmit = async (formData) => {
//   //   try {
//   //     let response;
//   //     if (selectedRow) {
//   //       // Edit mode
//   //       response = await axios.put(`http://localhost:8000/edit_graduate/${selectedRow.id}`, formData);
//   //     } else {
//   //       // Add mode
//   //       response = await axios.post('http://localhost:8000/users', formData);
//   //     }
  
//   //     openSnackbar({
//   //       open: true,
//   //       message: 'Submit Success',
//   //       variant: 'alert',
//   //       alert: {
//   //         color: 'success'
//   //       }
//   //     });
  
//   //     onUpdate(response.data);
//   //     handleClose(); // Close dialog on success
//   //   } catch (error) {
//   //     openSnackbar({
//   //       open: true,
//   //       message: 'Submit Failed',
//   //       variant: 'alert',
//   //       alert: {
//   //         color: 'error'
//   //       }
//   //     });
//   //     console.error('Error submitting form:', error);
//   //   }
//   // };

  
  
  

//   // const onSubmit = async (formData) => {
//   //   try {
//   //     let response;
//   //     if (selectedRow) {
//   //       response = await axios.put(`http://localhost:8000/edit_graduate/${selectedRow.id}`, formData);
//   //     } else {
//   //       response = await axios.post('http://localhost:8000/users', formData);
//   //     }
//   //     onUpdate(response.data); // Update the parent component state
//   //     handleClose(); // Close dialog on success
//   //   } catch (error) {
//   //     console.error('Error submitting form:', error);
//   //   }
//   // };

//   const states = [
//     "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", 
//     "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", 
//     "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", 
//     "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", 
//     "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", 
//     "Uttar Pradesh", "Uttarakhand", "West Bengal", 
//     "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", 
//     "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir"
//   ];


//   const technologies = [
//     'Python', 'Java', 'Angular', 'NodeJs', 'Reactjs', 'Android', 'IOS Native',
//     'Manual Testing', 'Automation Testing', 'AWS DevOps', 'CloudOps Engineer',
//     'WordPress elementor', 'PHP Laravel', 'PHP Codigniter', '.Net', 'Flutter', 'SQL',
//     'UI/UX', 'RPA-AA', 'Block Chain', 'DBA', 'Data Analyst', 'Golang', 'Business Analyst',
//     'Vuejs', 'Power BI', 'Appium', 'AWS Data Engineer', 'Big data', 'ETL', 'Cucumber',
//     'Content developer', 'Data Engineer with GCP', 'Data Scientist', 'Artificial Intelligence/Machine Learning',
//     'Delphi', 'Digital Marketing', 'Drupal', 'IOT', 'Java with ReactJs', 'Java with Angular',
//     'Java with AWS', 'Kafka', 'Magento', 'Mern stack', 'Mean stack'
//   ];
//   // const onSubmit = (data) => {
//   //   openSnackbar({
//   //     open: true,
//   //     message: 'Submit Success',
//   //     variant: 'alert',
//   //     alert: {
//   //       color: 'success'
//   //     }
//   //   });
//   //   console.log(data);
//   // };

//   const validateCurrentCTC = (value) => {
//     if (!value) return 'Current CTC is required';
//     if (isNaN(value)) return 'Please enter a valid number';
//     if (parseFloat(value) > 10000000) return 'Current CTC should not exceed 1 crore';
//     return true;
//   };


  
//   const validateEmail = (value) => {
//     if (!value) {
//       return 'Email is required';
//     }
//     // Regular expression for validating an email address
//     if (!/\S+@\S+\.\S+/.test(value)) {
//       return 'Invalid email address';
//     }
//     return true;
//   };

//   const validateYear = (year) => {
//     const yearNumber = parseInt(year, 10);
//     if (!year || year.length !== 4 || isNaN(yearNumber)) {
//       return 'Year must be a 4-digit number';
//     }
//     if (yearNumber < 2001 || yearNumber > currentYear) {
//       return `Year must be between 2001 and ${currentYear}`;
//     }
//     return true;
//   };


//   return (
//     <React.Fragment>
     
//       <Dialog
//         open={open}
//         onClose={handleClose}
//         aria-labelledby="alert-dialog-title"
//         aria-describedby="alert-dialog-description"
//       >
//         <DialogTitle id="alert-dialog-title">
//           Add Graduate Form
//         </DialogTitle>
//         <DialogContent>
//         <Grid container spacing={4}>
//           {/* Candidate Name */}
//           <Grid item xs={12} sm={12}>
//         <FormControl fullWidth>
//           <Controller
//             name="candidateName"
//             control={control}          
//             rules={{
//               required: 'Candidate Name is required',
//               pattern: {
//                 value: /^[a-zA-Z\s]+$/,
//                 message: 'Candidate Name should only contain alphabetic characters and spaces'
//               }
//             }}
//             render={({ field }) => (
//               <NameTextField
                
//                 {...field}
//                 label="Candidate Name*"
//                 type='text'
//                 InputLabelProps={{ shrink: true }}
//                 size="small"
//                 placeholder="Enter candidate name"
//                 error={Boolean(errors.candidateName)}
//                 helperText={errors.candidateName?.message}
//               />
//             )}
//           />
//         </FormControl>
//       </Grid>
          
//           {/* Contact Number */}
//           <Grid item xs={12} sm={12}>
//             <FormControl fullWidth>
//               <Controller
//                 name="contactNo"
//                 control={control}
//                 rules={{
//                   required: 'Contact Number is required',
//                   pattern: {
//                     value: /^[0-9]{10,20}$/,
//                     message: 'Contact Number must be between 10 and 20 digits'
//                   }
//                 }}
//                 render={({ field, fieldState: { error } }) => (
//                   <TextField
//                     {...field}
//                     label="Contact Number"
//                     type="text"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Enter contact number"
//                     error={Boolean(error)}
//                     helperText={error ? error.message : ''}
//                     inputProps={{
//                       inputMode: 'numeric',
//                       pattern: '[0-9]*',
//                     }}
//                     onChange={(e) => {
//                       if (e.target.value.length <= 10) {
//                         field.onChange(e);
//                       }
//                     }}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Email */}
//           <Grid item xs={12} sm={12}>
//       <FormControl fullWidth>
//         <Controller
//           name="email"
//           control={control}
//           rules={{ validate: validateEmail }}
//           render={({ field }) => (
//             <TextField
//               {...field}
//               label="Email"
//               type="email"
//               InputLabelProps={{ shrink: true }}
//               size="small"
//               placeholder="Enter email address"
//               error={Boolean(errors.email)}
//               helperText={errors.email?.message}
//               inputProps={{
//                 maxLength: 50 // Example: Limiting to a maximum of 50 characters
//               }}
//             />
//           )}
//         />
//       </FormControl>
//     </Grid>


//     {/* Current location */}
//       <Grid item xs={12} sm={6}>
//       <FormControl fullWidth>
//         <Controller
//           name= "currentLocations"
//           control={control}
//           rules={{ required: 'Preferred Locations are required' }}
//           render={({ field }) => (
//             <TextField
//               {...field}
//               label="Current Locations"
//               InputLabelProps={{ shrink: true }}
//               size="small"
//               placeholder="Enter Current locations"
//               error={Boolean(errors. currentLocations)}
//               helperText={errors. currentLocations?.message}
//             />
//           )}
//         />
//       </FormControl>
//     </Grid>


//     <Grid item xs={12} sm={6}>
//       <FormControl fullWidth>
//         <InputLabel id="state-label" shrink>
//           Which state are you residing in?
//         </InputLabel>
//         <Controller
//           name="residingState"
//           control={control}
//           rules={{ required: 'This field is required' }}
//           render={({ field }) => (
//             <Select
//               {...field}
//               labelId="state-label"
//               label="Which state are you residing in?"
//               size="small"
//               fullWidth
//               error={Boolean(errors.residingState)}
//             >
//               {states.map((state) => (
//                 <MenuItem key={state} value={state}>{state}</MenuItem>
//               ))}
//             </Select>
//           )}
//         />
//         {errors.residingState && (
//           <FormHelperText error>
//             {errors.residingState.message}
//           </FormHelperText>
//         )}
//       </FormControl>
//     </Grid>




//       <Grid item xs={12} sm={12}>
//         <FormControl fullWidth>
//           <InputLabel id="internship-agreement-label" shrink>
//             Do you agree with the unpaid internship of initial 3 months during probation period of 9 months?
//           </InputLabel>
//           <Controller
//             name="internshipAgreement"
//             control={control}
//             rules={{ required: 'This field is required' }}
//             render={({ field }) => (
//               <Select
//                 {...field}
//                 labelId="internship-agreement-label"
//                 label="Do you agree with the unpaid internship of initial 3 months during probation period of 9 months?"
//                 size="small"
//                 fullWidth
//                 error={Boolean(errors.internshipAgreement)}
//               >
//                 <MenuItem value="yes">Yes</MenuItem>
//                 <MenuItem value="no">No</MenuItem>
//               </Select>
//             )}
//           />
//           {errors.internshipAgreement && (
//             <FormHelperText error>
//               {errors.internshipAgreement.message}
//             </FormHelperText>
//           )}
//         </FormControl>
//         </Grid>


//           {/* {curently working} */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="currently-working-label" shrink>
//               Currently Working?
//             </InputLabel>
//             <Controller
//               name="currentlyWorking"
//               control={control}
//               rules={{ required: 'This field is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="currently-working-label"
//                   label="Currently Working?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.currentlyWorking)}
//                 >
//                   <MenuItem value="yes">Yes</MenuItem>
//                   <MenuItem value="no">No</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.currentlyWorking && (
//               <FormHelperText error>
//                 {errors.currentlyWorking.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>



//         <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="personal-projects-label" shrink>
//               Did you do any personal projects on the relevant tech stack?
//             </InputLabel>
//             <Controller
//               name="personalProjects"
//               control={control}
//               rules={{ required: 'This field is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="personal-projects-label"
//                   label="Did you do any personal projects on the relevant tech stack?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.personalProjects)}
//                 >
//                   <MenuItem value="yes">Yes</MenuItem>
//                   <MenuItem value="no">No</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.personalProjects && (
//               <FormHelperText error>
//                 {errors.personalProjects.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>



//         <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="technology-interest-label" shrink>
//               Which technology are you interested in and looking for a job?
//             </InputLabel>
//             <Controller
//               name="technologyInterest"
//               control={control}
//               rules={{ required: 'This field is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="technology-interest-label"
//                   label="Which technology are you interested in and looking for a job?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.technologyInterest)}
//                 >
//                   {technologies.map((tech) => (
//                     <MenuItem key={tech} value={tech}>{tech}</MenuItem>
//                   ))}
//                 </Select>
//               )}
//             />
//             {errors.technologyInterest && (
//               <FormHelperText error>
//                 {errors.technologyInterest.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>

//         <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="currently-working-label" shrink>
//               If Currently Working, Name of the company?
//             </InputLabel>
//             <Controller
//               name="companyName"
//               control={control}
//               rules={{ required: 'Company name is required' }}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
//                   fullWidth
//                   size="small"
//                   // label="Company Name"
//                   error={Boolean(errors.companyName)}
//                   helperText={errors.companyName && errors.companyName.message}
//                 />
//               )}
//             />
//             {errors.companyName && (
//               <FormHelperText error>
//                 {errors.companyName.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>

//         {/* <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="currently-working-label" shrink>
//               If Currently Working, Total Experience?
//             </InputLabel>
//             <Controller
//               name="totalExperience"
//               control={control}
//               rules={{ required: 'Total experience is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="currently-working-label"
//                   label="If Currently Working, Total Experience?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.totalExperience)}
//                 >
//                   <MenuItem value="0-6 Months">0 - 6 </MenuItem>
//                   <MenuItem value="6-12 Months">6 - 12 </MenuItem>
//                   <MenuItem value="> 1 yr">&gt; 1</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.totalExperience && (
//               <FormHelperText error>
//                 {errors.totalExperience.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid> */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="currently-working-label" shrink>
//               If Currently Working, Total Experience?
//             </InputLabel>
//             <Controller
//               name="totalExperience"
//               control={control}
//               rules={{ required: 'Total experience is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="currently-working-label"
//                   label="If Currently Working, Total Experience?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.totalExperience)}
//                 >
//                   <MenuItem value={0}>0 - 6 Months</MenuItem>
//                   <MenuItem value={1}>6 - 12 Months</MenuItem>
//                   <MenuItem value={2}>More than 1 Year</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.totalExperience && (
//               <FormHelperText error>
//                 {errors.totalExperience.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>



//         <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="course-training-label" shrink>
//               Have you done any Course/Training in the relevant Technology?
//             </InputLabel>
//             <Controller
//               name="courseTraining"
//               control={control}
//               rules={{ required: 'Please select an option' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="course-training-label"
//                   label="Have you done any Course/Training in the relevant Technology?"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.courseTraining)}
//                 >
//                   <MenuItem value="Yes">Yes</MenuItem>
//                   <MenuItem value="No">No</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.courseTraining && (
//               <FormHelperText error>
//                 {errors.courseTraining.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>



//         {/* Training Institution Name */}
//         <Grid item xs={12} sm={6}>
//         <FormControl fullWidth>
//           <Controller
//             name="trainingInstitutionName"  // Updated field name
//             control={control}
//             rules={{ required: 'Training Institution Name is required' }}
//             render={({ field }) => (
//               <TextField
//                 {...field}
//                 label="Training Institution Name"  // Updated label
//                 InputLabelProps={{ shrink: true }}
//                 size="small"
//                 placeholder="Enter Training Institution Name"  // Updated placeholder
//                 error={Boolean(errors.trainingInstitutionName)}
//                 helperText={errors.trainingInstitutionName?.message}
//               />
//             )}
//           />
//         </FormControl>
//       </Grid>



//       <Grid item xs={12} sm={6}>
//       <FormControl fullWidth>
//       <Controller
//         name="keySkillsLearned"
//         control={control}
//         rules={{ required: 'Key skills Learned during Course/Training are required' }}
//         render={({ field }) => (
//           <TextField
//             {...field}
//             label="Key skills Learned during Course/Training"
//             InputLabelProps={{ shrink: true }}
//             size="small"
//             placeholder="Enter Key skills Learned during Course/Training"
//             error={Boolean(errors.keySkillsLearned)}
//             helperText={errors.keySkillsLearned?.message}
//           />
//         )}
//       />
//      </FormControl>
//     </Grid>


//     <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel id="duration-label" shrink>
//       Duration of Course/Training
//     </InputLabel>
//     <Controller
//       name="durationOfTraining"
//       control={control}
//       rules={{ required: 'Duration of Course/Training is required' }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="duration-label"
//           label="Duration of Course/Training"
//           size="small"
//           fullWidth
//           error={Boolean(errors.durationOfTraining)}
//         >
//           <MenuItem value="1 month">1 month</MenuItem>
//           <MenuItem value="2 months">2 months</MenuItem>
//           <MenuItem value="3 months">3 months</MenuItem>
//           <MenuItem value="4 months">4 months</MenuItem>
//           <MenuItem value="5 months">5 months</MenuItem>
//           <MenuItem value="6 months">6 months</MenuItem>
//           <MenuItem value="1 year">1 year</MenuItem>
//           <MenuItem value="Other">Other</MenuItem>
//         </Select>
//       )}
//     />
//     {errors.durationOfTraining && (
//       <FormHelperText error>
//         {errors.durationOfTraining.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid>

// <Grid item xs={12} sm={12}>
//   <FormControl fullWidth>
//     <Controller
//       name="schoolName10thCompleted"
//       control={control}
//       rules={{ required: 'Name of the School 10th class completed is required' }}
//       render={({ field }) => (
//         <TextField
//           {...field}
//           label="Name of the School 10th class completed"
//           InputLabelProps={{ shrink: true }}
//           size="small"
//           placeholder="Enter school name"
//           error={Boolean(errors.schoolName10thCompleted)}
//           helperText={errors.schoolName10thCompleted?.message}
//         />
//       )}
//     />
//   </FormControl>
// </Grid> 



// <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="passed-out-year">
//       Passed Out Year
//     </InputLabel>
//     <Controller
//       name="passedOutYear"
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="passed-out-year"
//           id="passed-out-year"
//           // inputProps={{ maxLength: 4 }}
//           error={Boolean(errors.passedOutYear)}
//           helperText={errors.passedOutYear?.message}
//           size='small'
//         >
//           {[...Array(22)].map((_, index) => {
//             const year = 2001 + index;
//             return (
//               <MenuItem key={year} value={year}>
//                 {year}
//               </MenuItem>
//             );
//           })}
//         </Select>
//       )}
//     />
//     {errors.passedOutYear && (
//       <FormHelperText error>
//         {errors.passedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid>




//           {/* Tenth Percentage */}
//           <Grid item xs={12} sm={6}>
//       <FormControl fullWidth error={Boolean(errors.tenthPercentage)}>
//         <InputLabel shrink>Tenth Percentage</InputLabel>
//         <Controller
//           name="tenthPercentage"
//           control={control}
//           rules={{ required: 'Tenth Percentage is required' }}
//           render={({ field }) => (
//             <Select
//               {...field}
//               label="Tenth Percentage"
//               size="small"
//               displayEmpty
//             >
//               <MenuItem value="" disabled>
//                 Select percentage
//               </MenuItem>
//               <MenuItem value=">60">{'<'}60%</MenuItem>
//               <MenuItem value=">70">{'>'}70%</MenuItem>
//               <MenuItem value=">80">{'>'}80%</MenuItem>
//               <MenuItem value=">90">{'>'}90%</MenuItem>
//             </Select>
//           )}
//         />
//         {errors.tenthPercentage && (
//           <FormHelperText>{errors.tenthPercentage.message}</FormHelperText>
//         )}
//       </FormControl>
//     </Grid>



//     <Grid item xs={12} sm={12}>
//     <FormControl fullWidth>
//       <Controller
//         name="cgpaName10thCompleted"
//         control={control}
//         rules={{ required: 'Name of the School 10th class completed is required' }}
//         render={({ field }) => (
//           <TextField
//             {...field}
//             label="If CGPA in 10th class Grade obtained?"
//             InputLabelProps={{ shrink: true }}
//             size="small"
//             placeholder="Enter school name"
//             error={Boolean(errors.cgpaName10thCompleted)}
//             helperText={errors.cgpaName10thCompleted?.message}
//           />
//         )}
//       />
//     </FormControl>
//   </Grid>



//         <Grid item xs={12} sm={6}>
//         <FormControl fullWidth>
//           <Controller
//             name="educationLevel"
//             control={control}
//             rules={{ required: 'This field is required' }}
//             render={({ field }) => (
//               <TextField
//                 {...field}
//                 label="Intermediate(12th Class) or Diploma?"
//                 select
//                 InputLabelProps={{ shrink: true }}
//                 size="small"
//                 placeholder="Select an option"
//                 error={Boolean(errors.educationLevel)}
//                 helperText={errors.educationLevel?.message}
//               >
//                 <MenuItem value="intermediate">Intermediate (12th Class)</MenuItem>
//                 <MenuItem value="diploma">Diploma</MenuItem>
//               </TextField>
//             )}
//           />
//         </FormControl>
//       </Grid>



//       <Grid item xs={12} sm={12}>
//       < FormControl fullWidth>
//         <Controller
//           name="collegeNameIntermediateCompleted"
//           control={control}
//           rules={{ required: 'Name of the College / Institution Intermediate(12th Class) or Diploma completed is required' }}
//           render={({ field }) => (
//             <TextField
//               {...field}
//               label="Name of the College / Institution Intermediate(12th Class) or Diploma completed?"
//               InputLabelProps={{ shrink: true }}
//               size="small"
//               placeholder="Enter college/institution name"
//               error={Boolean(errors.collegeNameIntermediateCompleted)}
//               helperText={errors.collegeNameIntermediateCompleted?.message}
//             />
//           )}
//         />
//       </FormControl>
//     </Grid>


//     <Grid item xs={12} sm={6}>
//     <FormControl fullWidth>
//     <InputLabel shrink htmlFor="intermediate-diploma-passed-out-year">
//       Intermediate(12th Class) or Diploma passed out Year?
//     </InputLabel>
//     <Controller
//       name="intermediateDiplomaPassedOutYear"
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="intermediate-diploma-passed-out-year"
//           id="intermediate-diploma-passed-out-year"
//           // inputProps={{ maxLength: 4 }}
//           error={Boolean(errors.intermediateDiplomaPassedOutYear)}
//           helperText={errors.intermediateDiplomaPassedOutYear?.message}
//           size='small'
//         >
//           {[...Array(24)].map((_, index) => {
//             const year = 2001 + index;
//             return (
//               <MenuItem key={year} value={year}>
//                 {year}
//               </MenuItem>
//             );
//           })}
//         </Select>
//       )}
//     />
//     {errors.intermediateDiplomaPassedOutYear && (
//       <FormHelperText error>
//         {errors.intermediateDiplomaPassedOutYear.message}
//       </FormHelperText>
//     )}
//     </FormControl>
//   </Grid>





//   <Grid item xs={12} sm={6}>
//         <FormControl fullWidth>
//           <InputLabel id="inter-diploma-percentage-label" shrink>
//             Intermediate or Diploma Percentage
//           </InputLabel>
//           <Controller
//             name="interDiplomaPercentage"
//             control={control}
//             rules={{ required: 'Percentage is required' }}
//             render={({ field }) => (
//               <Select
//                 {...field}
//                 labelId="inter-diploma-percentage-label"
//                 label="Intermediate or Diploma Percentage"
//                 size="small"
//                 fullWidth
//                 error={Boolean(errors.interDiplomaPercentage)}
//               >
//                 <MenuItem value="<60">{'<'}60%</MenuItem>
//                 <MenuItem value=">70">{'>'}70%</MenuItem>
//                 <MenuItem value=">80">{'>'}80%</MenuItem>
//                 <MenuItem value=">90">{'>'}90%</MenuItem>
//               </Select>
//             )}
//           />
//           {errors.interDiplomaPercentage && (
//             <FormHelperText error>
//               {errors.interDiplomaPercentage.message}
//             </FormHelperText>
//           )}
//         </FormControl>
//       </Grid>


//       <Grid item xs={12} sm={6}>
//     <FormControl fullWidth>
//     <Controller
//       name="cgpa12thOrDiploma"
//       control={control}
//       rules={{ required: 'CGPA in Intermediate(12th Class) or Diploma Grade is required' }}
//       render={({ field }) => (
//         <TextField
//           {...field}
//           label="If CGPA in Intermediate(12th Class) or Diploma Grade obtained?"
//           InputLabelProps={{ shrink: true }}
//           size="small"
//           placeholder="Enter CGPA or grade"
//           error={Boolean(errors.cgpa12thOrDiploma)}
//           helperText={errors.cgpa12thOrDiploma?.message}
//         />
//       )}
//     />
//   </FormControl>
// </Grid>



// <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="graduationStream"
//                 control={control}
//                 rules={{ required: 'This field is required' }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Graduation Stream?"
//                     select
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Select an option"
//                     error={Boolean(errors.graduationStream)}
//                     helperText={errors.graduationStream?.message}
//                   >
//                     <MenuItem value="btech">B.Tech</MenuItem>
//                     <MenuItem value="bcom">B.Com</MenuItem>
//                     <MenuItem value="ba">B.A</MenuItem>
//                     <MenuItem value="bsc">B.Sc</MenuItem>
//                     <MenuItem value="other">Other</MenuItem>
//                   </TextField>
//                 )}
//               />
//             </FormControl>
//           </Grid>


//          {/* Grad Branch */}
//          <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="gradBranch"
//                 control={control}  
//                 rules={{ required: 'Graduation Branch is required' }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="In which branch, Graduation was completed ? "
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Ex : Civil/Mechanical/CSE/EEE/ECE/Bsc(MPC)/ Bsc(MEC)........*"
//                     error={Boolean(errors.gradBranch)}
//                     helperText={errors.gradBranch?.message}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>




//            {/* Grad University */}
//            <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="gradUniversity"
//                 control={control}
//                 rules={{ required: 'Graduation University is required' }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Graduation University"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Enter graduation university"
//                     error={Boolean(errors.gradUniversity)}
//                     helperText={errors.gradUniversity?.message}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>



//           {/* Grad Passed Out Year */}
//           {/* <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="passed-out-year">
//       Graduation Passed Out Year
//     </InputLabel>
//     <Controller
//       name="gradpassedOutYear"
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="gradpassed-out-year"
//           id="gradpassed-out-year"
     
//           error={Boolean(errors.gradpassedOutYear)}
//           helperText={errors.gradpassedOutYear?.message}
//           size='small'
//         >
//           {[...Array(24)].map((_, index) => {
//             const year = 2001 + index;
//             return (
//               <MenuItem key={year} value={year}>
//                 {year}
//               </MenuItem>
//             );
//           })}
//         </Select>
//       )}
//     />
//     {errors.passedOutYear && (
//       <FormHelperText error>
//         {errors.passedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid> */}

// {/* check with s */}
// {/* <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="gradpassed-out-year">
//       Graduation Passed Out Year
//     </InputLabel>
//     <Controller
//       name="gradpassedOutYear"  // Ensure this matches your backend model field name
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // Uncomment and implement validate function if needed
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="gradpassed-out-year"
//           id="gradpassed-out-year"
//           error={Boolean(errors.gradpassedOutYear)}
//           helperText={errors.gradpassedOutYear?.message}
//           size="small"
//         >
//           {[...Array(24)].map((_, index) => {
//             const year = 2001 + index;
//             return (
//               <MenuItem key={year} value={year}>
//                 {year}
//               </MenuItem>
//             );
//           })}
//         </Select>
//       )}
//     />
//     {errors.gradpassedOutYear && (
//       <FormHelperText error>
//         {errors.gradpassedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid> */}
// {/* <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="gradpassed-out-year">
//       Graduation Passed Out Year
//     </InputLabel>
//     <Controller
//       name="gradpassedOutYear"  // Ensure this matches your backend model field name
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // Uncomment and implement validate function if needed
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <TextField
//           {...field}
//           labelId="gradpassed-out-year"
//           id="gradpassed-out-year"
//           error={Boolean(errors.gradpassedOutYear)}
//           helperText={errors.gradpassedOutYear?.message}
//           size="small"
//         />
//       )}
//     />
//     {errors.gradpassedOutYear && (
//       <FormHelperText error>
//         {errors.gradpassedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid> */}



// {/* <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="gradpassed-out-year">
//       Graduation Passed Out Year
//     </InputLabel>
//     <Controller
//       name="gradpassedOutYear"  // Ensure this matches your backend model field name
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // Uncomment and implement validate function if needed
//         // validate: validateYear
//       }}
//       render={({ field: { onChange, value, ...field } }) => (
//         <TextField
//           {...field}
//           labelId="gradpassed-out-year"
//           id="gradpassed-out-year"
//           error={Boolean(errors.gradpassedOutYear)}
//           helperText={errors.gradpassedOutYear?.message}
//           size="small"
//           value={value || ''}
//           onChange={(e) => onChange(parseInt(e.target.value, 10) || '')}
//         />
//       )}
//     />
//     {errors.gradpassedOutYear && (
//       <FormHelperText error>
//         {errors.gradpassedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid> */}


// <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel shrink htmlFor="grad-passed-out-year">
//       Graduation Passed Out Year
//     </InputLabel>
//     <Controller
//       name="gradPassedOutYear"
//       control={control}
//       rules={{ 
//         required: 'Passed Out Year is required',
//         // validate: validateYear
//       }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="grad-passed-out-year"
//           id="grad-passed-out-year"
//           // inputProps={{ maxLength: 4 }}
//           error={Boolean(errors.gradPassedOutYear)}
//           helperText={errors.gradPassedOutYear?.message}
//           size='small'
//         >
//           {[...Array(24)].map((_, index) => {
//             const year = 2001 + index;
//             return (
//               <MenuItem key={year} value={year}>
//                 {year}
//               </MenuItem>
//             );
//           })}
//         </Select>
//       )}
//     />
//     {errors.gradPassedOutYear && (
//       <FormHelperText error>
//         {errors.gradPassedOutYear.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid>



//   {/* Grad Percentage */}
//   <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="graduate-percentage-label" shrink>
//               Graduate Percentage
//             </InputLabel>
//             <Controller
//               name="gradPercentage"
//               control={control}
//               rules={{ required: 'Percentage is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="graduate-percentage-label"
//                   label="Graduate Percentage"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.gradPercentage)}
//                 >
//                   <MenuItem value=">60">{'<'}60%</MenuItem>
//                   <MenuItem value=">70">{'>'}70%</MenuItem>
//                   <MenuItem value=">80">{'>'}80%</MenuItem>
//                   <MenuItem value=">90">{'>'}90%</MenuItem>
//                 </Select>
//               )}
//             />
//             {errors.gradPercentage && (
//               <FormHelperText error>
//                 {errors.gradPercentage.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>



//         <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <InputLabel id="post-grad-stream-label" shrink>
//       Which Post Graduation stream completed?
//     </InputLabel>
//     <Controller
//       name="postGradStream"
//       control={control}
//       rules={{ required: 'This field is required' }}
//       render={({ field }) => (
//         <Select
//           {...field}
//           labelId="post-grad-stream-label"
//           label="Which Post Graduation stream completed?"
//           size="small"
//           fullWidth
//           error={Boolean(errors.postGradStream)}
//         >
//           <MenuItem value="not_applicable">Not Applicable</MenuItem>
//           <MenuItem value="mtech">M Tech</MenuItem>
//           <MenuItem value="msc">MSc</MenuItem>
//           <MenuItem value="mca">MCA</MenuItem>
//         </Select>
//       )}
//     />
//     {errors.postGradStream && (
//       <FormHelperText error>
//         {errors.postGradStream.message}
//       </FormHelperText>
//     )}
//   </FormControl>
// </Grid>


//  {/* Post Grad Specialization */}
//  <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="postGradSpecialization"
//                 control={control}
//                 rules={{ required: 'Specialization is required' }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Post Graduation Specialization"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Ex: Civil/Mechanical/CSE/EEE/ECE/Msc(MPC)/ Msc(MEC)........"
//                     error={Boolean(errors.postGradSpecialization)}
//                     helperText={errors.postGradSpecialization?.message}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>



//            {/* Post Grad University */}
//            <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="postGradUniversity"
//                 control={control}
//                 rules={{ required: 'University is required' }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Post Graduation University"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Enter university"
//                     error={Boolean(errors.postGradUniversity)}
//                     helperText={errors.postGradUniversity?.message}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>



//                  <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <InputLabel id="post-grad-passed-out-year-label" shrink>
//               Post Graduation Passed Out Year
//             </InputLabel>
//             <Controller
//               name="postGradPassedOutYear"
//               control={control}
//               rules={{ required: 'Passed Out Year is required' }}
//               render={({ field }) => (
//                 <Select
//                   {...field}
//                   labelId="post-grad-passed-out-year-label"
//                   label="Post Graduation Passed Out Year"
//                   size="small"
//                   fullWidth
//                   error={Boolean(errors.postGradPassedOutYear)}
//                 >
//                   {[...Array(35)].map((_, index) => {
//                     const year = 1990 + index;
//                     return (
//                       <MenuItem key={year} value={year}>
//                         {year}
//                       </MenuItem>
//                     );
//                   })}
//                 </Select>
//               )}
//             />
//             {errors.postGradPassedOutYear && (
//               <FormHelperText error>
//                 {errors.postGradPassedOutYear.message}
//               </FormHelperText>
//             )}
//           </FormControl>
//         </Grid>


//         <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="post-grad-percentage-label" shrink>
//                 Post Graduation Percentage
//               </InputLabel>
//               <Controller
//                 name="postGradPercentage"
//                 control={control}
//                 rules={{ required: 'Percentage is required' }}
//                 render={({ field }) => (
//                   <Select
//                     {...field}
//                     labelId="post-grad-percentage-label"
//                     label="Post Graduation Percentage"
//                     size="small"
//                     fullWidth
//                     error={Boolean(errors.postGradPercentage)}
//                   >
//                     <MenuItem value="<60">{'<'}60%</MenuItem>
//                     <MenuItem value=">70">{'>'}70%</MenuItem>
//                     <MenuItem value=">80">{'>'}80%</MenuItem>
//                     <MenuItem value=">90">{'>'}90%</MenuItem>
//                   </Select>
//                 )}
//               />
//               {errors.postGradPercentage && (
//                 <FormHelperText error>
//                   {errors.postGradPercentage.message}
//                 </FormHelperText>
//               )}
//             </FormControl>
//           </Grid>



//             <Grid item xs={12} sm={6}>
//     <FormControl fullWidth>
//       <InputLabel shrink htmlFor="pay-expectations">
//         Pay expectations. Please mention as (ex: 2 LPA / 3 LPA / 4 LPA / 5 LPA)
//       </InputLabel>
//       <Controller
//         name="payExpectations"
//         control={control}
//         rules={{ 
//           required: 'Pay expectations are required',
//           // validate: validatePay
//         }}
//         render={({ field }) => (
//           <Select
//             {...field}
//             labelId="pay-expectations"
//             id="pay-expectations"
//             error={Boolean(errors.payExpectations)}
//             helperText={errors.payExpectations?.message}
//             size='small'
//           >
//             {[...Array(4)].map((_, index) => {
//               const pay = `${index + 2} LPA`;
//               return (
//                 <MenuItem key={pay} value={pay}>
//                   {pay}
//                 </MenuItem>
//               );
//             })}
//           </Select>
//         )}
//       />
//       {errors.payExpectations && (
//         <FormHelperText error>
//           {errors.payExpectations.message}
//         </FormHelperText>
//       )}
//     </FormControl>
//   </Grid>


//     {/* Interested In Chidhagni? */}
//       <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <Controller
//               name="interestedInChidhagni"
//               control={control}
//               rules={{ required: 'This field is required' }}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
//                   label="Interested in joining startup ?"
//                   select
//                   InputLabelProps={{ shrink: true }}
//                   size="small"
//                   placeholder="Select an option"
//                   error={Boolean(errors.interestedInChidhagni)}
//                   helperText={errors.interestedInChidhagni?.message}
//                 >
//                   <MenuItem value="yes">Yes</MenuItem>
//                   <MenuItem value="no">No</MenuItem>
//                 </TextField>
//               )}
//             />
//           </FormControl>
//         </Grid>







//         <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <Controller
//       name="campusOffer"
//       control={control}
//       rules={{ required: 'This field is required' }}
//       render={({ field }) => (
//         <TextField
//           {...field}
//           label="Do You have any Campus Offer?"
//           select
//           InputLabelProps={{ shrink: true }}
//           size="small"
//           placeholder="Select an option"
//           error={Boolean(errors.campusOffer)}
//           helperText={errors.campusOffer?.message}
//         >
//           <MenuItem value="yes">Yes</MenuItem>
//           <MenuItem value="no">No</MenuItem>
//         </TextField>
//       )}
//     />
//   </FormControl>
// </Grid>
// </Grid>
//         </DialogContent>
//         <DialogActions>
//         <Button onClick={handleClose}>Cancel</Button>
//         <Button onClick={handleSubmit(onSubmit)}>
//           {selectedRow ? 'Update' : 'Submit'}
//         </Button>

//         </DialogActions>
//       </Dialog>
//     </React.Fragment>
//   );
// }
import React, { useEffect } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, FormControl, TextField } from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import axios from 'axios';

export default function FormDialog({ open, handleClose, selectedRow, onUpdate, fetchData, isEditMode }) {
  const { control, handleSubmit, reset, formState: { errors } } = useForm({
    defaultValues: {
      client_name: "",
      contacts_number: "",
      email_id: "",
      website: "",
      industry: "",
      country: "",
      saret: "",
      city: "",
      business_unit: "",
      category: "",
      postal: "",
      created_by: "",
      created_on: "",
    },
  });

  // Log selectedRow to check if it's passed correctly
  console.log('Selected Row Prop:', selectedRow);

  // Reset the form when the selectedRow or open changes
  useEffect(() => {
    if (isEditMode && selectedRow && open) {
      console.log('Selected Row Inside useEffect:', selectedRow); // Log inside useEffect
      reset({
        client_name: selectedRow?.client_name || "",
        contacts_number: selectedRow.contacts_number || "",
        email_id: selectedRow.email_id || "",
        website: selectedRow.website || "",
        industry: selectedRow.industry || "",
        country: selectedRow.country || "",
        saret: selectedRow.saret || "",
        city: selectedRow.city || "",
        business_unit: selectedRow.business_unit || "",
        category: selectedRow.category || "",
        postal: selectedRow.postal || "",
        created_by: selectedRow.created_by || "",
        created_on: selectedRow.created_on || "",
      });
    } else if (!isEditMode && open) {
      // Reset the form to default values for Add mode
      reset({
        client_name: "",
        contacts_number: "",
        email_id: "",
        website: "",
        industry: "",
        country: "",
        saret: "",
        city: "",
        business_unit: "",
        category: "",
        postal: "",
        created_by: "",
        created_on: "",
      });
    }
  }, [selectedRow, open, isEditMode, reset]);

  // Handle form submission
  const onSubmit = async (formData) => {
    console.log(formData);
    const method = isEditMode ? 'put' : 'post';
    const url = isEditMode 
      ? `http://localhost:8080/clients/${selectedRow.id}` 
      : 'http://localhost:8080/clients'; // Add endpoint for new client

    try {
      const config = {
        method: method,
        url: url,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
          'Content-Type': 'application/json'
        },
        data: formData
      };

      const response = await axios(config);
      if (response.status === 200 || response.status === 201) {
        fetchData(); // Re-fetch the updated list of clients
        onUpdate(response.data); // Update the state in the parent component
        handleCloseDialog(); // Close the dialog
      } else {
        console.error('Failed to submit the form');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleCloseDialog = () => {
    reset({}); // Reset the form when closing the dialog
    handleClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleCloseDialog}
      aria-labelledby="form-dialog-title"
    >
      <DialogTitle id="form-dialog-title">
        {isEditMode ? 'Edit Client Details' : 'Add New Client'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={4}>
          {/* Client Name */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="client_name"
                control={control}
                rules={{ required: 'Client Name is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Client Name"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter Client Name"
                    disabled={isEditMode} // Disable field for edit mode, enable for add mode
                    error={Boolean(errors.client_name)}
                    helperText={errors.client_name?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Contact Number */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="contacts_number"
                control={control}
                rules={{
                  required: 'Contact Number is required',
                  pattern: {
                    value: /^[0-9]{10,20}$/,
                    message: 'Contact Number must be between 10 and 20 digits'
                  }
                }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    label="Contacts Number"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter contact number"
                    error={Boolean(error)}
                    helperText={error ? error.message : ''}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Email ID */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="email_id"
                control={control}
                rules={{
                  required: 'Email ID is required',
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Enter a valid email address'
                  }
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Email ID"
                    type="email"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter email address"
                    error={Boolean(errors.email_id)}
                    helperText={errors.email_id?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Website */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="website"
                control={control}
                rules={{ required: 'Website is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Website"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter website"
                    error={Boolean(errors.website)}
                    helperText={errors.website?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Industry */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller 
                name="industry"
                control={control}
                rules={{ required: 'Industry is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Industry"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter industry"
                    error={Boolean(errors.industry)}
                    helperText={errors.industry?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Country */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="country"
                control={control}
                rules={{ required: 'Country is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Country"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter country"
                    error={Boolean(errors.country)}
                    helperText={errors.country?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Saret */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="saret"
                control={control}
                rules={{ required: 'Saret is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="State"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter State"
                    error={Boolean(errors.saret)}
                    helperText={errors.saret?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* City */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="city"
                control={control}
                rules={{ required: 'City is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="City"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter city"
                    error={Boolean(errors.city)}
                    helperText={errors.city?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Business Unit */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="business_unit"
                control={control}
                rules={{ required: 'Business Unit is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Business Unit"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter Business Unit"
                    error={Boolean(errors.business_unit)}
                    helperText={errors.business_unit?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Category */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="category"
                control={control}
                rules={{ required: 'Category is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Category"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter category"
                    error={Boolean(errors.category)}
                    helperText={errors.category?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Postal */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="postal"
                control={control}
                rules={{ required: 'Postal is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Postal"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter postal code"
                    error={Boolean(errors.postal)}
                    helperText={errors.postal?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Created By */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="created_by"
                control={control}
                rules={{ required: 'Created By is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Created By"
                    type="text"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter creator's name"
                    error={Boolean(errors.created_by)}
                    helperText={errors.created_by?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Created On */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="created_on"
                control={control}
                rules={{ required: 'Created On is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Created On"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter creation date"
                    error={Boolean(errors.created_on)}
                    helperText={errors.created_on?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Cancel</Button>
        <Button onClick={handleSubmit(onSubmit)}>
          {isEditMode ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}