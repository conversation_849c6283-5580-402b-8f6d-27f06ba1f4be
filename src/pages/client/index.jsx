// // import React, { useState } from 'react';
// // // material-ui
// // import Table from '@mui/material/Table';
// // import TableBody from '@mui/material/TableBody';
// // import TableCell from '@mui/material/TableCell';
// // import TableContainer from '@mui/material/TableContainer';
// // import TableHead from '@mui/material/TableHead';
// // import TableRow from '@mui/material/TableRow';
// // import IconButton from '@mui/material/IconButton';
// // import EditIcon from '@mui/icons-material/Edit';
// // import DeleteIcon from '@mui/icons-material/Delete';
// // import Dialog from '@mui/material/Dialog';
// // import DialogActions from '@mui/material/DialogActions';
// // import DialogContent from '@mui/material/DialogContent';
// // import DialogTitle from '@mui/material/DialogTitle';
// // import TextField from '@mui/material/TextField';
// // import Button from '@mui/material/Button';

// // // project-imports
// // import MainCard from 'components/MainCard';
// // import { CSVExport } from 'components/third-party/react-table';

// // // table data
// // function createData(name, calories, fat, carbs, protein) {
// //   return { name, calories, fat, carbs, protein };
// // }

// // const initialRows = [
// //   createData('Frozen yoghurt', 159, 6.0, 24, 4.0),
// //   createData('Ice cream sandwich', 237, 9.0, 37, 4.3),
// //   createData('Eclair', 262, 16.0, 24, 6.0),
// //   createData('Cupcake', 305, 3.7, 67, 4.3),
// //   createData('Gingerbread', 356, 16.0, 49, 3.9)
// // ];

// // export const header = [
// //   { label: 'Dessert (100g serving)', key: 'name' },
// //   { label: 'Calories (g)', key: 'calories' },
// //   { label: 'Fat (g)', key: 'fat' },
// //   { label: 'Carbs (g)', key: 'carbs' },
// //   { label: 'Protein (g)', key: 'protein' }
// // ];

// // // ==============================|| MUI TABLE - BASIC ||============================== //

// // export default function TableBasic() {
// //   const [rows, setRows] = useState(initialRows);
// //   const [open, setOpen] = useState(false);
// //   const [currentRow, setCurrentRow] = useState(null);

// //   const handleEdit = (row) => {
// //     setCurrentRow({ ...row });
// //     setOpen(true);
// //   };

// //   const handleDelete = (row) => {
// //     setRows(rows.filter(r => r.name !== row.name));
// //   };

// //   const handleClose = () => {
// //     setOpen(false);
// //     setCurrentRow(null);
// //   };

// //   const handleSave = () => {
// //     setRows(rows.map(row => (row.name === currentRow.name ? currentRow : row)));
// //     handleClose();
// //   };

// //   const handleChange = (e) => {
// //     const { name, value } = e.target;
// //     setCurrentRow({ ...currentRow, [name]: value });
// //   };

// //   return (
// //     <MainCard content={false} title="Basic Table" secondary={<CSVExport data={rows} headers={header} filename="basic-table-data.csv" />}>
// //       {/* table */}
// //       <TableContainer>
// //         <Table sx={{ minWidth: 350 }} aria-label="simple table">
// //           <TableHead>
// //             <TableRow>
// //               <TableCell sx={{ pl: 3 }}>Dessert (100g serving)</TableCell>
// //               <TableCell align="right">Calories</TableCell>
// //               <TableCell align="right">Fat&nbsp;(g)</TableCell>
// //               <TableCell align="right">Carbs&nbsp;(g)</TableCell>
// //               <TableCell align="right">Protein&nbsp;(g)</TableCell>
// //               <TableCell align="right">Actions</TableCell>
// //             </TableRow>
// //           </TableHead>
// //           <TableBody>
// //             {rows.map((row) => (
// //               <TableRow hover key={row.name}>
// //                 <TableCell sx={{ pl: 3 }} component="th" scope="row">
// //                   {row.name}
// //                 </TableCell>
// //                 <TableCell align="right">{row.calories}</TableCell>
// //                 <TableCell align="right">{row.fat}</TableCell>
// //                 <TableCell align="right">{row.carbs}</TableCell>
// //                 <TableCell align="right">{row.protein}</TableCell>
// //                 <TableCell align="right">
// //                   <IconButton onClick={() => handleEdit(row)}>
// //                     <EditIcon />
// //                   </IconButton>
// //                   <IconButton onClick={() => handleDelete(row)}>
// //                     <DeleteIcon />
// //                   </IconButton>
// //                 </TableCell>
// //               </TableRow>
// //             ))}
// //           </TableBody>
// //         </Table>
// //       </TableContainer>

// //       <Dialog open={open} onClose={handleClose}>
// //         <DialogTitle>Edit Row</DialogTitle>
// //         <DialogContent>
// //           <TextField
// //             margin="dense"
// //             name="name"
// //             label="Dessert"
// //             type="text"
// //             fullWidth
// //             value={currentRow?.name || ''}
// //             onChange={handleChange}
// //           />
// //           <TextField
// //             margin="dense"
// //             name="calories"
// //             label="Calories"
// //             type="number"
// //             fullWidth
// //             value={currentRow?.calories || ''}
// //             onChange={handleChange}
// //           />
// //           <TextField
// //             margin="dense"
// //             name="fat"
// //             label="Fat"
// //             type="number"
// //             fullWidth
// //             value={currentRow?.fat || ''}
// //             onChange={handleChange}
// //           />
// //           <TextField
// //             margin="dense"
// //             name="carbs"
// //             label="Carbs"
// //             type="number"
// //             fullWidth
// //             value={currentRow?.carbs || ''}
// //             onChange={handleChange}
// //           />
// //           <TextField
// //             margin="dense"
// //             name="protein"
// //             label="Protein"
// //             type="number"
// //             fullWidth
// //             value={currentRow?.protein || ''}
// //             onChange={handleChange}
// //           />
// //         </DialogContent>
// //         <DialogActions>
// //           <Button onClick={handleClose}>Cancel</Button>
// //           <Button onClick={handleSave}>Save</Button>
// //         </DialogActions>
// //       </Dialog>
// //     </MainCard>
// //   );
// // }








// import React, { useState } from 'react';
// // material-ui
// import Table from '@mui/material/Table';
// import TableBody from '@mui/material/TableBody';
// import TableCell from '@mui/material/TableCell';
// import TableContainer from '@mui/material/TableContainer';
// import TableHead from '@mui/material/TableHead';
// import TableRow from '@mui/material/TableRow';
// import IconButton from '@mui/material/IconButton';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import Dialog from '@mui/material/Dialog';
// import DialogActions from '@mui/material/DialogActions';
// import DialogContent from '@mui/material/DialogContent';
// import DialogTitle from '@mui/material/DialogTitle';
// import TextField from '@mui/material/TextField';
// import Button from '@mui/material/Button';
// import AddIcon from '@mui/icons-material/Add';

// // project-imports
// import MainCard from 'components/MainCard';
// import { CSVExport } from 'components/third-party/react-table';

// // table data
// function createData(name, location, email, phoneNumber) {
//   return { name, location, email, phoneNumber };
// }

// const initialRows = [
//   createData('John Doe', 'New York', '<EMAIL>', '************'),
//   createData('Jane Smith', 'Los Angeles', '<EMAIL>', '************'),
//   createData('Alice Johnson', 'Chicago', '<EMAIL>', '************'),
//   createData('Bob Brown', 'Houston', '<EMAIL>', '************'),
//   createData('Charlie Davis', 'Phoenix', '<EMAIL>', '************')
// ];

// export const header = [
//   { label: 'Name', key: 'name' },
//   { label: 'Location', key: 'location' },
//   { label: 'Email', key: 'email' },
//   { label: 'Phone Number', key: 'phoneNumber' }
// ];

// // ==============================|| MUI TABLE - BASIC ||============================== //

// export default function TableBasic() {
//   const [rows, setRows] = useState(initialRows);
//   const [open, setOpen] = useState(false);
//   const [currentRow, setCurrentRow] = useState(null);
//   const [isEditing, setIsEditing] = useState(false);

//   const handleAdd = () => {
//     setCurrentRow({ name: '', location: '', email: '', phoneNumber: '' });
//     setIsEditing(false);
//     setOpen(true);
//   };

//   const handleEdit = (row) => {
//     setCurrentRow({ ...row });
//     setIsEditing(true);
//     setOpen(true);
//   };

//   const handleDelete = (row) => {
//     setRows(rows.filter(r => r.name !== row.name));
//   };

//   const handleClose = () => {
//     setOpen(false);
//     setCurrentRow(null);
//   };

//   const handleSave = () => {
//     if (isEditing) {
//       setRows(rows.map(row => (row.name === currentRow.name ? currentRow : row)));
//     } else {
//       setRows([...rows, currentRow]);
//     }
//     handleClose();
//   };

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setCurrentRow({ ...currentRow, [name]: value });
//   };

//   return (
//     <MainCard
//       content={false}
//       title="Clients List"
//       secondary={
//         <div style={{ display: 'flex', alignItems: 'center' }}>
//           <Button
//             variant="contained"
//             color="primary"
//             startIcon={<AddIcon />}
//             onClick={handleAdd}
//             style={{ marginRight: '16px' }}
//           >
//             Add Data
//           </Button>
//           <CSVExport data={rows} headers={header} filename="basic-table-data.csv" />
//         </div>
//       }
//     >
    
  
//       {/* table */}
//       <TableContainer>
//         <Table sx={{ minWidth: 350 }} aria-label="simple table">
//           <TableHead>
//             <TableRow>
//               <TableCell sx={{ pl: 3 }}>Name</TableCell>
//               <TableCell align="right">Location</TableCell>
//               <TableCell align="right">Email</TableCell>
//               <TableCell align="right">Phone Number</TableCell>
//               <TableCell align="right">Actions</TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {rows.map((row) => (
//               <TableRow hover key={row.name}>
//                 <TableCell sx={{ pl: 3 }} component="th" scope="row">
//                   {row.name}
//                 </TableCell>
//                 <TableCell align="right">{row.location}</TableCell>
//                 <TableCell align="right">{row.email}</TableCell>
//                 <TableCell align="right">{row.phoneNumber}</TableCell>
//                 <TableCell align="right">
//                   <IconButton onClick={() => handleEdit(row)}>
//                     <EditIcon />
//                   </IconButton>
//                   <IconButton onClick={() => handleDelete(row)}>
//                     <DeleteIcon />
//                   </IconButton>
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </TableContainer>

//       <Dialog open={open} onClose={handleClose}>
//         <DialogTitle>{isEditing ? 'Edit Row' : 'Add Row'}</DialogTitle>
//         <DialogContent>
//           <TextField
//             margin="dense"
//             name="name"
//             label="Name"
//             type="text"
//             fullWidth
//             value={currentRow?.name || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="location"
//             label="Location"
//             type="text"
//             fullWidth
//             value={currentRow?.location || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="email"
//             label="Email"
//             type="email"
//             fullWidth
//             value={currentRow?.email || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="phoneNumber"
//             label="Phone Number"
//             type="text"
//             fullWidth
//             value={currentRow?.phoneNumber || ''}
//             onChange={handleChange}
//           />
//         </DialogContent>
//         <DialogActions>
//           <Button onClick={handleClose}>Cancel</Button>
//           <Button onClick={handleSave}>{isEditing ? 'Save' : 'Add'}</Button>
//         </DialogActions>
//       </Dialog>
//     </MainCard>
//   );
// }

// import React, { useState } from 'react';
// import MainCard from 'components/MainCard';
// import Table from '@mui/material/Table';
// import TableBody from '@mui/material/TableBody';
// import TableCell from '@mui/material/TableCell';
// import TableContainer from '@mui/material/TableContainer';
// import TableHead from '@mui/material/TableHead';
// import TableRow from '@mui/material/TableRow';
// import IconButton from '@mui/material/IconButton';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import Dialog from '@mui/material/Dialog';
// import DialogActions from '@mui/material/DialogActions';
// import DialogContent from '@mui/material/DialogContent';
// import DialogTitle from '@mui/material/DialogTitle';
// import TextField from '@mui/material/TextField';
// import Button from '@mui/material/Button';
// import AddIcon from '@mui/icons-material/Add';
// import TablePagination from '@mui/material/TablePagination';
// import { CSVExport } from 'components/third-party/react-table';

// // Table data creation function
// function createData(clientName, jobTitle, jobLocation, email, phoneNumber) {
//   return { id: Math.random(), clientName, jobTitle, jobLocation, email, phoneNumber };
// }

// // Initial rows for the table
// const initialRows = [
//   createData('John Doe', 'Developer', 'New York', '<EMAIL>', '************'),
//   createData('Jane Smith', 'Designer', 'Los Angeles', '<EMAIL>', '************'),
//   createData('Alice Johnson', 'Manager', 'Chicago', '<EMAIL>', '************'),
//   createData('Bob Brown', 'Engineer', 'Houston', '<EMAIL>', '************'),
//   createData('Charlie Davis', 'Analyst', 'Phoenix', '<EMAIL>', '************')
// ];

// // Header configuration
// export const header = [
//   { label: 'Client Name', key: 'clientName' },
//   { label: 'Job Title', key: 'jobTitle' },
//   { label: 'Job Location', key: 'jobLocation' },
//   { label: 'Email', key: 'email' },
//   { label: 'Phone Number', key: 'phoneNumber' }
// ];

// export default function TableBasic() {
//   // State for managing table rows, dialog open/close, and editing state
//   const [rows, setRows] = useState(initialRows);
//   const [open, setOpen] = useState(false);
//   const [currentRow, setCurrentRow] = useState(null);
//   const [isEditing, setIsEditing] = useState(false);
//   const [page, setPage] = useState(0);
//   const [rowsPerPage, setRowsPerPage] = useState(5);

//   // Functions for handling Add, Edit, Delete, Save, and Dialog Close actions
//   const handleAdd = () => {
//     setCurrentRow({ clientName: '', jobTitle: '', jobLocation: '', email: '', phoneNumber: '' });
//     setIsEditing(false);
//     setOpen(true);
//   };

//   const handleEdit = (row) => {
//     setCurrentRow({ ...row });
//     setIsEditing(true);
//     setOpen(true);
//   };

//   const handleDelete = (row) => {
//     setRows(rows.filter(r => r.id !== row.id));
//   };

//   const handleClose = () => {
//     setOpen(false);
//     setCurrentRow(null);
//   };

//   const handleSave = () => {
//     if (isEditing) {
//       setRows(rows.map(row => (row.id === currentRow.id ? currentRow : row)));
//     } else {
//       setRows([...rows, { ...currentRow, id: Math.random() }]);
//     }
//     handleClose();
//   };

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setCurrentRow({ ...currentRow, [name]: value });
//   };

//   // Pagination change handlers
//   const handleChangePage = (event, newPage) => {
//     setPage(newPage);
//   };

//   const handleChangeRowsPerPage = (event) => {
//     setRowsPerPage(parseInt(event.target.value, 10));
//     setPage(0); // Reset page to 0 when rowsPerPage changes
//   };

//   // Calculate pagination variables
//   const emptyRows = rowsPerPage - Math.min(rowsPerPage, rows.length - page * rowsPerPage);

//   return (
//     <MainCard
//       content={false}
//       title="Job Request"
//       secondary={
//         <div style={{ display: 'flex', alignItems: 'center' }}>
//           <Button
//             variant="contained"
//             color="primary"
//             startIcon={<AddIcon />}
//             onClick={handleAdd}
//             style={{ marginRight: '16px' }}
//           >
//             Add Data
//           </Button>
//           <CSVExport data={rows} headers={header} filename="basic-table-data.csv" />
//         </div>
//       }
//     >
//       {/* Table */}
//       <TableContainer>
//         <Table sx={{ minWidth: 350 }} aria-label="simple table">
//           <TableHead>
//             <TableRow>
//               <TableCell sx={{ pl: 3 }}>Client Name</TableCell>
//               <TableCell align="right">Job Title</TableCell>
//               <TableCell align="right">Job Location</TableCell>
//               <TableCell align="right">Email</TableCell>
//               <TableCell align="right">Phone Number</TableCell>
//               <TableCell align="right">Actions</TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {/* Display rows for the current page */}
//             {(rowsPerPage > 0
//               ? rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
//               : rows
//             ).map((row) => (
//               <TableRow hover key={row.id}>
//                 <TableCell sx={{ pl: 3 }} component="th" scope="row">
//                   {row.clientName}
//                 </TableCell>
//                 <TableCell align="right">{row.jobTitle}</TableCell>
//                 <TableCell align="right">{row.jobLocation}</TableCell>
//                 <TableCell align="right">{row.email}</TableCell>
//                 <TableCell align="right">{row.phoneNumber}</TableCell>
//                 <TableCell align="right">
//                   <IconButton onClick={() => handleEdit(row)}>
//                     <EditIcon />
//                   </IconButton>
//                   <IconButton onClick={() => handleDelete(row)}>
//                     <DeleteIcon />
//                   </IconButton>
//                 </TableCell>
//               </TableRow>
//             ))}
//             {/* Empty rows for pagination spacing */}
//             {emptyRows > 0 && (
//               <TableRow style={{ height: 53 * emptyRows }}>
//                 <TableCell colSpan={6} />
//               </TableRow>
//             )}
//           </TableBody>
//         </Table>
//       </TableContainer>

//       {/* Pagination */}
//       <TablePagination
//         rowsPerPageOptions={[5, 10, 25]}
//         component="div"
//         count={rows.length}
//         rowsPerPage={rowsPerPage}
//         page={page}
//         onPageChange={handleChangePage}
//         onRowsPerPageChange={handleChangeRowsPerPage}
//       />

//       {/* Dialog for adding/editing rows */}
//       <Dialog open={open} onClose={handleClose}>
//         <DialogTitle>{isEditing ? 'Edit Row' : 'Add Row'}</DialogTitle>
//         <DialogContent>
//           <TextField
//             margin="dense"
//             name="clientName"
//             label="Client Name"
//             type="text"
//             fullWidth
//             value={currentRow?.clientName || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="jobTitle"
//             label="Job Title"
//             type="text"
//             fullWidth
//             value={currentRow?.jobTitle || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="jobLocation"
//             label="Job Location"
//             type="text"
//             fullWidth
//             value={currentRow?.jobLocation || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="email"
//             label="Email"
//             type="email"
//             fullWidth
//             value={currentRow?.email || ''}
//             onChange={handleChange}
//           />
//           <TextField
//             margin="dense"
//             name="phoneNumber"
//             label="Phone Number"
//             type="text"
//             fullWidth
//             value={currentRow?.phoneNumber || ''}
//             onChange={handleChange}
//           />
//         </DialogContent>
//         <DialogActions>
//           <Button onClick={handleClose}>Cancel</Button>
//           <Button onClick={handleSave}>{isEditing ? 'Save' : 'Add'}</Button>
//         </DialogActions>
//       </Dialog>
//     </MainCard>
//   );
// }




// import React, { useEffect, useState } from 'react';

// import IconButton from '@mui/material/IconButton';
// import VisibilityIcon from '@mui/icons-material/Visibility';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';

// import { DataGrid } from '@mui/x-data-grid';
// import MainCard from 'components/MainCard';
// import FormDialog from './dilognew';

// function App() {
//   const rows = [
//     {
//       id: 1,
//       candidateName: "Alpha Investments",
//       contactNo: "************",
//       email: "<EMAIL>",
//       currentLocations: "New York",
//       residingState: "NY",
//       internshipAgreement: "Yes",
//       currentlyWorking: "Yes",
//       personalProjects: "Project A, Project B",
//       technologyInterest: "React, Node.js",
//       companyName: "Alpha Inc.",
//       totalExperience: 5,
//       courseTraining: "Web Development",
//       trainingInstitutionName: "ABC Institute",
//       keySkillsLearned: "JavaScript, React",
//       durationOfTraining: "6 months",
//       schoolName10thCompleted: "High School ABC",
//       collegeNameIntermediateCompleted: "College XYZ",
//       passedOutYear: 2010,
//       tenthPercentage: "85%",
//       cgpaName10thCompleted: "8.5",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2012,
//       interDiplomaPercentage: "87%",
//       cgpa12thOrDiploma: "8.7",
//       graduationStream: "Computer Science",
//       gradBranch: "Engineering",
//       gradUniversity: "University ABC",
//       gradPassedOutYear: 2016,
//       gradPercentage: "88%",
//       postGradStream: "Software Engineering",
//       postGradSpecialization: "AI",
//       postGradUniversity: "University XYZ",
//       postGradPassedOutYear: 2018,
//       postGradPercentage: "89%",
//       payExpectations: "100000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 2,
//       candidateName: "Beta Solutions",
//       contactNo: "987-654-3210",
//       email: "<EMAIL>",
//       currentLocations: "Los Angeles",
//       residingState: "CA",
//       internshipAgreement: "No",
//       currentlyWorking: "No",
//       personalProjects: "Project X, Project Y",
//       technologyInterest: "Vue.js, Python",
//       companyName: "Beta Ltd.",
//       totalExperience: 3,
//       courseTraining: "Data Science",
//       trainingInstitutionName: "XYZ Institute",
//       keySkillsLearned: "Python, Machine Learning",
//       durationOfTraining: "4 months",
//       schoolName10thCompleted: "High School DEF",
//       collegeNameIntermediateCompleted: "College UVW",
//       passedOutYear: 2012,
//       tenthPercentage: "90%",
//       cgpaName10thCompleted: "9.0",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2014,
//       interDiplomaPercentage: "92%",
//       cgpa12thOrDiploma: "9.2",
//       graduationStream: "Information Technology",
//       gradBranch: "Engineering",
//       gradUniversity: "University DEF",
//       gradPassedOutYear: 2018,
//       gradPercentage: "91%",
//       postGradStream: "Data Engineering",
//       postGradSpecialization: "Big Data",
//       postGradUniversity: "University UVW",
//       postGradPassedOutYear: 2020,
//       postGradPercentage: "93%",
//       payExpectations: "90000",
//       interestedInChidhagni: "No",
//       campusOffer: "Yes"
//     },
//     {
//       id: 3,
//       candidateName: "Gamma Tech",
//       contactNo: "555-123-4567",
//       email: "<EMAIL>",
//       currentLocations: "Chicago",
//       residingState: "IL",
//       internshipAgreement: "Yes",
//       currentlyWorking: "Yes",
//       personalProjects: "Project C, Project D",
//       technologyInterest: "Angular, Java",
//       companyName: "Gamma Corp.",
//       totalExperience: 4,
//       courseTraining: "Full Stack Development",
//       trainingInstitutionName: "LMN Institute",
//       keySkillsLearned: "Java, Angular",
//       durationOfTraining: "5 months",
//       schoolName10thCompleted: "High School GHI",
//       collegeNameIntermediateCompleted: "College STU",
//       passedOutYear: 2011,
//       tenthPercentage: "87%",
//       cgpaName10thCompleted: "8.7",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2013,
//       interDiplomaPercentage: "89%",
//       cgpa12thOrDiploma: "8.9",
//       graduationStream: "Software Engineering",
//       gradBranch: "Engineering",
//       gradUniversity: "University GHI",
//       gradPassedOutYear: 2017,
//       gradPercentage: "90%",
//       postGradStream: "Cloud Computing",
//       postGradSpecialization: "AWS",
//       postGradUniversity: "University STU",
//       postGradPassedOutYear: 2019,
//       postGradPercentage: "92%",
//       payExpectations: "95000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 4,
//       candidateName: "Delta Innovations",
//       contactNo: "************",
//       email: "<EMAIL>",
//       currentLocations: "San Francisco",
//       residingState: "CA",
//       internshipAgreement: "No",
//       currentlyWorking: "No",
//       personalProjects: "Project E, Project F",
//       technologyInterest: "Flutter, Dart",
//       companyName: "Delta LLC",
//       totalExperience: 2,
//       courseTraining: "Mobile App Development",
//       trainingInstitutionName: "OPQ Institute",
//       keySkillsLearned: "Flutter, Dart",
//       durationOfTraining: "3 months",
//       schoolName10thCompleted: "High School JKL",
//       collegeNameIntermediateCompleted: "College RST",
//       passedOutYear: 2013,
//       tenthPercentage: "88%",
//       cgpaName10thCompleted: "8.8",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2015,
//       interDiplomaPercentage: "90%",
//       cgpa12thOrDiploma: "9.0",
//       graduationStream: "Information Systems",
//       gradBranch: "Engineering",
//       gradUniversity: "University JKL",
//       gradPassedOutYear: 2019,
//       gradPercentage: "89%",
//       postGradStream: "Cyber Security",
//       postGradSpecialization: "Ethical Hacking",
//       postGradUniversity: "University RST",
//       postGradPassedOutYear: 2021,
//       postGradPercentage: "91%",
//       payExpectations: "85000",
//       interestedInChidhagni: "No",
//       campusOffer: "Yes"
//     },
//     {
//       id: 5,
//       candidateName: "Epsilon Enterprises",
//       contactNo: "333-987-6543",
//       email: "<EMAIL>",
//       currentLocations: "Houston",
//       residingState: "TX",
//       internshipAgreement: "Yes",
//       currentlyWorking: "No",
//       personalProjects: "Project G, Project H",
//       technologyInterest: "React Native, SQL",
//       companyName: "Epsilon Group",
//       totalExperience: 6,
//       courseTraining: "Database Management",
//       trainingInstitutionName: "RST Institute",
//       keySkillsLearned: "SQL, Database Design",
//       durationOfTraining: "7 months",
//       schoolName10thCompleted: "High School MNO",
//       collegeNameIntermediateCompleted: "College PQR",
//       passedOutYear: 2009,
//       tenthPercentage: "84%",
//       cgpaName10thCompleted: "8.4",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2011,
//       interDiplomaPercentage: "86%",
//       cgpa12thOrDiploma: "8.6",
//       graduationStream: "Software Development",
//       gradBranch: "Engineering",
//       gradUniversity: "University MNO",
//       gradPassedOutYear: 2015,
//       gradPercentage: "87%",
//       postGradStream: "Data Analytics",
//       postGradSpecialization: "Data Science",
//       postGradUniversity: "University PQR",
//       postGradPassedOutYear: 2017,
//       postGradPercentage: "90%",
//       payExpectations: "105000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 6,
//       candidateName: "Zeta Solutions",
//       contactNo: "222-654-3219",
//       email: "<EMAIL>",
//       currentLocations: "Austin",
//       residingState: "TX",
//       internshipAgreement: "No",
//       currentlyWorking: "Yes",
//       personalProjects: "Project I, Project J",
//       technologyInterest: "Kotlin, Spring Boot",
//       companyName: "Zeta Corp.",
//       totalExperience: 7,
//       courseTraining: "Backend Development",
//       trainingInstitutionName: "UVW Institute",
//       keySkillsLearned: "Java, Spring Boot",
//       durationOfTraining: "8 months",
//       schoolName10thCompleted: "High School PQR",
//       collegeNameIntermediateCompleted: "College JKL",
//       passedOutYear: 2008,
//       tenthPercentage: "86%",
//       cgpaName10thCompleted: "8.6",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2010,
//       interDiplomaPercentage: "89%",
//       cgpa12thOrDiploma: "8.9",
//       graduationStream: "Computer Science",
//       gradBranch: "Engineering",
//       gradUniversity: "University PQR",
//       gradPassedOutYear: 2014,
//       gradPercentage: "90%",
//       postGradStream: "Software Architecture",
//       postGradSpecialization: "Microservices",
//       postGradUniversity: "University JKL",
//       postGradPassedOutYear: 2016,
//       postGradPercentage: "92%",
//       payExpectations: "110000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     }
//   ];
  
//   const columns = [
//     { flex: 0.1, field: 'id', minWidth: 80, headerName: 'ID' },
//     { flex: 0.25, minWidth: 230, field: 'candidateName', headerName: 'Candidate Name' },
//     { flex: 0.25, minWidth: 150, field: 'contactNo', headerName: 'Contact Number' },
//     { flex: 0.25, minWidth: 230, field: 'email', headerName: 'Email' },
//     { flex: 0.25, minWidth: 200, field: 'currentLocations', headerName: 'Current Locations' },
//     { flex: 0.25, minWidth: 200, field: 'residingState', headerName: 'Residing State' },
//     {
//       flex: 0.5,
//       minWidth: 150,
//       field: 'actions',
//       headerName: 'Actions',
//       renderCell: (params) => (
//         <>
//           <IconButton onClick={() => handleViewClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <VisibilityIcon />
//           </IconButton>
//           <IconButton onClick={() => handleEditClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <EditIcon />
//           </IconButton>
//           <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
//             <DeleteIcon />
//           </IconButton>
//         </>
//       )
//     }
//   ];
//   const [viewData, setViewData] = useState(null);
//   const closeViewDialog = () => {
//     setViewData(null);
//   };
//   const [selectedRow, setSelectedRow] = React.useState(null);
//   const handleDeleteClick = (id) => {
//     // Filter out the item with the given id
//     const updatedRows = rows.filter(row => row.id !== id);
//     setRows(updatedRows); // Update state to reflect the deletion
//   };

//   const handleEditClick = (rowData) => {
//     setSelectedRow(rowData);
//     handleClickOpen();
//   };


//   const [open, setOpen] = React.useState(false);

//   const handleClickOpen = () => {
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };


  

//   const handleAddButtonClick = () => {
//     // Add your logic here for what happens when the button is clicked
//     console.log('Add button clicked!');
//   };


  

//   return (
//     <MainCard>
//       <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '1rem' }}>
//         <button onClick={handleClickOpen}>
//           Add
//         </button>
//       </div>
//       <div style={{ height: 400, width: '100%' }}>
//         <DataGrid
//           rows={rows}
//           columns={columns}
//           pageSize={5}
//           rowsPerPageOptions={[5, 10, 20]}
//           checkboxSelection
//           disableSelectionOnClick
//         />
//       </div>
//       <FormDialog open = {open} handleClose={handleClose}/>
//       {viewData && (
//         <Dialog open={Boolean(viewData)} onClose={closeViewDialog} maxWidth="md" fullWidth>
//           <DialogTitle>View Investment Manager Details</DialogTitle>
//           <DialogContent>
//             <DetailTable data={viewData} />
//           </DialogContent>
//           <DialogActions>
//             <Button onClick={closeViewDialog} color="primary">
//               Close
//             </Button>
//           </DialogActions>
//         </Dialog>
//       )}
//     </MainCard>
//   );





// } 
// export default App;


// import React, { useState } from 'react';

// import IconButton from '@mui/material/IconButton';
// import VisibilityIcon from '@mui/icons-material/Visibility';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';

// import { DataGrid } from '@mui/x-data-grid';
// import MainCard from 'components/MainCard';
// import FormDialog from './dilognew';
//  // Assuming you have this component
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';

// function App() {
//   const [rows, setRows] = useState([
//     {
//       id: 1,
//       candidateName: "Alpha Investments",
//       contactNo: "************",
//       email: "<EMAIL>",
//       currentLocations: "New York",
//       residingState: "NY",
//       internshipAgreement: "Yes",
//       currentlyWorking: "Yes",
//       personalProjects: "Project A, Project B",
//       technologyInterest: "React, Node.js",
//       companyName: "Alpha Inc.",
//       totalExperience: 5,
//       courseTraining: "Web Development",
//       trainingInstitutionName: "ABC Institute",
//       keySkillsLearned: "JavaScript, React",
//       durationOfTraining: "6 months",
//       schoolName10thCompleted: "High School ABC",
//       collegeNameIntermediateCompleted: "College XYZ",
//       passedOutYear: 2010,
//       tenthPercentage: "85%",
//       cgpaName10thCompleted: "8.5",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2012,
//       interDiplomaPercentage: "87%",
//       cgpa12thOrDiploma: "8.7",
//       graduationStream: "Computer Science",
//       gradBranch: "Engineering",
//       gradUniversity: "University ABC",
//       gradPassedOutYear: 2016,
//       gradPercentage: "88%",
//       postGradStream: "Software Engineering",
//       postGradSpecialization: "AI",
//       postGradUniversity: "University XYZ",
//       postGradPassedOutYear: 2018,
//       postGradPercentage: "89%",
//       payExpectations: "100000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 2,
//       candidateName: "Beta Solutions",
//       contactNo: "987-654-3210",
//       email: "<EMAIL>",
//       currentLocations: "Los Angeles",
//       residingState: "CA",
//       internshipAgreement: "No",
//       currentlyWorking: "No",
//       personalProjects: "Project X, Project Y",
//       technologyInterest: "Vue.js, Python",
//       companyName: "Beta Ltd.",
//       totalExperience: 3,
//       courseTraining: "Data Science",
//       trainingInstitutionName: "XYZ Institute",
//       keySkillsLearned: "Python, Machine Learning",
//       durationOfTraining: "4 months",
//       schoolName10thCompleted: "High School DEF",
//       collegeNameIntermediateCompleted: "College UVW",
//       passedOutYear: 2012,
//       tenthPercentage: "90%",
//       cgpaName10thCompleted: "9.0",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2014,
//       interDiplomaPercentage: "92%",
//       cgpa12thOrDiploma: "9.2",
//       graduationStream: "Information Technology",
//       gradBranch: "Engineering",
//       gradUniversity: "University DEF",
//       gradPassedOutYear: 2018,
//       gradPercentage: "91%",
//       postGradStream: "Data Engineering",
//       postGradSpecialization: "Big Data",
//       postGradUniversity: "University UVW",
//       postGradPassedOutYear: 2020,
//       postGradPercentage: "93%",
//       payExpectations: "90000",
//       interestedInChidhagni: "No",
//       campusOffer: "Yes"
//     },
//     {
//       id: 3,
//       candidateName: "Gamma Tech",
//       contactNo: "555-123-4567",
//       email: "<EMAIL>",
//       currentLocations: "Chicago",
//       residingState: "IL",
//       internshipAgreement: "Yes",
//       currentlyWorking: "Yes",
//       personalProjects: "Project C, Project D",
//       technologyInterest: "Angular, Java",
//       companyName: "Gamma Corp.",
//       totalExperience: 4,
//       courseTraining: "Full Stack Development",
//       trainingInstitutionName: "LMN Institute",
//       keySkillsLearned: "Java, Angular",
//       durationOfTraining: "5 months",
//       schoolName10thCompleted: "High School GHI",
//       collegeNameIntermediateCompleted: "College STU",
//       passedOutYear: 2011,
//       tenthPercentage: "87%",
//       cgpaName10thCompleted: "8.7",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2013,
//       interDiplomaPercentage: "89%",
//       cgpa12thOrDiploma: "8.9",
//       graduationStream: "Software Engineering",
//       gradBranch: "Engineering",
//       gradUniversity: "University GHI",
//       gradPassedOutYear: 2017,
//       gradPercentage: "90%",
//       postGradStream: "Cloud Computing",
//       postGradSpecialization: "AWS",
//       postGradUniversity: "University STU",
//       postGradPassedOutYear: 2019,
//       postGradPercentage: "92%",
//       payExpectations: "95000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 4,
//       candidateName: "Delta Innovations",
//       contactNo: "************",
//       email: "<EMAIL>",
//       currentLocations: "San Francisco",
//       residingState: "CA",
//       internshipAgreement: "No",
//       currentlyWorking: "No",
//       personalProjects: "Project E, Project F",
//       technologyInterest: "Flutter, Dart",
//       companyName: "Delta LLC",
//       totalExperience: 2,
//       courseTraining: "Mobile App Development",
//       trainingInstitutionName: "OPQ Institute",
//       keySkillsLearned: "Flutter, Dart",
//       durationOfTraining: "3 months",
//       schoolName10thCompleted: "High School JKL",
//       collegeNameIntermediateCompleted: "College RST",
//       passedOutYear: 2013,
//       tenthPercentage: "88%",
//       cgpaName10thCompleted: "8.8",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2015,
//       interDiplomaPercentage: "90%",
//       cgpa12thOrDiploma: "9.0",
//       graduationStream: "Information Systems",
//       gradBranch: "Engineering",
//       gradUniversity: "University JKL",
//       gradPassedOutYear: 2019,
//       gradPercentage: "89%",
//       postGradStream: "Cyber Security",
//       postGradSpecialization: "Ethical Hacking",
//       postGradUniversity: "University RST",
//       postGradPassedOutYear: 2021,
//       postGradPercentage: "91%",
//       payExpectations: "85000",
//       interestedInChidhagni: "No",
//       campusOffer: "Yes"
//     },
//     {
//       id: 5,
//       candidateName: "Epsilon Enterprises",
//       contactNo: "333-987-6543",
//       email: "<EMAIL>",
//       currentLocations: "Houston",
//       residingState: "TX",
//       internshipAgreement: "Yes",
//       currentlyWorking: "No",
//       personalProjects: "Project G, Project H",
//       technologyInterest: "React Native, SQL",
//       companyName: "Epsilon Group",
//       totalExperience: 6,
//       courseTraining: "Database Management",
//       trainingInstitutionName: "RST Institute",
//       keySkillsLearned: "SQL, Database Design",
//       durationOfTraining: "7 months",
//       schoolName10thCompleted: "High School MNO",
//       collegeNameIntermediateCompleted: "College PQR",
//       passedOutYear: 2009,
//       tenthPercentage: "84%",
//       cgpaName10thCompleted: "8.4",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2011,
//       interDiplomaPercentage: "86%",
//       cgpa12thOrDiploma: "8.6",
//       graduationStream: "Software Development",
//       gradBranch: "Engineering",
//       gradUniversity: "University MNO",
//       gradPassedOutYear: 2015,
//       gradPercentage: "87%",
//       postGradStream: "Data Analytics",
//       postGradSpecialization: "Data Science",
//       postGradUniversity: "University PQR",
//       postGradPassedOutYear: 2017,
//       postGradPercentage: "90%",
//       payExpectations: "105000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     },
//     {
//       id: 6,
//       candidateName: "Zeta Solutions",
//       contactNo: "222-654-3219",
//       email: "<EMAIL>",
//       currentLocations: "Austin",
//       residingState: "TX",
//       internshipAgreement: "No",
//       currentlyWorking: "Yes",
//       personalProjects: "Project I, Project J",
//       technologyInterest: "Kotlin, Spring Boot",
//       companyName: "Zeta Corp.",
//       totalExperience: 7,
//       courseTraining: "Backend Development",
//       trainingInstitutionName: "UVW Institute",
//       keySkillsLearned: "Java, Spring Boot",
//       durationOfTraining: "8 months",
//       schoolName10thCompleted: "High School PQR",
//       collegeNameIntermediateCompleted: "College JKL",
//       passedOutYear: 2008,
//       tenthPercentage: "86%",
//       cgpaName10thCompleted: "8.6",
//       educationLevel: "Graduate",
//       intermediateDiplomaPassedOutYear: 2010,
//       interDiplomaPercentage: "89%",
//       cgpa12thOrDiploma: "8.9",
//       graduationStream: "Computer Science",
//       gradBranch: "Engineering",
//       gradUniversity: "University PQR",
//       gradPassedOutYear: 2014,
//       gradPercentage: "90%",
//       postGradStream: "Software Architecture",
//       postGradSpecialization: "Microservices",
//       postGradUniversity: "University JKL",
//       postGradPassedOutYear: 2016,
//       postGradPercentage: "92%",
//       payExpectations: "110000",
//       interestedInChidhagni: "Yes",
//       campusOffer: "No"
//     }
//   ]);
     
        
//   const columns = [
//     { flex: 0.1, field: 'id', minWidth: 80, headerName: 'ID' },
//     { flex: 0.25, minWidth: 230, field: 'candidateName', headerName: 'Candidate Name' },
//     { flex: 0.25, minWidth: 150, field: 'contactNo', headerName: 'Contact Number' },
//     { flex: 0.25, minWidth: 230, field: 'email', headerName: 'Email' },
//     { flex: 0.25, minWidth: 200, field: 'currentLocations', headerName: 'Current Locations' },
//     { flex: 0.25, minWidth: 200, field: 'residingState', headerName: 'Residing State' },
//     {
//       flex: 0.5,
//       minWidth: 150,
//       field: 'actions',
//       headerName: 'Actions',
//       renderCell: (params) => (
//         <>
//           <IconButton onClick={() => handleViewClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <VisibilityIcon />
//           </IconButton>
//           <IconButton onClick={() => handleEditClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <EditIcon />
//           </IconButton>
//           <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
//             <DeleteIcon />
//           </IconButton>
//         </>
//       )
//     }
//   ];


//   const [viewData, setViewData] = useState(null);
//   const closeViewDialog = () => {
//     setViewData(null);
//   };

//   const [selectedRow, setSelectedRow] = useState(null);
//   const [open, setOpen] = useState(false);

//   const handleDeleteClick = (id) => {
//     const updatedRows = rows.filter(row => row.id !== id);
//     setRows(updatedRows);
//   };

//   const handleEditClick = (rowData) => {
//     setSelectedRow(rowData);
//     handleClickOpen();
//   };

//   const handleViewClick = (id) => {
//     const row = rows.find(row => row.id === id);
//     setViewData(row);
//   };

//   const handleClickOpen = () => {
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };

//   const DetailTable = ({ data }) => {
//     return (
//       <table style={{ width: '100%', borderCollapse: 'collapse' }}>
//         <tbody>
//           {Object.keys(data).map((key) => (
//             <tr key={key} style={{ borderBottom: '1px solid #ddd' }}>
//               <td style={{ padding: '8px', fontWeight: 'bold', textTransform: 'capitalize' }}>{key}</td>
//               <td style={{ padding: '8px' }}>{data[key]}</td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     );
//   };

//   return (
//     <MainCard
//     content={false}
//     title="Graduate Form"
//     secondary={
//       <>
//         <Button variant="contained" color="primary" onClick={handleClickOpen} style={{ marginRight: '16px' }}>
//           Add
//         </Button>
       
//       </>
//     }
//   >
//       <div style={{ height: 400, width: '100%' }}>
//         <DataGrid
//           rows={rows}
//           columns={columns}
//           pageSize={5}
//           rowsPerPageOptions={[5, 10, 20]}
//           checkboxSelection
//           disableSelectionOnClick
//         />
//       </div>
//       <FormDialog open={open} handleClose={handleClose} selectedRow={selectedRow} />
//       {viewData && (
//         <Dialog open={Boolean(viewData)} onClose={closeViewDialog} maxWidth="md" fullWidth>
//           <DialogTitle>View Investment Manager Details</DialogTitle>
//           <DialogContent>
//             <DetailTable data={viewData} />
//           </DialogContent>
//           <DialogActions>
//             <Button onClick={closeViewDialog} color="primary">
//               Close
//             </Button>
//           </DialogActions>
//         </Dialog>
//       )}
//     </MainCard>
//   );
// }

// export default App;
// import React, { useState, useEffect } from 'react';
// import IconButton from '@mui/material/IconButton';
// import VisibilityIcon from '@mui/icons-material/Visibility';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import { DataGrid } from '@mui/x-data-grid';
// import MainCard from 'components/MainCard';
// import FormDialog from './dilognew'; // Assuming you have this component
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';

// function App() {
//   const [rows, setRows] = useState([]);
//   const [viewData, setViewData] = useState(null);
//   const [selectedRow, setSelectedRow] = useState(null);
//   const [open, setOpen] = useState(false);

//   useEffect(() => {
//     const fetchData = async () => {
//       try {
//         const response = await fetch('http://127.0.0.1:8000/graduate_details/');
//         const data = await response.json();

//         // Ensure each row has a unique id
//         const dataWithIds = data.map((row, index) => ({
//           id: row.id || index + 1, // Use existing id or generate one
//           ...row
//         }));
        
//         setRows(dataWithIds);
//       } catch (error) {
//         console.error('Error fetching data:', error);
//       }
//     };

//     fetchData();
//   }, []);

//   const columns = [
//     { flex: 0.1, field: 'id', minWidth: 80, headerName: 'ID' },
//     { flex: 0.25, minWidth: 230, field: 'candidateName', headerName: 'Candidate Name' },
//     { flex: 0.25, minWidth: 150, field: 'contactNo', headerName: 'Contact Number' },
//     { flex: 0.25, minWidth: 230, field: 'email', headerName: 'Email' },
//     { flex: 0.25, minWidth: 200, field: 'currentLocations', headerName: 'Current Locations' },
//     { flex: 0.25, minWidth: 200, field: 'residingState', headerName: 'Residing State' },
//     {
//       flex: 0.5,
//       minWidth: 150,
//       field: 'actions',
//       headerName: 'Actions',
//       renderCell: (params) => (
//         <>
//           <IconButton onClick={() => handleViewClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <VisibilityIcon />
//           </IconButton>
//           <IconButton onClick={() => handleEditClick(params.row)} style={{ marginRight: '8px' }}>
//             <EditIcon />
//           </IconButton>
//           <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
//             <DeleteIcon />
//           </IconButton>
//         </>
//       )
//     }
//   ];



//   const closeViewDialog = () => {
//     setViewData(null);
//   };

//   const handleDeleteClick = (id) => {
//     const updatedRows = rows.filter((row) => row.id !== id);
//     setRows(updatedRows);
//   };

//   const handleEditClick = (olddata) => {
 
   
//     setSelectedRow(olddata)
//     handleClickOpen()
//     // console.log(olddata)
//   };

//   const handleViewClick = (id) => {
//     const row = rows.find((row) => row.id === id);
//     setViewData(row);
//   };

//   const handleClickOpen = () => {
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };

//   const DetailTable = ({ data }) => {
//     return (
//       <table style={{ width: '100%', borderCollapse: 'collapse' }}>
//         <tbody>
//           {Object.keys(data).map((key) => (
//             <tr key={key} style={{ borderBottom: '1px solid #ddd' }}>
//               <td style={{ padding: '8px', fontWeight: 'bold', textTransform: 'capitalize' }}>{key}</td>
//               <td style={{ padding: '8px' }}>{data[key]}</td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     );
//   };
  

//   return (
//     <MainCard
//       content={false}
//       title="Graduate Form"
//       secondary={
//         <>
//           <Button variant="contained" color="primary" onClick={handleClickOpen} style={{ marginRight: '16px' }}>
//             Add
//           </Button>
//         </>
//       }
//     >
//       <div style={{ height: 400, width: '100%' }}>
//         <DataGrid
//           rows={rows}
//           columns={columns}
//           pageSize={5}
//           rowsPerPageOptions={[5, 10, 20]}
//           checkboxSelection
//           disableSelectionOnClick
//         />
//       </div>
//       <FormDialog open={open} handleClose={handleClose} selectedRow={selectedRow} />
//       {viewData && (
//         <Dialog open={Boolean(viewData)} onClose={closeViewDialog} maxWidth="md" fullWidth>
//           <DialogTitle>View Investment Manager Details</DialogTitle>
//           <DialogContent>
//             <DetailTable data={viewData} />
//           </DialogContent>
//           <DialogActions>
//             <Button onClick={closeViewDialog} color="primary">
//               Close
//             </Button>
//           </DialogActions>
//         </Dialog>
//       )}
//     </MainCard>
//   );
// }

// export default App;
// import React, { useState, useEffect } from 'react';
// import IconButton from '@mui/material/IconButton';
// import VisibilityIcon from '@mui/icons-material/Visibility';
// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import { DataGrid } from '@mui/x-data-grid';
// import MainCard from 'components/MainCard';
// import FormDialog from './dilognew'; // Assuming you have this component
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';

// function App() {
//   const [rows, setRows] = useState([]);
//   const [viewData, setViewData] = useState(null);
//   const [selectedRow, setSelectedRow] = useState(null);
//   const [open, setOpen] = useState(false);

//   const fetchData = async () => {
//     try {
//       const response = await fetch('http://127.0.0.1:8000/graduate_details/');
//       const data = await response.json();

//       // Ensure each row has a unique id
//       const dataWithIds = data.map((row, index) => ({
//         id: row.id || index + 1, // Use existing id or generate one
//         ...row
//       }));
      
//       setRows(dataWithIds);
//       handleClose();
//     } catch (error) {
//       console.error('Error fetching data:', error);
//     }
//   };
//   useEffect(() => {
  

//     fetchData();
//   }, []);

//   const columns = [
//     { flex: 0.1, field: 'id', minWidth: 80, headerName: 'ID' },
//     { flex: 0.25, minWidth: 230, field: 'candidateName', headerName: 'Candidate Name' },
//     { flex: 0.25, minWidth: 150, field: 'contactNo', headerName: 'Contact Number' },
//     { flex: 0.25, minWidth: 230, field: 'email', headerName: 'Email' },
//     { flex: 0.25, minWidth: 200, field: 'currentLocations', headerName: 'Current Locations' },
//     { flex: 0.25, minWidth: 200, field: 'residingState', headerName: 'Residing State' },
//     {
//       flex: 0.5,
//       minWidth: 150,
//       field: 'actions',
//       headerName: 'Actions',
//       renderCell: (params) => (
//         <>
//           <IconButton onClick={() => handleViewClick(params.row.id)} style={{ marginRight: '8px' }}>
//             <VisibilityIcon />
//           </IconButton>
//           <IconButton onClick={() => handleEditClick(params.row)} style={{ marginRight: '8px' }}>
//             <EditIcon />
//           </IconButton>
//           <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
//             <DeleteIcon />
//           </IconButton>
//         </>
//       )
//     }
//   ];

//   const closeViewDialog = () => {
//     setViewData(null);
//   };

//   // const handleDeleteClick = (id) => {
//   //   // const updatedRows = rows.filter((row) => row.id !== id);
//   //   // setRows(updatedRows);
//   //   console.log(id)
//   // };


//   // const handleDeleteClick = async (id) => {
//   //   try {
//   //     const response = await fetch(`http://127.0.0.1:8000/delete_graduate/${selectedRow.id}`, {
//   //       method: 'DELETE',
//   //     });

//   //     if (response.ok) {
//   //       const updatedRows = rows.filter((row) => row.id !== id);
//   //       setRows(updatedRows);
//   //     } else {
//   //       console.error('Failed to delete user');
//   //     }
//   //   } catch (error) {
//   //     console.error('Error deleting user:', error);
//   //   }
//   // };
//   const handleDeleteClick = async (id) => {
//     try {
//       // Ensure id is defined
//       if (!id) {
//         throw new Error('Invalid ID');
//       }
      
//       const response = await fetch(`http://127.0.0.1:8000/delete_graduate/${id}`, {
//         method: 'DELETE',
//       });
  
//       if (response.ok) {
//         const updatedRows = rows.filter((row) => row.id !== id);
//         setRows(updatedRows);
//       } else {
//         console.error('Failed to delete user');
//       }
//     } catch (error) {
//       console.error('Error deleting user:', error);
//     }
//   };
  

//   const handleEditClick = (rowData) => {
//     setSelectedRow(rowData);
//     handleClickOpen();
//     console.log(rowData);
//   };

//   const handleViewClick = (id) => {
//     const row = rows.find((row) => row.id === id);
//     setViewData(row);
//   };

//   const handleClickOpen = () => {
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//     // setSelectedRow(null); // Clear selectedRow when dialog is closed
//   };

//   const DetailTable = ({ data }) => {
//     return (
//       <table style={{ width: '100%', borderCollapse: 'collapse' }}>
//         <tbody>
//           {Object.keys(data).map((key) => (
//             <tr key={key} style={{ borderBottom: '1px solid #ddd' }}>
//               <td style={{ padding: '8px', fontWeight: 'bold', textTransform: 'capitalize' }}>{key}</td>
//               <td style={{ padding: '8px' }}>{data[key]}</td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     );
//   };


//   const handleUpdate = (updatedItem) => {
//     setData((prevData) => {
//       const index = prevData.findIndex(item => item.id === updatedItem.id);
//       if (index !== -1) {
//         // Update existing item
//         const newData = [...prevData];
//         newData[index] = updatedItem;
//         return newData;
//       } else {
//         // Add new item
//         return [...prevData, updatedItem];
//       }
//     });
//   };





//   return (
//     <MainCard
//       content={false}
//       title="Graduate data"
//       secondary={
//         <>
//           <Button variant="contained" color="primary" onClick={handleClickOpen} style={{ marginRight: '16px' }}>
//             Add
//           </Button>
//         </>
//       }
//     >
//       <div style={{ height: 400, width: '100%' }}>
//         <DataGrid
//           rows={rows}
//           columns={columns}
//           pageSize={5}
//           rowsPerPageOptions={[5, 10, 20]}
//           checkboxSelection
//           disableSelectionOnClick
//         />
//       </div>
//       <FormDialog open={open} handleClose={handleClose} selectedRow={selectedRow} onUpdate={handleUpdate} fetchData ={fetchData} />
//       {viewData && (
//         <Dialog open={Boolean(viewData)} onClose={closeViewDialog} maxWidth="md" fullWidth>
//           <DialogTitle>Graduate Details</DialogTitle>
//           <DialogContent>
//             <DetailTable data={viewData} />
//           </DialogContent>
//           <DialogActions>
//             <Button onClick={closeViewDialog} color="primary">
//               Close
//             </Button>
//           </DialogActions>
//         </Dialog>
//       )}
//     </MainCard>
//   );
// }

// export default App;
import React, { useState, useEffect } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import Link from '@mui/material/Link';
import MainCard from 'components/MainCard';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { is } from 'immutable';
import FormDialog from './dialognew';

function App() {
  const [rows, setRows] = useState([]);
  const [viewData, setViewData] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [open, setOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const token = localStorage.getItem('serviceToken'); // Assume token is stored in localStorage

  // Fetch client data from the backend
  const fetchClientData = async () => {
    try {
      const response = await fetch('http://localhost:8080/clients', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRows(data);
      } else {
        console.error('Failed to fetch client data');
      }
    } catch (error) {
      console.error('Error fetching client data:', error);
    }
  };

  useEffect(() => {
    fetchClientData(); // Fetch data when the component mounts
  }, []);

//   const columns = [
//     { flex: 0.1, field: 'id', minWidth: 80, headerName: 'Client ID' },
//     {
//       flex: 0.25,
//       minWidth: 230,
//       field: 'client_name',
//       headerName: 'Client Name',
//       renderCell: (params) => (
//         <Link component="button" variant="body2" onClick={() => handleViewClick(params.row.id)}>
//           {params.row.client_name}
//         </Link>
//       ),
//     },
//     { flex: 0.25, minWidth: 150, field: 'contacts_number', headerName: 'Contacts Number' },
//     { flex: 0.25, minWidth: 230, field: 'email_id', headerName: 'Email ID' },
//     { flex: 0.25, minWidth: 230, field: 'website', headerName: 'Website' },
//     // { flex: 0.25, minWidth: 230, field: 'industry', headerName: 'Industry' },
//     // { flex: 0.25, minWidth: 150, field: 'country', headerName: 'Country' },
//     // { flex: 0.25, minWidth: 150, field: 'saret', headerName: 'Saret' },
//     // { flex: 0.25, minWidth: 150, field: 'city', headerName: 'City' },
//     // { flex: 0.25, minWidth: 230, field: 'business_unit', headerName: 'Business Unit' },
//     // { flex: 0.25, minWidth: 230, field: 'category', headerName: 'Category' },
//     // { flex: 0.25, minWidth: 150, field: 'postal', headerName: 'Postal' },
//     // { flex: 0.25, minWidth: 200, field: 'created_by', headerName: 'Created By' },
//     // { flex: 0.25, minWidth: 200, field: 'created_on', headerName: 'Created On' },
    
//     {
//   field: 'is_active',
//   headerName: 'Active',
//   flex: 0.1,
//   minWidth: 100,
//   renderCell: (params) => {
//     const isActive = params.row.is_active !== false; // Treat `null` as active
//     return (
//       <Typography variant="body2" color={isActive ? 'green' : 'red'}>
//         {isActive ? 'Active' : 'Inactive'}
//       </Typography>
//     );
//   },
// },

//     {
//       flex: 0.5,
//       minWidth: 150,
//       field: 'actions',
//       headerName: 'Actions',
//       renderCell: (params) => (
//         <>
//           <IconButton onClick={() => handleEditClick(params.row)} style={{ marginRight: '8px' }}>
//             <EditIcon />
//           </IconButton>
//           <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
//             <DeleteIcon />
//           </IconButton>
//         </>
//       ),
//     },
//   ];  
const columns = [
  { flex: 0.1, field: 'id', minWidth: 80, headerName: 'Client ID' },
  {
    flex: 0.25,
    minWidth: 230,
    field: 'client_name',
    headerName: 'Client Name',
    renderCell: (params) => (
      <Link component="button" variant="body2" onClick={() => handleViewClick(params.row.id)}>
        {params.row.client_name}
      </Link>
    ),
  },
  { flex: 0.25, minWidth: 150, field: 'contacts_number', headerName: 'Contacts Number' },
  { flex: 0.25, minWidth: 230, field: 'email_id', headerName: 'Email ID' },
  { flex: 0.25, minWidth: 230, field: 'website', headerName: 'Website' },
  {
    field: 'is_active',
    headerName: 'Active',
    flex: 0.1,
    minWidth: 100,
    renderCell: (params) => (
      <Typography variant="body2" color={params.row.is_active ? 'green' : 'red'}>
        {params.row.is_active ? 'Active' : 'Inactive'}
      </Typography>
    ),
  },
  {
    flex: 0.5,
    minWidth: 150,
    field: 'actions',
    headerName: 'Actions',
    renderCell: (params) => (
      <>
        <IconButton onClick={() => handleEditClick(params.row)} style={{ marginRight: '8px' }}>
          <EditIcon />
        </IconButton>
        <IconButton onClick={() => handleDeleteClick(params.row.id)} style={{ color: 'red' }}>
          <DeleteIcon />
        </IconButton>
      </>
    ),
  },
];



const [jobRequests, setJobRequests] = useState([]);
const [clientId, setClientId] = useState(null);

const fetchJobRequests = async (clientId) => {
  try {
    const response = await fetch(`http://localhost:8000/job-requests/client/${clientId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      setJobRequests(data);
    } else {
      console.error('Failed to fetch job requests');
    }
  } catch (error) {
    console.error('Error fetching job requests:', error);
  }
};
const handleViewClick = (id) => {
  const row = rows.find((row) => row.id === id);
  setViewData(row);
  setClientId(id);
  fetchJobRequests(id);
};



  const closeViewDialog = () => {
    setViewData(null);
  };

  // Delete handler to remove a client
  const handleDeleteClick = async (id) => {
    try {
      const response = await fetch(`http://localhost:8080/clients/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Mark the job request as inactive
        setRows(rows.map((row) => 
          row.id === id ? { ...row, is_active: false } : row
        ));
      } else {
        console.error('Failed to deactivate job request');
      }
    } catch (error) {
      console.error('Error deactivating job request:', error);
    }
  };
  // For Add
  const handleAddClick = () => {
    setSelectedRow(null); // Clear selectedRow
    setIsEditMode(false); // Set to add mode
    setOpen(true);        // Open the dialog
  };

  const handleEditClick = (rowData) => {
    setSelectedRow(rowData);
    handleClickOpen();
    console.log("Data handling click ************", rowData);
    setIsEditMode(true);  // Set to edit mode
    setOpen(true);  
  };

  const jobRequestsColumns = [
    { flex: 0.1, field: 'id', minWidth: 80, headerName: 'Job Request ID' },
    { flex: 0.25, minWidth: 230, field: 'job_title', headerName: 'Job Title' },
    { flex: 0.25, minWidth: 150, field: 'status', headerName: 'Status' },

  ];

  // const handleViewClick = (id) => {
  //   const row = rows.find((row) => row.id === id);
  //   setViewData(row);
  // };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedRow(null); // Clear the selectedRow on close
  };

  const handleUpdate = (updatedItem) => {
    setRows((prevData) => {
      const index = prevData.findIndex((item) => item.id === updatedItem.id);
      if (index !== -1) {
        const newData = [...prevData];
        newData[index] = updatedItem;
        return newData;
      } else {
        return [...prevData, updatedItem];
      }
    });
  };

  return (
    <MainCard
      content={false}
      title="Client Data"
      secondary={
        <>
          <Button variant="contained" color="primary" onClick={handleAddClick} style={{ marginRight: '16px' }}>
            Add Client
          </Button>
        </>
      }
    >
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid
          rows={rows}
          columns={columns}
          pageSize={5}
          rowsPerPageOptions={[5, 10, 20]}
          checkboxSelection
          disableSelectionOnClick
        />
      </div>

      <FormDialog
        open={open}
        handleClose={handleClose}
        selectedRow={selectedRow}
        onUpdate={handleUpdate}
        fetchData={fetchClientData}
        isEditMode={isEditMode} // Pass the mode
      />

      {viewData && (
        <Dialog open={Boolean(viewData)} onClose={closeViewDialog} maxWidth="md" fullWidth>
          <DialogTitle>Client Information </DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2 }}>
              {/* Company Name and Status */}
              <Typography variant="h6" component="div">
                {viewData.client_name} <span style={{ fontSize: 'small' }}>({viewData.status})</span>
              </Typography>

              {/* Contact Number, Location, and Website */}
              <Typography variant="body2" color="textSecondary">
                {viewData.contacts_number} | {viewData.city}, {viewData.saret}, {viewData.country}
              </Typography>
              <Link href={`https://${viewData.website}`} target="_blank" rel="noopener noreferrer">
                {viewData.website}
              </Link>

              {/* Grid Layout for Remaining Details */}
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  {/* <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Primary Owner:</strong> {viewData.primaryOwner}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Status:</strong> {viewData.status}
                    </Typography>
                  </Grid> */}
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Category:</strong> {viewData.category}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Industry:</strong> {viewData.industry}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Business Unit:</strong> {viewData.business_unit}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Postal:</strong> {viewData.postal}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Created By:</strong> {viewData.created_by}
                    </Typography>
                  </Grid>
                  <Grid item xs={3}>
                    <Typography variant="body1">
                      <strong>Created On:</strong> {viewData.created_on}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </DialogContent>
          
          <div style={{ height: 400, width: '100%', marginTop: '16px' }}>
          <DataGrid
            rows={jobRequests}
            columns={jobRequestsColumns}
            pageSize={5}
            rowsPerPageOptions={[5, 10, 20]}
            checkboxSelection
            disableSelectionOnClick
          />
        </div>
          <DialogActions>
            <Button onClick={closeViewDialog} color="primary">
              Close
            </Button>
            <Button variant="contained" color="primary">
              Add Job
            </Button>
          
          </DialogActions>
        

        </Dialog>
      )}
    </MainCard>
  );
}

export default App;