import React, { useEffect, useState } from 'react';
import { FormControl, MenuItem, Select } from '@mui/material';
import { Controller } from 'react-hook-form';

const CustomDropdownFields = ({
  name,
  control,
  placeholder,
  options = [], // Initial options list
  sx = {},
  ...props
}) => {
  console.log('Checking the data passing:', options);

  const [dropdownOptions, setDropdownOptions] = useState(options); // State to track options

  // Update options when new options are passed from the parent
  useEffect(() => {
    setDropdownOptions(options);
  }, [options]);

  return (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            size="small"
            fullWidth
            displayEmpty
            variant="outlined"
            renderValue={(selected) => {
              const selectedOption = dropdownOptions.find((option) => option.value === selected);
              return selectedOption ? selectedOption.label : <em>{placeholder}</em>;
            }}
            sx={{
              backgroundColor: sx.backgroundColor || 'white', // ✅ Allow custom background
              // borderRadius: "0px",
              '& .MuiSelect-select': {
                backgroundColor: sx.backgroundColor || 'white'
                // padding:"8px"
              },
              // "& fieldset": {
              //   borderRadius: "0px",
              // },
              '& .MuiInputBase-input::placeholder': {
                fontStyle: 'Inter var',
                color: 'rgba(0, 0, 0, 0.6)'
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main'
              },

              ...sx // ✅ Merge any additional styles
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  borderRadius: '0px !important',
                  padding: '0 !important',
                  margin: '0 !important'
                }
              },
              MenuListProps: {
                sx: {
                  paddingTop: '0 !important', // Remove padding at the top of the dropdown list
                  paddingBottom: '0 !important' // Remove padding at the bottom of the dropdown list
                }
              }
            }}
            {...props}
          >
            {/* <MenuItem value="" disabled>
              <em>{placeholder}</em>
            </MenuItem> */}
            {dropdownOptions.map((option) => (
              <MenuItem
                key={option.value}
                value={option.value}
                sx={{
                  border: '1px solid transparent',
                  borderRadius: '0px',
                  padding: '4px',
                  '&:hover': {
                    borderColor: 'black', // Border color becomes black when hovered
                    backgroundColor: 'rgba(0, 0, 0, 0.1)' // Light background on hover
                  },

                  '&.Mui-focusVisible': {
                    borderColor: 'black' // Border color becomes black when clicked/focused
                  }
                }}
              >
                {option.label} {/* Display client name instead of ID */}
              </MenuItem>
            ))}
          </Select>
        )}
      />
    </FormControl>
  );
};

export default CustomDropdownFields;
