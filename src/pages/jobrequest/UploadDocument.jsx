import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  <PERSON>ack,
  Typography,
  FormHelperText,
  Box,
  IconButton,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Button
} from '@mui/material';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import MainCard from 'components/MainCard';
import MultiFileUpload from 'components/third-party/drop-zone/MultiFile';
import { Formik } from 'formik';
import * as yup from 'yup';
import { Visibility, Close, Fullscreen, FullscreenExit, Description, Delete } from '@mui/icons-material';
import Mammoth from 'mammoth';
import upload from "src/assets/images/upload/upload.svg"

export default function ApplicantDropzonePage() {
  const [file, setFileData] = useState();
  const [filePreview, setFilePreview] = useState(file || null);
  console.log('File Type:', filePreview?.type);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [replaceDialogOpen, setReplaceDialogOpen] = useState(false);
  const [newFile, setNewFile] = useState(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [docxContent, setDocxContent] = useState('');
  const validFileTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.gif', '.bmp', '.png'];

  useEffect(() => {
    if (file) {
      setFilePreview(file);
    }
  }, [file]);
  useEffect(() => {
    if (filePreview?.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      console.log('Processing DOCX file:', filePreview.url);

      fetch(filePreview.url)
        .then((res) => res.arrayBuffer())
        .then((buffer) => {
          console.log('DOCX buffer loaded:', buffer);
          return Mammoth.convertToHtml({ arrayBuffer: buffer });
        })
        .then((result) => {
          console.log('Extracted DOCX Content:***', result.value);
          setDocxContent(result.value);
        })
        .catch((error) => {
          console.error('Error processing DOCX file:', error);
          setDocxContent('<p>Failed to load document.</p>');
        });
    }
  }, [filePreview]);

  console.log('File Type:', filePreview?.type);
  console.log('DOCX Content Available:', !!docxContent);

  if (filePreview?.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && docxContent) {
    console.log('DOCX Preview Condition is TRUE*S');
  } else {
    console.log('DOCX Preview Condition is FALSE');
  }
  const handleFileChange = (files) => {
    if (!files || files.length === 0) return;
    const file = files[0];

    setFileData(files[0]);
    setFilePreview(files[0]);

    if (filePreview) {
      setNewFile(file);
      setReplaceDialogOpen(true);
    } else {
      processFile(file);
    }
  };

  const processFile = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => setFilePreview({ name: file.name, url: e.target.result, type: file.type });
    reader.readAsDataURL(file);
  };

  const handleConfirmReplace = () => {
    if (newFile) processFile(newFile);
    setReplaceDialogOpen(false);
    setNewFile(null);
  };

  const handleCancelReplace = () => {
    setReplaceDialogOpen(false);
    setNewFile(null);
  };

  const handlePreview = () => setPreviewOpen(true);
  const handleClosePreview = () => setPreviewOpen(false);
  const toggleFullScreen = () => setIsFullScreen(!isFullScreen);

  return (
    <Grid container spacing={3}>
      <Grid item xs={10} sx={12}>
        <Box sx={{ borderRadius: '2px' }}>
          {/* <img src={upload} alt='upload image' /> */}
          <Formik
            initialValues={{ file: null }}
            onSubmit={() => {}}
            validationSchema={yup.object().shape({ file: yup.mixed().required('File is required.') })}
          >
            {({ values, setFieldValue, touched, errors }) => (
              <form>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Stack spacing={1.5} alignItems="center">
                      <MultiFileUpload
                        showList={false}
                        setFieldValue={(name, files) => {
                          setFieldValue(name, files);
                          handleFileChange(files);
                        }}
                        files={values.file}
                        error={touched.file && !!errors.file}
                      />
                    </Stack>
                    {touched.file && errors.file && <FormHelperText error>{errors.file}</FormHelperText>}
                  </Grid>

                  {filePreview && (
                    <Grid item xs={12}>
                      <TableContainer component={Paper}>
                        <Table>
                          <TableHead>
                            <TableRow sx={{ backgroundColor: '#f5f8f2' }}>
                              <TableCell>
                                <strong>FILE NAME</strong>
                              </TableCell>
                              <TableCell align="center">
                                <strong>ACTIONS</strong>
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell sx={{ fontWeight: 'bold' }}>{filePreview?.name || 'Unknown File'}</TableCell>
                              <TableCell align="center">
                                {/* Preview Button */}
                                {filePreview.type.includes('image') ||
                                filePreview.type.includes('pdf') ||
                                filePreview.type === 'application/msword' ||
                                filePreview.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ? (
                                  <IconButton onClick={handlePreview} sx={{ color: 'primary.main' }}>
                                    <Visibility />
                                  </IconButton>
                                ) : validFileTypes.includes(`.${filePreview.name.split('.').pop().toLowerCase()}`) ? (
                                  <IconButton component="a" href={filePreview.url} download={filePreview.name} sx={{ color: 'green' }}>
                                    <Visibility />
                                  </IconButton>
                                ) : (
                                  <Typography variant="body2" color="textSecondary">
                                    Unsupported file format
                                  </Typography>
                                )}

                                {/* Delete Button */}
                                {/* <IconButton onClick={() => setFilePreview(null)} sx={{ color: "red" }}>
                <Delete />
              </IconButton> */}
                                <IconButton
                                  onClick={() => {
                                    setFilePreview(null);
                                    setFileData(null); // ✅ Clear file data when deleting
                                  }}
                                  sx={{ color: 'red' }}
                                >
                                  <Delete />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>
                  )}
                </Grid>
              </form>
            )}
          </Formik>
        </Box>
      </Grid>

      <Dialog open={previewOpen} onClose={handleClosePreview} maxWidth="sm" fullWidth fullScreen={isFullScreen}>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">{filePreview?.name}</Typography>
            <Box>
              <IconButton onClick={toggleFullScreen}>{isFullScreen ? <FullscreenExit /> : <Fullscreen />}</IconButton>
              <IconButton onClick={handleClosePreview}>
                <Close />
              </IconButton>
            </Box>
          </Box>
          {filePreview?.type.includes('image') ? (
            <img src={filePreview.url} alt={filePreview.name} width="100%" style={{ borderRadius: '5px' }} />
          ) : filePreview?.type.includes('pdf') ? (
            <iframe src={filePreview.url} width="100%" height="500px" title="PDF Preview"></iframe>
          ) : filePreview?.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' && docxContent ? (
            <div dangerouslySetInnerHTML={{ __html: docxContent }} style={{ maxWidth: '100%', maxHeight: '80vh' }} />
          ) : (
            <Typography variant="body1">Preview not available</Typography>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={replaceDialogOpen} onClose={handleCancelReplace}>
        <DialogTitle>Replace Existing File?</DialogTitle>
        <DialogContent>
          <Typography>Do you want to replace the existing file with "{newFile?.name}"?</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelReplace} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleConfirmReplace} color="primary" variant="contained">
            Replace
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
}
