import React, { useState, useMemo, useEffect } from 'react';
import { Box, Button, Divider, Grid, IconButton, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MainCard from 'components/MainCard';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import { useTheme } from '@mui/material/styles';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import { getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import { useForm } from 'react-hook-form';
const API_URL = import.meta.env.VITE_APP_API_URL;

const ViewActivities = () => {
  const { control } = useForm();
  const location = useLocation();
  const viewData = location.state?.row?.original;

  const clientID = viewData?.id;
  console.log('this is client id', clientID);
  const theme = useTheme();

  const [contacts, setContacts] = useState([
    {
      dateTime: '2025-04-30 14:32',
      description:
        'Sent by John <NAME_EMAIL> regarding project deadline report. Location: 90210 / California / Project X. Contact: <EMAIL>, +1 555-123-4567'
    },
    {
      dateTime: '2025-05-01 09:45',
      description:
        'Sent by Alice <NAME_EMAIL> regarding Q2 financial forecast. Location: 10001 / New York / Finance. Contact: <EMAIL>, +1 555-987-6543'
    },
    {
      dateTime: '2025-05-02 16:10',
      description:
        'Sent by Bob <NAME_EMAIL> regarding contract review summary. Location: 60614 / Chicago / Legal. Contact: <EMAIL>, +1 555-222-3333'
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [clientJobRequest, setClientJobRequest] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const fetchclientjobrequest = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/job-requests/client/${clientID}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      console.log('this is jsonrequest', response);
      const data = await response.json();

      setClientJobRequest(data);
    } catch (error) {
      console.error('Error fetching job requests:', error);
    } finally {
      setLoading(false); // stop spinner
    }
  };

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const columns = useMemo(
    () => [
      {
        header: 'Date & Time',
        accessorKey: 'dateTime',
        cell: ({ getValue }) => {
          const dateTime = getValue();
          return (
            <Tooltip title={dateTime} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {dateTime}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: ({ getValue }) => {
          const description = getValue();
          return (
            <Tooltip title={description} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {description}
              </Typography>
            </Tooltip>
          );
        }
      }
    ],
    []
  );

  const table = useReactTable({
    data: contacts,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection
  });

  const rowButton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          <Spinner />
        </Box>
      ) : (
        <>
          <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', border: 'none' }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Activities
                </Typography>
              </Grid>
            </Grid>
            <Divider sx={{ mt: 1, mb: 3 }} />
            <CustomGrid table={table} rowSelection={rowSelection} theme={theme} />
            <CustomCardHeader secondary={rowButton} />
          </MainCard>
        </>
      )}
    </>
  );
};

export default ViewActivities;
