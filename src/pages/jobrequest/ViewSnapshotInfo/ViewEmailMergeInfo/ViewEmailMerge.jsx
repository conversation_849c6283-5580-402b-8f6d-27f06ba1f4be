import React, { useState, useMemo, useEffect } from 'react';
import { Box, Button, Divider, Grid, IconButton, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MainCard from 'components/MainCard';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import { useTheme } from '@mui/material/styles';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import { getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import { useForm } from 'react-hook-form';
const API_URL = import.meta.env.VITE_APP_API_URL;

const ViewEmailMerge = () => {
  const { control } = useForm();
  const location = useLocation();
  const viewData = location.state?.row?.original;

  const clientID = viewData?.id;
  console.log('this is client id', clientID);
  const theme = useTheme();

  const [contacts, setContacts] = useState([
    {
      sentByOn: 'Alice Johnson / 2025-04-30 10:15 AM',
      sentTo: '<EMAIL>',
      searchString: 'urgent meeting tomorrow',
      zipLocationSubject: '90210 - Project Update',
      emailDetails: {
        email: '<EMAIL>',
        mobile: '******-0123'
      }
    },
    {
      sentByOn: 'Mark Smith / 2025-04-29 03:45 PM',
      sentTo: '<EMAIL>',
      searchString: 'budget Q2 review',
      zipLocationSubject: '10001 - Finance Report',
      emailDetails: {
        email: '<EMAIL>',
        mobile: '******-0456'
      }
    },
    {
      sentByOn: 'Jane Doe / 2025-04-28 09:00 AM',
      sentTo: '<EMAIL>',
      searchString: 'leave application',
      zipLocationSubject: '30301 - HR Request',
      emailDetails: {
        email: '<EMAIL>',
        mobile: '******-0789'
      }
    },
    {
      sentByOn: 'Tom Allen / 2025-05-01 02:30 PM',
      sentTo: '<EMAIL>',
      searchString: 'system outage report',
      zipLocationSubject: '60614 - IT Incident',
      emailDetails: {
        email: '<EMAIL>',
        mobile: '******-0987'
      }
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [clientJobRequest, setClientJobRequest] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const fetchclientjobrequest = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/job-requests/client/${clientID}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      console.log('this is jsonrequest', response);
      const data = await response.json();

      setClientJobRequest(data);
    } catch (error) {
      console.error('Error fetching job requests:', error);
    } finally {
      setLoading(false); // stop spinner
    }
  };

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const columns = useMemo(
    () => [
      {
        header: 'Sent By / On',
        accessorKey: 'sentByOn', // Assuming you combined or formatted this in your data
        cell: ({ getValue }) => {
          const sentByOn = getValue();
          return (
            <Tooltip title={sentByOn} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {sentByOn}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Sent To',
        accessorKey: 'sentTo',
        cell: ({ getValue }) => {
          const sentTo = getValue();
          return (
            <Tooltip title={sentTo} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {sentTo}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Search String',
        accessorKey: 'searchString',
        cell: ({ getValue }) => {
          const searchString = getValue();
          return (
            <Tooltip title={searchString} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {searchString}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Zip Location Subject',
        accessorKey: 'zipLocationSubject',
        cell: ({ getValue }) => {
          const zipLocationSubject = getValue();
          return (
            <Tooltip title={zipLocationSubject} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {zipLocationSubject}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Email Details',
        accessorKey: 'emailDetails',
        cell: ({ getValue }) => {
          const emailDetails = getValue();
          return (
            <Tooltip title={`Email: ${emailDetails.email}, Mobile: ${emailDetails.mobile}`} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {emailDetails.email} / {emailDetails.mobile}
              </Typography>
            </Tooltip>
          );
        }
      }
    ],
    []
  );

  const table = useReactTable({
    data: contacts,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection
  });

  const rowButton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          <Spinner />
        </Box>
      ) : (
        <>
          <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', border: 'none' }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Email Merge
                </Typography>
              </Grid>
            </Grid>
            <Divider sx={{ mt: 0, mb: 3 }} />
            <CustomGrid table={table} rowSelection={rowSelection} theme={theme} />
            <CustomCardHeader secondary={rowButton} />
          </MainCard>
        </>
      )}
    </>
  );
};

export default ViewEmailMerge;
