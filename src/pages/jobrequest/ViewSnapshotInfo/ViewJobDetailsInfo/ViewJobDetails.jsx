import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Divider,
  IconButton,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import axios from 'axios';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';

const ViewJobDetails = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue, reset, getValues } = useForm({
    defaultValues: {
      placement_fee_percentage: '15',
      pricing_model: 'Fixed',
      tvop: '3 months',
      job_start_date: '01/01/2024',
      remote_job: 'Yes',
      required_hours: '40',
      turnaround_time: '2 weeks',
      required_documents: 'Resume, ID Proof',
      end_client: 'Tech Corp',
      additional_details: 'Looking for experienced candidate',
      work_authorization: 'Citizen',
      application_form: 'Standard',
      job_end_date: '12/31/2024',
      respond_by: '12/15/2023',
      expenses_paid: 'Yes',
      client: 'ABC Corp',
      client_manager: 'John Smith',
      priority: 'High',
      ceipad_ref: 'CEI-123',
      interview_mode: 'Virtual',
      address: '123 Business St',
      postal_code: '12345',
      country: 'United States',
      state: 'California',
      city: 'Los Angeles',
    },
  });
  console.log("Value of 'control' immediately after useForm:", control);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);

  const onSubmit = (data) => {
    console.log('Form Data (Submitted):', data);
    setIsEditMode(false);
    // You can perform further actions here, such as sending the data to an API
  };

  const handleEditClick = () => {
    setIsEditMode(true);
  };

  const handleCancelClick = () => {
    setIsEditMode(false);
    reset(); // Reset form to the initially loaded dummy data
  };

  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    // setFormData(getValues()); // This was commented out
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '0px', backgroundColor: 'white', border: 'none' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Job Details</Typography>
          {!isEditMode ? (
            <IconButton onClick={handleEditClick}>
              <EditIcon />
            </IconButton>
          ) : (
            <Box>
              <IconButton type="submit" color="primary">
                <SaveIcon />
              </IconButton>
              <IconButton onClick={handleCancelClick}>
                <CancelIcon />
              </IconButton>
            </Box>
          )}
        </Box>
        <Divider sx={{ marginBottom: 2 }} />
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>
                Placement Fee Percentage <span style={{ color: 'red' }}>*</span>
              </CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="placement_fee_percentage"
                  control={control}
                  placeholder="Enter Placement Fee Percentage"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('placement_fee_percentage')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>
                Pricing Model <span style={{ color: 'red' }}>*</span>
              </CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="pricing_model"
                  control={control}
                  placeholder="Select Pricing Model"
                  options={[
                    { value: 'Fixed' },
                    { value: 'Hourly' },
                    { value: 'Project Based' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('pricing_model')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>
                TVOP <span style={{ color: 'red' }}>*</span>
              </CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="tvop"
                  control={control}
                  placeholder="Enter TVOP"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('tvop')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Job Start Date</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="job_start_date"
                  control={control}
                  placeholder="Enter Job Start Date"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('job_start_date')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Remote Job</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="remote_job"
                  control={control}
                  placeholder="Select Remote Job"
                  options={[{ value: 'Yes' }, { value: 'No' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('remote_job')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Required Hours/Week</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="required_hours"
                  control={control}
                  placeholder="Enter Required Hours"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('required_hours')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Turnaround Time</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="turnaround_time"
                  control={control}
                  placeholder="Enter Turnaround Time"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('turnaround_time')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Required Documents</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="required_documents"
                  control={control}
                  placeholder="Enter Required Documents"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('required_documents')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>End Client</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="end_client"
                  control={control}
                  placeholder="Enter End Client"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('end_client')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Additional Details</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="additional_details"
                  control={control}
                  placeholder="Enter Additional Details"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('additional_details')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Work Authorization</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="work_authorization"
                  control={control}
                  placeholder="Select Work Authorization"
                  options={[
                    { value: 'Citizen' },
                    { value: 'Green Card' },
                    { value: 'H1-B' },
                    { value: 'Other' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('work_authorization')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Application Form</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="application_form"
                  control={control}
                  placeholder="Enter Application Form"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('application_form')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Job End Date</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="job_end_date"
                  control={control}
                  placeholder="Enter Job End Date"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('job_end_date')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Respond By</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="respond_by"
                  control={control}
                  placeholder="Enter Respond By Date"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('respond_by')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Expenses Paid</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="expenses_paid"
                  control={control}
                  placeholder="Select Expenses Paid"
                  options={[{ value: 'Yes' }, { value: 'No' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('expenses_paid')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Client</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="client"
                  control={control}
                  placeholder="Enter Client"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('client')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Client Manager</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="client_manager"
                  control={control}
                  placeholder="Enter Client Manager"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('client_manager')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Priority</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="priority"
                  control={control}
                  placeholder="Select Priority"
                  options={[
                    { value: 'High' },
                    { value: 'Medium' },
                    { value: 'Low' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('priority')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>CEIPAD Ref</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="ceipad_ref"
                  control={control}
                  placeholder="Enter CEIPAD Ref"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('ceipad_ref')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Interview Mode</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="interview_mode"
                  control={control}
                  placeholder="Select Interview Mode"
                  options={[
                    { value: 'Virtual' },
                    { value: 'In-Person' },
                    { value: 'Hybrid' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('interview_mode')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Address</CustomInputLabel>
              {isEditMode ? (
                <CustomAddressField
                  name="address"
                  control={control}
                  placeholder="Enter Address"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('address')}</Typography>
              )}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Postal Code</CustomInputLabel>
              {isEditMode ? (
                <CustomPostalCodeField
                  name="postal_code"
                  control={control}
                  placeholder="Enter Postal Code"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('postal_code')}</Typography>
              )}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Country</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="country"
                  control={control}
                  placeholder="Select Country"
                  options={countriesOptions}
                  onChange={handleCountryChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('country')}</Typography>
              )}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>State/Province</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="state"
                  control={control}
                  placeholder="Select State/Province"
                  options={statesOptions}
                  onChange={handleStateChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('state')}</Typography>
              )}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={4} lg={4} md={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>City</CustomInputLabel>
              {isEditMode ? (
                <CustomAllCharactersField
                  name="city"
                  control={control}
                  placeholder="Enter City"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('city')}</Typography>
              )}
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
    </form>
  );
};

export default ViewJobDetails;