// function ViewSnapshot() {
//   // This is the default export
//   return <></>;
// }
// export default ViewSnapshot;
import React, { useState, useMemo, useEffect } from 'react';
import { Box, Button, Divider, Grid, IconButton, Menu, MenuItem, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MainCard from 'components/MainCard';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import { useTheme } from '@mui/material/styles';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import Stack from '@mui/material/Stack';
import { getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
const API_URL = import.meta.env.VITE_APP_API_URL;
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MoreVertIcon from '@mui/icons-material/MoreVert';
const ViewNotes = () => {
  const { control } = useForm();
  const location = useLocation();
  const viewData = location.state?.row?.original;

  const clientID = viewData?.id;
  console.log('this is client id', clientID);
  const theme = useTheme();

  const [contacts, setContacts] = useState([
    {
      name: 'John Doe',
      addedByOn: 'Jane Smith / 2025-04-28',
      notes: 'Candidate has excellent communication skills and a strong background in sales.'
    },
    {
      name: 'Alice Johnson',
      addedByOn: 'Mark Taylor / 2025-04-27',
      notes: 'Referred by a current employee. Highly recommended for customer success role.'
    },
    {
      name: 'Michael Green',
      addedByOn: 'Samantha Brown / 2025-04-26',
      notes: 'Limited experience, but shows great potential based on interview.'
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [clientJobRequest, setClientJobRequest] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const fetchclientjobrequest = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/job-requests/client/${clientID}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      console.log('this is jsonrequest', response);
      const data = await response.json();

      setClientJobRequest(data);
    } catch (error) {
      console.error('Error fetching job requests:', error);
    } finally {
      setLoading(false); // stop spinner
    }
  };

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const columns = useMemo(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        cell: ({ getValue }) => {
          const name = getValue();
          return (
            <Tooltip title={name} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {name}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Added By/On',
        accessorKey: 'addedByOn',
        cell: ({ getValue }) => {
          const added = getValue();
          return (
            <Tooltip title={added} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {added}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Notes/Description',
        accessorKey: 'notes',
        cell: ({ getValue }) => {
          const notes = getValue();
          return (
            <Tooltip title={notes} placement="bottom">
              <Typography
                variant="body2"
                sx={{ display: 'inline-block', maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
              >
                {notes}
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: 'Actions',
      //   cell: ({ row }) => (
      //     <>
      //       <IconButton onClick={() => handleEdit(row.original)}>
      //         <ModeEditIcon />
      //       </IconButton>
      //       <IconButton onClick={() => handleDelete(row.original.id)}>
      //         {' '}
      //         {/* ✅ Corrected */}
      //         <DeleteIcon />
      //       </IconButton>
      //     </>
      //   )
      // },
      {
        header: 'Menu',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);

          const handleClick = (event) => {
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          const handleEdit = () => {
            handleClose();
            // Call your edit function
            console.log('Edit', row.original);
          };

          const handleView = () => {
            handleClose();
            // Call your view function
            console.log('View', row.original);
          };

          return (
            <>
              <IconButton onClick={handleClick}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
              >
                <MenuItem onClick={handleEdit}>Edit</MenuItem>
                <MenuItem onClick={handleView}>View</MenuItem>
              </Menu>
            </>
          );
        }
      }
    ],
    []
  );

  const table = useReactTable({
    data: contacts,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection
  });

  const rowButton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          <Spinner />
        </Box>
      ) : (
        <>
          <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', border: 'none', mt: 3, padding: 2 }}>
            <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
              <Grid item xs>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Notes
                </Typography>
              </Grid>

              {/* 👇 This item is now flex-grow: 1, so it takes space and aligns to the left */}
              {/* <Grid item sx={{ flexGrow: 12 }}>
                <Grid container spacing={1}>
                  <Grid item>
                    <Button variant="outlined" color="primary" size="small" sx={{ borderRadius: 20 }}>
                      Job Posting
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="contained" size="small" color="primary" sx={{ borderRadius: 20 }}>
                      Application Reference
                    </Button>
                  </Grid>
                </Grid>
              </Grid> */}

              {/* Right-aligned Add button */}
              <Grid item>
                <Button variant="contained" color="primary" size="small">
                  Add
                </Button>
              </Grid>
            </Grid>
            <Divider sx={{ mt: -1, mb: 3 }} />

            <CustomGrid table={table} rowSelection={rowSelection} theme={theme} />
            <CustomCardHeader secondary={rowButton} />
          </MainCard>
        </>
      )}
    </>
  );
};

export default ViewNotes;
