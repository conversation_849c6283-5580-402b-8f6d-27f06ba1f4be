// function ViewSnapshot() {
//   // This is the default export
//   return <></>;
// }
// export default ViewSnapshot;
import React, { useState, useMemo, useEffect } from 'react';
import { Box, Button, Divider, Grid, IconButton, Typography } from '@mui/material';
import { useLocation } from 'react-router-dom';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MainCard from 'components/MainCard';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import { useTheme } from '@mui/material/styles';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import Stack from '@mui/material/Stack';
import { getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm } from 'react-hook-form';
const API_URL = import.meta.env.VITE_APP_API_URL;
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

const ViewSubmission = () => {
  const { control } = useForm();
  const location = useLocation();
  const viewData = location.state?.row?.original;

  const clientID = viewData?.id;
  console.log('this is client id', clientID);
  const theme = useTheme();

  const [contacts, setContacts] = useState([
    {
      name: 'Alice Johnson',
      submittedByOn: 'John Smith / 2025-04-29',
      payRate: '$45/hr',
      status: 'Approved'
    },
    {
      name: 'Bob Williams',
      submittedByOn: 'Sara Lee / 2025-04-27',
      payRate: '$38/hr',
      status: 'Pending'
    },
    {
      name: 'Clara Davis',
      submittedByOn: 'Michael Chen / 2025-04-30',
      payRate: '$50/hr',
      status: 'Rejected'
    },
    {
      name: 'David Thompson',
      submittedByOn: 'Emily Rose / 2025-05-01',
      payRate: '$42/hr',
      status: 'Approved'
    }
  ]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [sorting, setSorting] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [clientJobRequest, setClientJobRequest] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const fetchclientjobrequest = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/job-requests/client/${clientID}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      console.log('this is jsonrequest', response);
      const data = await response.json();

      setClientJobRequest(data);
    } catch (error) {
      console.error('Error fetching job requests:', error);
    } finally {
      setLoading(false); // stop spinner
    }
  };

  useEffect(() => {
    if (clientID) {
      fetchclientjobrequest();
    }
  }, [clientID]);

  const columns = useMemo(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        cell: ({ getValue }) => {
          const name = getValue();
          return (
            <Tooltip title={name} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {name}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Submitted By/On',
        accessorKey: 'submittedByOn',
        cell: ({ getValue }) => {
          const submitted = getValue();
          return (
            <Tooltip title={submitted} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {submitted}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Pay Rate',
        accessorKey: 'payRate',
        cell: ({ getValue }) => {
          const payRate = getValue();
          return (
            <Tooltip title={payRate} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {payRate}
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ getValue }) => {
          const status = getValue();
          return (
            <Tooltip title={status} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                {status}
              </Typography>
            </Tooltip>
          );
        }
      }
    ],
    []
  );

  const table = useReactTable({
    data: contacts,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection
  });

  const rowButton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  return (
    <>
      {loading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60vh'
          }}
        >
          <Spinner />
        </Box>
      ) : (
        <>
          <MainCard sx={{ borderRadius: 0, backgroundColor: 'white', border: 'none', mt: 3, padding: 2 }}>
            <Grid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
              <Grid item xs>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Submission
                </Typography>
              </Grid>
              {/* <Grid item>
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={2} xl={2}>
                    <Stack spacing={1}>
                      <CustomNameField name="name" control={control} placeholder="Search" />
                    </Stack>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Pipeline
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="contained" color="primary" sx={{ borderRadius: 20 }}>
                      All
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Client Submissions
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Interviews
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Confirmations
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Placements
                    </Button>
                  </Grid>
                  <Grid item>
                    <Button variant="outlined" color="primary" sx={{ borderRadius: 20 }}>
                      Not Joined
                    </Button>
                  </Grid>
                </Grid>
              </Grid> */}
            </Grid>
            <Divider sx={{ mt: 0, mb: 3 }} />

            <CustomGrid table={table} rowSelection={rowSelection} theme={theme} />
            <CustomCardHeader secondary={rowButton} />
          </MainCard>
        </>
      )}
    </>
  );
};

export default ViewSubmission;
