import React, { useEffect, useState } from 'react';
import { Box, Grid, Typography, Divider, InputLabel, <PERSON>ton, Card, FormControlLabel, RadioGroup, Radio } from '@mui/material';
import { useForm } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
// import InputLabel from 'components/custom-components/InputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomA<PERSON><PERSON>haractersField from 'components/custom-components/CustomAllCharactersField';
import CustomClientName from 'components/custom-components/CustomClientName';
import CustomCurrencyInputField from 'components/custom-components/CustomCurrencyInputField';
import CustomDateOfBirth from 'components/custom-components/CustomDateOfBirth';
import CustomExperience from 'components/custom-components/CustomExperience';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { Controller } from 'react-hook-form';

const AddJobDetails = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control } = useForm();

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [clientManagers, setClientManagers] = useState([]); // State for client manager options

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/country`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };

    const fetchStates = async () => {
      try {
        const response = await axios.get(`${API_URL}/state`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };

    // Fetch client managers - placeholder data for now
    const fetchClientManagers = () => {
      setClientManagers([
        { value: 'manager1', label: 'Client Manager One' },
        { value: 'manager2', label: 'Client Manager Two' },
        { value: 'manager3', label: 'Client Manager Three' },
      ]);
    };

    fetchCountries();
    fetchStates();
    fetchClientManagers();

  }, []);

  const countryOptions = countries.map(country => ({ value: country.code, label: country.name })); // Adjust mapping based on API response structure
  const stateOptions = states.map(state => ({ value: state.code, label: state.name }));     // Adjust mapping based on API response structure
  const clientManagerOptions = clientManagers.map(manager => ({ value: manager.value, label: manager.label }));


  const onSubmit = (data) => {
    console.log('Form Data:', data);
    // You can perform further actions here, such as sending the data to an API
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title="Job Details"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt:-2 }}
      >
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Job Details</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} /> */}
        <Grid container spacing={3}>
          {/* <Grid item xs={12} sm={6} lg={6} md={6} xl={6}>
            <Stack spacing={1}>
              <InputLabel>
                Business Unit <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="business_unit"
                control={control}
                placeholder="Select"
                options={[
                  { value: 'IT', label: 'Information Technology' },
                  { value: 'Healthcare', label: 'Healthcare' },
                  { value: 'Finance', label: 'Finance' },
                  { value: 'Manufacturing', label: 'Manufacturing' },
                  { value: 'Retail', label: 'Retail' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid> */}

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Job Code <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
              </InputLabel>
              <CustomNameField
                name="job_code"
                control={control}
                placeholder="Enter Job Code "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Job Title <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
              </InputLabel>
              <CustomNameField
                name="job_title"
                control={control}
                placeholder="Enter Job Title "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Client Bill Rate/salary </InputLabel>
              {/* Use CustomCurrencyInputField for Client Bill Rate */}
              <CustomCurrencyInputField
                name="client_bill_rate"
                control={control}
                placeholder="Enter Client Bill Rate"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Pay Rate/salary <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
              </InputLabel>
              {/* Use CustomCurrencyInputField for Pay Rate */}
              <CustomCurrencyInputField
                name="pay_rate"
                control={control}
                placeholder="Enter Pay Rate"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Job Start Date </InputLabel>
              {/* Use CustomDateOfBirth for Job Start Date */}
              <CustomDateOfBirth
                name="job_start_date"
                control={control}
                placeholder="Select Job Start Date"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Job End date </InputLabel>
              {/* Use CustomDateOfBirth for Job End Date */}
              <CustomDateOfBirth
                name="job_end_date"
                control={control}
                placeholder="Select Job End date"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Respond By</InputLabel>
              <CustomDropdownField
                name="respond_by"
                control={control}
                placeholder="Select Respond By"
                options={[{ value: '1 Day' }, { value: '2 Days' }, { value: '3 Days' }, { value: '5 Days' }, { value: '1 Week' }]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Remote job</InputLabel>
              <Controller
                name="remote_job"
                control={control}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                    <FormControlLabel value="no" control={<Radio />} label="No" />
                    <FormControlLabel value="hybrid" control={<Radio />} label="Hybrid" />
                  </RadioGroup>
                )}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Expenses paid </InputLabel>
              <Controller
                name="expenses_paid"
                control={control}
                render={({ field }) => (
                  <RadioGroup {...field} row>
                    <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                    <FormControlLabel value="no" control={<Radio />} label="No" />
                    <FormControlLabel value="partial" control={<Radio />} label="Partial" />
                  </RadioGroup>
                )}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Country <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="country"
                control={control}
                placeholder="Select Country"
                options={countryOptions}
                rules={{ required: 'Country is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                State <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="state"
                control={control}
                placeholder="Select State"
                options={stateOptions}
                rules={{ required: 'State is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Location <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomAllCharactersField
                name="location "
                control={control}
                placeholder="Enter Location"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Job Status <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="job_status"
                control={control}
                placeholder="Select Job Status"
                rules={{ required: 'Job Status is required' }}
                options={[
                  { value: 'Active', label: 'Active' },
                  { value: 'Closed', label: 'Closed' },
                  { value: 'Filled', label: 'Filled' },
                  { value: 'Hold by Client', label: 'Hold by Client' },
                  { value: 'On Hold', label: 'On Hold' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Job Type <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="job_type"
                control={control}
                placeholder="Select Job Type"
                rules={{ required: 'Job Type is required' }}
                options={[
                  { value: 'Full Time', label: 'Full Time' },
                  { value: 'Part Time', label: 'Part Time' },
                  { value: 'Contract', label: 'Contract' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Required Hours/Week </InputLabel>
              <CustomExperience
                name="hours_week"
                control={control}
                placeholder="Enter Required Hours/Week"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Client <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="client"
                control={control}
                placeholder="Select Client"
                options={[
                  { value: 'client1', label: 'Client One' },
                  { value: 'client2', label: 'Client Two' },
                  { value: 'client3', label: 'Client Three' },
                  { value: 'client4', label: 'Client Four' },
                  { value: 'client5', label: 'Client Five' }
                ]}
                rules={{ required: 'Client is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Client Manager</InputLabel>
              <CustomDropdownField
                name="client_manager"
                control={control}
                placeholder="Select Client Manager"
                options={clientManagerOptions}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Client Job Id</InputLabel>
              <CustomAllCharactersField
                name="client_job_id"
                control={control}
                placeholder="Enter Client Job Id"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Priority</InputLabel>
              <CustomDropdownField
                name="priority"
                control={control}
                placeholder="Select Priority"
                options={[
                  { value: 'High', label: 'High Priority' },
                  { value: 'Medium', label: 'Medium Priority' },
                  { value: 'Low', label: 'Low Priority' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Client Category</InputLabel>
              <CustomDropdownField
                name="client_category"
                control={control}
                placeholder="Select Client Category"
                options={[
                  { value: 'Enterprise', label: 'Enterprise Client' },
                  { value: 'MidMarket', label: 'Mid-Market Client' },
                  { value: 'SmallBusiness', label: 'Small Business Client' },
                  { value: 'Startup', label: 'Startup Client' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Duration</InputLabel>
              <CustomAllCharactersField
                name="duration"
                control={control}
                placeholder="Enter Duration"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Additional Details</InputLabel>
              <CustomAllCharactersField
                name="additional_details"
                control={control}
                placeholder="Enter Additional Details"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Work Authorization</InputLabel>
              <CustomDropdownField
                name="work_authorization"
                control={control}
                placeholder="Select Work Authorization"
                options={[
                  { value: 'USCitizen', label: 'US Citizen' },
                  { value: 'GreenCard', label: 'Green Card Holder' },
                  { value: 'H1B', label: 'H1B Visa' },
                  { value: 'OPT', label: 'OPT Status' },
                  { value: 'CPT', label: 'CPT Status' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Ceipal Ref</InputLabel>
              <CustomAllCharactersField
                name="ceipal_ref"
                control={control}
                placeholder="Enter Ceipal Ref"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Interview Mode</InputLabel>
              <CustomAllCharactersField
                name="interview_mode"
                control={control}
                placeholder="Enter Interview Mode"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Application Form</InputLabel>
              <CustomDropdownField
                name="application_form"
                control={control}
                placeholder="Select Application Form"
                options={[
                  { value: 'Standard', label: 'Standard Form' },
                  { value: 'Custom', label: 'Custom Form' },
                  { value: 'None', label: 'No Form Required' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address</InputLabel>
              <CustomAllCharactersField
                name="address"
                control={control}
                placeholder="Enter Address"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Placement Fee Percentage</InputLabel>
              <CustomAllCharactersField
                name="placement_fee_percentage"
                control={control}
                placeholder="Enter Placement Fee Percentage"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Placement Type</InputLabel>
              <CustomDropdownField
                name="placement_type"
                control={control}
                placeholder="Select Placement Type"
                options={[
                  { value: 'DirectHire', label: 'Direct Hire' },
                  { value: 'Contract', label: 'Contract' },
                  { value: 'ContractToHire', label: 'Contract to Hire' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Commercial Model</InputLabel>
              <CustomDropdownField
                name="commercial_model"
                control={control}
                placeholder="Select Commercial Model"
                options={[
                  { value: 'FixedPrice', label: 'Fixed Price Model' },
                  { value: 'TimeAndMaterial', label: 'Time and Material Model' },
                  { value: 'MilestoneBased', label: 'Milestone Based Model' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>TVOP</InputLabel>
              <CustomAllCharactersField
                name="tvop"
                control={control}
                placeholder="Enter TVOP"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Domain</InputLabel>
              <CustomDropdownField
                name="domain"
                control={control}
                placeholder="Select Domain"
                options={[
                  { value: 'IT', label: 'Information Technology' },
                  { value: 'Healthcare', label: 'Healthcare' },
                  { value: 'Finance', label: 'Finance' },
                  { value: 'Manufacturing', label: 'Manufacturing' },
                  { value: 'Retail', label: 'Retail' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Project Type</InputLabel>
              <CustomDropdownField
                name="project_type"
                control={control}
                placeholder="Select Project Type"
                options={[
                  { value: 'Development', label: 'Development Project' },
                  { value: 'Maintenance', label: 'Maintenance Project' },
                  { value: 'Support', label: 'Support Project' },
                  { value: 'Implementation', label: 'Implementation Project' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Notice Period</InputLabel>
              <CustomDropdownField
                name="notice_period"
                control={control}
                placeholder="Select Notice Period"
                options={[
                  { value: 'Immediate', label: 'Immediate Joining' },
                  { value: '15Days', label: '15 Days Notice' },
                  { value: '30Days', label: '30 Days Notice' },
                  { value: '60Days', label: '60 Days Notice' },
                  { value: '90Days', label: '90 Days Notice' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Job Category</InputLabel>
              <CustomDropdownField
                name="job_category"
                control={control}
                placeholder="Select Job Category"
                options={[
                  { value: 'FullTime', label: 'Full Time Position' },
                  { value: 'PartTime', label: 'Part Time Position' },
                  { value: 'Contract', label: 'Contract Position' },
                  { value: 'Temporary', label: 'Temporary Position' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
        {/* <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2} sx={{ mt: 2.5 }}>
          <Button variant="outlined" size="medium" component={Link} to="/jobrequest">
            Cancel
          </Button>
          <Button variant="contained" size="medium">
            Submit
          </Button>
        </Stack> */}
      </MainCard>
    </form>
  );
};

export default AddJobDetails;
