import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import axios from 'axios';
// import  InputLabel from 'components/custom-components/ InputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomOfficeNumberField from 'components/custom-components/CustomOfficeNumberField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
import CustomAadharCardInput from 'components/custom-components/CustomAadharCardInput';
import CustomPanCardInput from 'components/custom-components/CustomPanCardInput';
import CustomCgpaInput from 'components/custom-components/CustomCgpaInput';
import CustomNumberInput from 'components/custom-components/CustomNumberInput';
import DropzonePage from 'pages/client-page/add/file/AddFileInfo';
import { Link } from 'react-router-dom';
import ApplicantDropzonePage from '../../UploadDocument';

const AddOrganizationalInformation = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue } = useForm();
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [industries, setIndustries] = useState([]);

  const onSubmit = (data) => {
    console.log('Form Data:', data);
    // You can perform further actions here, such as sending the data to an API
  };
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    setFormData(getValues());
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);

  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        const response = await axios.get(`${API_URL}/industry`);
        console.log('📥 Industry API Response:', response.data);
        setIndustries(response.data);
      } catch (error) {
        console.error('❌ Error fetching industries:', error);
      }
    };
    fetchIndustries();
  }, []);

  const industryOptions = industries.map((industry) => ({
    value: industry.name // Adjust based on actual API response
  }));

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title="Organizational Information"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: -2 }}
      >
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Organizational Information</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} /> */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Number Of Positions </InputLabel>
              <CustomNumberInput
                name="number_of_positions"
                control={control}
                placeholder="Enter Number Of Positions"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Max Allowed Submissions </InputLabel>
              <CustomNumberInput
                name="submissions"
                control={control}
                placeholder="Enter Max Allowed Submissions"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Tax Terms </InputLabel>
              <CustomDropdownField
                name="tax_terms"
                control={control}
                placeholder="Select Tax Terms"
                options={[
                  { value: 'Full Time' },
                  { value: 'Part Time' },
                  { value: 'Seasonal' },
                  { value: 'W-2' },
                  { value: 'Intern' },
                  { value: 'C2H' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Sales Manager </InputLabel>
              <CustomNameField
                name="sales_manager"
                control={control}
                placeholder="Enter Sales Manager"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Department </InputLabel>
              <CustomNameField
                name="department"
                control={control}
                placeholder="Enter Department"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Recruitment Manager </InputLabel>
              <CustomNameField
                name="recruitment_manager"
                control={control}
                placeholder="Enter Recruitment Manager"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Account Manager </InputLabel>
              <CustomNameField
                name="account_manager"
                control={control}
                placeholder="Enter Account Manager"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Assigned to </InputLabel>
              <CustomNameField
                name="assigned_to"
                control={control}
                placeholder="Enter Assigned to"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Primary Recruiter </InputLabel>
              <CustomNameField
                name="primary_recruiter"
                control={control}
                placeholder="Enter Primary Recruiter"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Notice Period </InputLabel>
              <CustomDropdownField
                name="notice_period"
                control={control}
                placeholder="Select Notice Period"
                options={[
                  { value: '15 Days or Less' },
                  { value: '1 Month' },
                  { value: '2 Months' },
                  { value: '3 Months' },
                  { value: 'More Than 3 Months' },
                  { value: 'Currently Serving Notice' },
                  { value: '7 Days' },
                  { value: '45 Days' },
                  { value: 'Immediate Joining' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Comments </InputLabel>
              <CustomAddressField
                name="comments"
                control={control}
                placeholder="Enter Comments"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Career Portal Published Date </InputLabel>
              <CustomNameField
                name="portal_date"
                control={control}
                placeholder="Enter Career Portal Published Date"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Additional Notifications </InputLabel>
              <CustomDropdownField
                name="notifications"
                control={control}
                placeholder="Select Additional Notifications"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Job Description </InputLabel>
              <CustomAddressField
                name="job_description"
                control={control}
                placeholder="Enter Job Description"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
      <MainCard title="Attachments" sx={{ backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}>
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5" sx={{ marginTop: 3 }}>
            Attachments
          </Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} /> */}
        {/* <Grid item xs={12} sm={10} xl={10} lg={6} md={10}>
          <Stack spacing={1}>
            <Typography color="secondary">Upload Document</Typography>
            <ApplicantDropzonePage
              name="document"
              control={control}
              placeholder="Upload Document"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            ></ApplicantDropzonePage>
          </Stack>
        </Grid> */}
        <Grid xs={12} sm={12}>
          <Stack spacing={1}>
            <InputLabel>Upload Document </InputLabel>
            <ApplicantDropzonePage
              name="document"
              control={control}
              placeholder="Upload Document"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            ></ApplicantDropzonePage>
          </Stack>
        </Grid>
        {/* <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2} sx={{ mt: 2.5 }}>
          <Button variant="outlined" size="medium" component={Link} to="/jobrequest">
            Cancel
          </Button>
          <Button variant="contained" size="medium">
            Submit
          </Button>
        </Stack> */}
      </MainCard>
    </form>
  );
};

export default AddOrganizationalInformation;
