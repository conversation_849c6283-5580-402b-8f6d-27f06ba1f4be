import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  List,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Box,
  Grid,
  Typography,
  Button,
  Snackbar,
  Alert,
  CardActions,
  Stack,
  Tabs,
  Tab,
  useMediaQuery,
  Divider,
  CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

// Icons
import { Profile } from 'iconsax-react';
import ContactsIcon from '@mui/icons-material/Contacts';
import WorkIcon from '@mui/icons-material/Work';

// Components
import MainCard from 'components/MainCard';
import EditJobDetails from '../jobdetails/EditJobDetails';
import EditSkills from '../skills/EditSkills';
import EditOrganizationalInformation from '../organizationinformation/EditOrganizationalInformation';
import { HEADER_HEIGHT } from 'config';

const tabLabels = [
  { label: 'Job Details', icon: <Profile size={18} /> },
  { label: 'Skills', icon: <ContactsIcon /> },
  { label: 'Organizational Info', icon: <WorkIcon /> }
];

function EditJobRequest() {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedTab, setSelectedTab] = useState(0);
  const [loading, setLoading] = useState(true);

  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  const [validateTabForm, setValidateTabForm] = useState(null);
  const [submitForm, setSubmitForm] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  const handleSnackbarClose = () => setOpenSnackbar(false);

  const handleTabChange = async (event, newValue) => {
    if (validateTabForm) {
      const isValid = await validateTabForm();
      if (!isValid) {
        setSnackbarMessage('Please fix errors before changing tab.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
        return;
      }
    }
    setSelectedTab(newValue);
  };

  const handleListItemClick = async (index) => {
    if (validateTabForm) {
      const isValid = await validateTabForm();
      if (!isValid) {
        setSnackbarMessage('Please fix errors before changing tab.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
        return;
      }
    }
    setSelectedTab(index);
  };

  const handleFormSubmit = async () => {
    if (validateTabForm) {
      const isValid = await validateTabForm();
      if (!isValid) {
        setSnackbarMessage('Please fix form errors before submitting.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
        return;
      }
    }
    setSubmitForm(true);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <EditJobDetails />;
      case 1:
        return <EditSkills />;
      case 2:
        return <EditOrganizationalInformation />;
      default:
        return null;
    }
  };

  return (
    <>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <CardActions
            sx={{
              position: 'sticky',
              top: 0,
              bgcolor: 'background.default',
              zIndex: 1100,
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                Edit Job Posting
              </Typography>
              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                <Button variant="outlined" size="small" component={Link} to="/jobrequest">
                  Cancel
                </Button>
                <Button variant="contained" size="small" onClick={handleFormSubmit}>
                  Submit
                </Button>
              </Stack>
            </Stack>
          </CardActions>
          <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
            {isSmallScreen ? (
              <>
                <Box sx={{ bgcolor: 'background.default', position: 'sticky', top: 60, zIndex: 80 }}>
                  <Tabs
                    value={selectedTab}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    aria-label="responsive edit tabs"
                  >
                    {tabLabels.map((tab, index) => (
                      <Tab key={index} label={tab.label} icon={tab.icon} iconPosition="start" />
                    ))}
                  </Tabs>
                </Box>
                <Box sx={{ mt: 2 }}>{renderTabContent()}</Box>
              </>
            ) : (
              <Grid container spacing={3}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  lg={3}
                  md={3.4}
                  xl={3}
                  sx={{
                    position: 'sticky',
                    top: 35,
                    alignSelf: 'flex-start',
                    zIndex: 80
                  }}
                >
                  <MainCard sx={{ borderRadius: '2%', mt: 2 }}>
                    <List component="nav">
                      {tabLabels.map((tab, index) => (
                        <ListItemButton key={index} selected={selectedTab === index} onClick={() => handleListItemClick(index)}>
                          <ListItemIcon>{tab.icon}</ListItemIcon>
                          <ListItemText primary={tab.label} />
                        </ListItemButton>
                      ))}
                    </List>
                  </MainCard>
                </Grid>
                <Grid item xs>
                  <Box sx={{ mt: 2 }}>{renderTabContent()}</Box>
                </Grid>
              </Grid>
            )}
          </MainCard>
          <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
            <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </>
      )}
    </>
  );
}

export default EditJobRequest;
