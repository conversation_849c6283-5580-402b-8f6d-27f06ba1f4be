import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import axios from 'axios';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomOfficeNumberField from 'components/custom-components/CustomOfficeNumberField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
import CustomAadharCardInput from 'components/custom-components/CustomAadharCardInput';
import CustomPanCardInput from 'components/custom-components/CustomPanCardInput';
import CustomCgpaInput from 'components/custom-components/CustomCgpaInput';
import CustomExperience from 'components/custom-components/CustomExperience';
import CustomMultiTagInput from 'components/custom-components/CustomMultiTagInput';
import { Link } from 'react-router-dom';

const EditSkills = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue } = useForm();
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [industries, setIndustries] = useState([]);

  const onSubmit = (data) => {
    console.log('Form Data:', data);
    // You can perform further actions here, such as sending the data to an API
  };
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    setFormData(getValues());
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);

  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        const response = await axios.get(`${API_URL}/industry`);
        console.log('📥 Industry API Response:', response.data);
        setIndustries(response.data);
      } catch (error) {
        console.error('❌ Error fetching industries:', error);
      }
    };
    fetchIndustries();
  }, []);

  const industryOptions = industries.map((industry) => ({
    value: industry.name // Adjust based on actual API response
  }));

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard title="Skills" sx={{ borderRadius: '1.5%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' } }}>
        {/* <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
              <Typography variant="h5">Skills</Typography>
            </Box>
            <Divider sx={{ marginBottom: 2 }} /> */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="industry">
                Industry <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="industry"
                control={control}
                placeholder="Select Industry"
                onBlur={handleInputChange}
                rules={{ required: 'Industry is required' }}
                options={[
                  { value: 'Information Technology' },
                  { value: 'Healthcare' },
                  { value: 'Finance' },
                  { value: 'Manufacturing' },
                  { value: 'Retail' },
                  { value: 'Education' },
                  { value: 'Telecommunications' },
                  { value: 'Energy' },
                  { value: 'Transportation' },
                  { value: 'Construction' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Degree </InputLabel>
              <CustomDropdownField
                name="degree"
                control={control}
                placeholder="Select Degree"
                options={[
                  { value: 'Bachelor of Technology' },
                  { value: 'Bachelor of Engineering' },
                  { value: 'Bachelor of Science' },
                  { value: 'Bachelor of Computer Applications' },
                  { value: 'Master of Technology' },
                  { value: 'Master of Engineering' },
                  { value: 'Master of Science' },
                  { value: 'Master of Computer Applications' },
                  { value: 'Doctor of Philosophy' },
                  { value: 'Post Graduate Diploma' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Experience </InputLabel>
              <CustomExperience
                name="experience"
                control={control}
                placeholder="Enter Experience"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Primary skills </InputLabel>
              <CustomMultiTagInput
                name="primary_skills"
                control={control}
                placeholder="Enter Primary skills"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Evaluation template </InputLabel>
              <CustomNameField
                name="evaluation"
                control={control}
                placeholder="Enter Evaluation template"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Secondary Skills </InputLabel>
              <CustomMultiTagInput
                name="secondary_skills"
                control={control}
                placeholder="Enter Secondary Skills"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Languages </InputLabel>
              <CustomDropdownField
                name="languages"
                control={control}
                placeholder="Select Languages"
                options={[
                  { value: 'English' },
                  { value: 'Spanish' },
                  { value: 'French' },
                  { value: 'German' },
                  { value: 'Chinese' },
                  { value: 'Japanese' },
                  { value: 'Korean' },
                  { value: 'Russian' },
                  { value: 'Arabic' },
                  { value: 'Hindi' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>

        {/* <Box mt={2}>
              <Button type="submit" variant="contained" color="primary">
                Submit
              </Button>
            </Box> */}
        {/* <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2} sx={{ mt: 2.5 }}>
              <Button variant="outlined" size="medium" component={Link} to="/jobrequest">
                Cancel
              </Button>
              <Button variant="contained" size="medium">
                Submit
              </Button>
            </Stack> */}
      </MainCard>
    </form>
  );
};

export default EditSkills;
