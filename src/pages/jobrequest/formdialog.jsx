// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import TextField from '@mui/material/TextField';
// import MenuItem from '@mui/material/MenuItem';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select from '@mui/material/Select';
// import Grid from '@mui/material/Grid';
// import axios from 'axios';

// export default function JobRequestAddForm({ open, close }) {
//   const [clients, setClients] = useState([]);  // State to hold client data
//   const [selectedClientId, setSelectedClientId] = useState(''); 
//   const [selectedClientName, setSelectedClientName] = useState(''); 

//   useEffect(() => {
//     const fetchClients = async () => {
//       try {
//         const response = await axios.get('http://localhost:8000/clients/', {
//           headers: {
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`, // Authorization header
//           },
//         });
  
//         console.log(response);
//         if (Array.isArray(response.data)) {
//           setClients(response.data);
//         } else {
//           console.error('Unexpected data format:', response.data);
//         }
//       } catch (error) {
//         console.error('Failed to fetch clients:', error);
//       }
//     };
  
//     fetchClients();
//   }, []);

//   const handleInputChange = (event) => {
//     const { name, value } = event.target;
//     setFormData((prevState) => ({
//       ...prevState,
//       [name]: value,
//     }));
//   };

//   const handleClientChange = (event) => {
//     const clientId = event.target.value;
//     const clientName = clients.find(client => client.id === clientId)?.client_name || '';
//     setSelectedClientId(clientId);
//     setSelectedClientName(clientName);
//     setFormData((prevState) => ({
//       ...prevState,
//       client_id: clientId, // Set the client ID
//       client_name: clientName // Set the client name
//     }));
//     console.log('Selected Client ID:', clientId);
//     console.log('Selected Client Name:', clientName);
//   };

//   const handleFileChange = (event) => {
//     setFormData((prevState) => ({
//       ...prevState,
//       document: event.target.files[0],
//     }));
//   };

//   const [formData, setFormData] = useState({
//     client_id: '',
//     client_name: '',
//     job_title: '',
//     job_location: '',
//     pay_range: '',
//     work_mode_type: '',
//     job_type: '',
//     description: '',
//     status: '',
//     primary_skills: '',
//     immediate_joining: '',
//     document: null // for file
//   });
  
//   const handleSubmit = async () => {
//     try {
//       const formDataToSubmit = new FormData();
  
//       for (const key in formData) {
//         if (formData.hasOwnProperty(key)) {
//           formDataToSubmit.append(key, formData[key]);
//         }
//       }
  
//       if (formData.document) {
//         formDataToSubmit.append('document', formData.document);
//       }
  
//       await axios.post('http://localhost:8000/job-requests/', formDataToSubmit, {
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//         },
//       });
  
//       setFormData({
//         client_id: '',
//         client_name: '',
//         job_title: '',
//         job_location: '',
//         pay_range: '',
//         work_mode_type: '',
//         job_type: '',
//         description: '',
//         status: '',
//         primary_skills: '',
//         immediate_joining: '',
//         document: null
//       });
  
//       close(); // Close modal or perform any post-submit action
//     } catch (error) {
//       console.error('Failed to submit job request:', error);
//     }
//   };
  
//   return (
//     <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
//       <DialogTitle>Add Job Request</DialogTitle>
//       <DialogContent>
//         <Grid container spacing={2}>
//           {/* Client Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="client-select-label">Client Name</InputLabel>
//               <Select
//                 labelId="client-select-label"
//                 id="client-select"
//                 value={selectedClientId}
//                 onChange={handleClientChange} // Update client selection handler
//               >
//                 {clients.map((client) => (
//                   <MenuItem key={client.id} value={client.id}>
//                     {client.client_name} {/* Display client name in dropdown */}
//                   </MenuItem>
//                 ))}
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Job Title */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Job Title"
//               name="job_title"
//               fullWidth
//               value={formData.job_title}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* Job Location */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Job Location"
//               name="job_location"
//               fullWidth
//               value={formData.job_location}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* Pay Range Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Pay Range</InputLabel>
//               <Select
//                 name="pay_range"
//                 value={formData.pay_range}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
//                 <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
//                 <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
//                 <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
//                 <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
//                 <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
//                 <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Work Mode Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Work Mode Type</InputLabel>
//               <Select
//                 name="work_mode_type"
//                 value={formData.work_mode_type}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Onsite">Onsite</MenuItem>
//                 <MenuItem value="Hybrid">Hybrid</MenuItem>
//                 <MenuItem value="Remote">Remote</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Job Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Job Type</InputLabel>
//               <Select
//                 name="job_type"
//                 value={formData.job_type}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Permanent">Permanent</MenuItem>
//                 <MenuItem value="Full-time">Full-time</MenuItem>
//                 <MenuItem value="Part-time">Part-time</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Status Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Status</InputLabel>
//               <Select
//                 name="status"
//                 value={formData.status}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Active">Active</MenuItem>
//                 <MenuItem value="Closed">Closed</MenuItem>
//                 <MenuItem value="On Hold">On Hold</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Primary Skills */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Primary Skills"
//               name="primary_skills"
//               fullWidth
//               value={formData.primary_skills}
//               onChange={handleInputChange}
//               placeholder="e.g., Java, Python, Springboot, C, C++"
//             />
//           </Grid>

//           {/* Immediate Joining Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Immediate Joining</InputLabel>
//               <Select
//                 name="immediate_joining"
//                 value={formData.immediate_joining}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Yes">Yes</MenuItem>
//                 <MenuItem value="No">No</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Description */}
//           <Grid item xs={12}>
//             <TextField
//               label="Description"
//               name="description"
//               fullWidth
//               multiline
//               rows={4}
//               value={formData.description}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* Document Upload */}
//           <Grid item xs={12}>
//             <TextField
//               type="file"
//               name="document"
//               fullWidth
//               onChange={handleFileChange}
//             />
//           </Grid>
//         </Grid>
//       </DialogContent>
//       <DialogActions>
//         <Button onClick={close}>Cancel</Button>
//         <Button onClick={handleSubmit} color="primary">Submit</Button>
//       </DialogActions>
//     </Dialog>
//   );
// }

// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import TextField from '@mui/material/TextField';
// import MenuItem from '@mui/material/MenuItem';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select from '@mui/material/Select';
// import Grid from '@mui/material/Grid';
// import axios from 'axios';

// export default function JobRequestAddForm({ open, close,  onJobRequestAdded }) {
//   const [clients, setClients] = useState([]);  // State to hold client data
//   const [selectedClientId, setSelectedClientId] = useState(''); 
//   const [selectedClientName, setSelectedClientName] = useState(''); 

//   useEffect(() => {
//     const fetchClients = async () => {
//       try {
//         const response = await axios.get('http://localhost:8080/client-page', {
//           headers: {
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`, // Authorization header
//           },
//         });
  
//         console.log(response);
//         if (Array.isArray(response.data)) {
//           setClients(response.data);
//         } else {
//           console.error('Unexpected data format:', response.data);
//         }
//       } catch (error) {
//         console.error('Failed to fetch clients:', error);
//       }
//     };
    
//     fetchClients();
//   }, []);

//   const handleInputChange = (event) => {
//     const { name, value } = event.target;
//     setFormData((prevState) => ({
//       ...prevState,
//       [name]: value,
//     }));
//   };

//   const handleClientChange = (event) => {
//     const clientId = event.target.value;
//     const clientName = clients.find(client => client.id === clientId)?.client_name || '';
//     setSelectedClientId(clientId);
//     setSelectedClientName(clientName);
//     setFormData((prevState) => ({
//       ...prevState,
//       client_id: clientId, // Set the client ID
//       client_name: clientName // Set the client name
//     }));
//     console.log('Selected Client ID:', clientId);
//     console.log('Selected Client Name:', clientName);
//   };

//   const handleFileChange = (event) => {
//     const file = event.target.files[0];
//     console.log('Selected file:', file);
//     setFormData((prevState) => ({
//       ...prevState,
//       document: file,
//     }));
//   };

//   const [formData, setFormData] = useState({
//     job_request_id: " ",
//     client_id: '',
//     client_name: '',
//     job_title: '',
//     job_location: '',
//     pay_range: '',
//     work_mode_type: '',
//     job_type: '',
//     description: '',
//     status: '',
//     primary_skills: '',
//     immediate_joining: '',
//     document: null // for file
//   });

//   const handleSubmit = async () => {
//     try {
//       // 1. Submit Job Request Data
//       const jobRequestResponse = await axios.post('http://localhost:8080/job-requests/', {
//         client_id: formData.client_id,
//         client_name: selectedClientName,
//         job_title: formData.job_title,
//         job_location: formData.job_location,
//         pay_range: formData.pay_range,
//         work_mode_type: formData.work_mode_type,
//         job_type: formData.job_type,
//         description: formData.description,
//         status: formData.status,
//         primary_skills: formData.primary_skills,
//         immediate_joining: formData.immediate_joining,
//       }, {
//         headers: {
//           'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//         },
//       });


//       console.log("Job request repsonse :" , jobRequestResponse)
//       // Extract job request ID from response
//       const jobRequestId = jobRequestResponse.data.id;  // Adjust based on your API response
//       console.log("Job request ID:", jobRequestId);

//       const newJobRequest = jobRequestResponse.data;
//       console.log("New job request:", newJobRequest);

      
//       if (onJobRequestAdded) {
//         onJobRequestAdded(newJobRequest);
//       }
//       // 2. Upload Document
//       if (formData.document) {
//         const documentFormData = new FormData();
        
//         // Append the file and job_request_id
//         documentFormData.append('file', formData.document);
//         documentFormData.append('job_request_id', jobRequestId);  // Pass job request ID

//         // Log FormData contents for debugging
//         for (let pair of documentFormData.entries()) {
//           console.log(`${pair[0]}: ${pair[1]}`);
//         }
//         console.log("Documentformdata" , documentFormData)
//         await axios.post('http://localhost:8000/resume-uploads/', documentFormData, {
//           headers: {
//             'Content-Type': 'multipart/form-data',
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//           },
//         });
//       }

//       // Reset form and close dialog
//       setFormData({
//         client_id: '',
//         client_name: '',
//         job_title: '',
//         job_location: '',
//         pay_range: '',
//         work_mode_type: '',
//         job_type: '',
//         description: '',
//         status: '',
//         primary_skills: '',
//         immediate_joining: '',
//         document: null
//       });

//       setSelectedClientId('');
//       setSelectedClientName('');
//       close(); // Close modal or perform any post-submit action
//     } catch (error) {
//       console.error('Failed to submit job request:', error);
//     }
//   };

//   return (
//     <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
//       <DialogTitle>Add Job Request</DialogTitle>
//       <DialogContent>
//         <Grid container spacing={2}>
//           {/* Client Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="client-select-label">Client Name</InputLabel>
//               <Select
//                 labelId="client-select-label"
//                 id="client-select"
//                 value={selectedClientId}
//                 onChange={handleClientChange} // Update client selection handler
//               >
//                 {clients.map((client) => (
//                   <MenuItem key={client.id} value={client.id}>
//                     {client.client_name} {/* Display client name in dropdown */}
//                   </MenuItem>
//                 ))}
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Job Title */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Job Title"
//               name="job_title"
//               fullWidth
//               value={formData.job_title}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* Job Location */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Job Location"
//               name="job_location"
//               fullWidth
//               value={formData.job_location}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* Pay Range Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Pay Range</InputLabel>
//               <Select
//                 name="pay_range"
//                 value={formData.pay_range}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
//                 <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
//                 <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
//                 <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
//                 <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
//                 <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
//                 <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Work Mode Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Work Mode Type</InputLabel>
//               <Select
//                 name="work_mode_type"
//                 value={formData.work_mode_type}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Onsite">Onsite</MenuItem>
//                 <MenuItem value="Hybrid">Hybrid</MenuItem>
//                 <MenuItem value="Remote">Remote</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Job Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Job Type</InputLabel>
//               <Select
//                 name="job_type"
//                 value={formData.job_type}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Permanent">Permanent</MenuItem>
//                 <MenuItem value="Full-time">Full-time</MenuItem>
//                 <MenuItem value="Part-time">Part-time</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Status Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel>Status</InputLabel>
//               <Select
//                 name="status"
//                 value={formData.status}
//                 onChange={handleInputChange}
//               >
//                 <MenuItem value="Active">Active</MenuItem>
//                 <MenuItem value="Closed">Closed</MenuItem>
//                 <MenuItem value="On Hold">On Hold</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>

//           {/* Primary Skills */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Primary Skills"
//               name="primary_skills"
//               fullWidth
//               value={formData.primary_skills}
//               onChange={handleInputChange}
//               placeholder="e.g., JavaScript, React"
//             />
//           </Grid>

//           {/* Immediate Joining */}
//           <Grid item xs={12} sm={6}>
//             <TextField
//               label="Immediate Joining"
//               name="immediate_joining"
//               fullWidth
//               value={formData.immediate_joining}
//               onChange={handleInputChange}
//               placeholder="Yes/No"
//             />
//           </Grid>

//           {/* Job Description */}
//           <Grid item xs={12}>
//             <TextField
//               label="Job Description"
//               name="description"
//               fullWidth
//               multiline
//               rows={4}
//               value={formData.description}
//               onChange={handleInputChange}
//             />
//           </Grid>

//           {/* File Upload */}
//           <Grid item xs={12} sm={12}>
//             <Button variant="contained" component="label" fullWidth>
//               Upload Document
//               <input
//                 type="file"
//                 hidden
//                 onChange={handleFileChange}  // Corrected file change handler
//               />
//             </Button>
//           </Grid>
//         </Grid>
//       </DialogContent>
//       <DialogActions>
//         <Button onClick={close} color="primary">Cancel</Button>
//         <Button onClick={handleSubmit} color="primary">Submit</Button>
//       </DialogActions>
//     </Dialog>
//   );
// }

// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import TextField from '@mui/material/TextField';
// import MenuItem from '@mui/material/MenuItem';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select from '@mui/material/Select';
// import Grid from '@mui/material/Grid';
// import { useForm, Controller } from 'react-hook-form'; // Import Controller
// import axios from 'axios';

// export default function JobRequestAddForm({ open, close, onJobRequestAdded }) {
//   const { control, handleSubmit, setValue } = useForm(); // Initialize useForm
//   const [clients, setClients] = useState([]);  // State to hold client data
//   const [selectedClientId, setSelectedClientId] = useState(''); 
//   const [selectedClientName, setSelectedClientName] = useState(''); 

//   useEffect(() => {
//     const fetchClients = async () => {
//       try {
//         const response = await axios.get('http://localhost:8080/client-page', {
//           headers: {
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`, // Authorization header
//           },
//         });
  
//         console.log(response);
//         if (Array.isArray(response.data)) {
//           setClients(response.data);
//         } else {
//           console.error('Unexpected data format:', response.data);
//         }
//       } catch (error) {
//         console.error('Failed to fetch clients:', error);
//       }
//     };
    
//     fetchClients();
//   }, []);

//   const handleClientChange = (event) => {
//     const clientId = event.target.value;
//     const clientName = clients.find(client => client.id === clientId)?.client_name || '';
//     setSelectedClientId(clientId);
//     setSelectedClientName(clientName);
//     setValue('client_id', clientId); // Set client ID in the form state
//     setValue('client_name', clientName); // Set client name in the form state
//     console.log('Selected Client ID:', clientId);
//     console.log('Selected Client Name:', clientName);
//   };

//   const handleFileChange = (event) => {
//     const file = event.target.files[0];
//     console.log('Selected file:', file);
//     setValue('document', file); // Set file in the form state
//   };

//   const handleSubmitForm = async (formData) => {
//     try {
//       // 1. Submit Job Request Data
//       const jobRequestResponse = await axios.post('http://localhost:8080/job-requests/', {
//         client_id: formData.client_id,
//         client_name: selectedClientName,
//         job_title: formData.job_title,
//         job_location: formData.job_location,
//         pay_range: formData.pay_range,
//         work_mode_type: formData.work_mode_type,
//         job_type: formData.job_type,
//         description: formData.description,
//         status: formData.status,
//         primary_skills: formData.primary_skills,
//         immediate_joining: formData.immediate_joining,
//       }, {
//         headers: {
//           'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//         },
//       });

//       console.log("Job request response:", jobRequestResponse);
//       const jobRequestId = jobRequestResponse.data.id;  // Adjust based on your API response
//       console.log("Job request ID:", jobRequestId);

//       const newJobRequest = jobRequestResponse.data;
//       console.log("New job request:", newJobRequest);
      
//       if (onJobRequestAdded) {
//         onJobRequestAdded(newJobRequest);
//       }

//       // 2. Upload Document
//       if (formData.document) {
//         const documentFormData = new FormData();
//         documentFormData.append('file', formData.document);
//         documentFormData.append('job_request_id', jobRequestId);  // Pass job request ID

//         // Log FormData contents for debugging
//         for (let pair of documentFormData.entries()) {
//           console.log(`${pair[0]}: ${pair[1]}`);
//         }
//         console.log("DocumentFormData:", documentFormData);
//         await axios.post('http://localhost:8000/resume-uploads/', documentFormData, {
//           headers: {
//             'Content-Type': 'multipart/form-data',
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//           },
//         });
//       }

//       // Reset form and close dialog
//       close(); // Close modal or perform any post-submit action
//     } catch (error) {
//       console.error('Failed to submit job request:', error);
//     }
//   };

//   return (
//     <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
     
//       <DialogTitle>Add Job Request</DialogTitle>
//       <DialogContent>
//         <Grid container spacing={3}>
//           {/* Client Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//             <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Client Name</InputLabel>
//               <Controller
//                 name="client_id"
//                 control={control}
               
//                 render={({ field }) => (
//                   <Select
//                     {...field}
//                     labelId="client-select-label"
                     
//                     id="client-select"
//                     value={selectedClientId}
//                     placeholder="Select Client Name"
//                      size="small"
                     
//                     onChange={(e) => {
//                       handleClientChange(e); // Update client selection handler
//                       field.onChange(e.target.value); // Set value in form state
//                     }}
//                   >
//                     <MenuItem value="" disabled>
//                         <em>Select Client Name</em> {/* Placeholder as MenuItem */}
//                     </MenuItem>
//                     {clients.map((client) => (
//                       <MenuItem key={client.id} value={client.id}>
//                         {client.client_name} {/* Display client name in dropdown */}
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Job Title */}
// <Grid item xs={12} sm={6}>
//   <FormControl fullWidth>
//     <Controller
//       name="job_title"
//       control={control}
//       rules={{
//         required: 'Job Title is required', // Add validation rule for required field
//       }}
//       render={({ field }) => (
//         <TextField
//           {...field}
//           label="Job Title"
//           InputLabelProps={{ shrink: true }}
//           size="small"
//           placeholder="Enter job title"
//           fullWidth
//           error={Boolean(errors.job_title)} // Display error state if there's an error
//           helperText={errors.job_title?.message} // Display error message if there's an error
//           onChange={(e) => {
//             // Sanitize input to prevent leading spaces, numeric, and special characters
//             let value = e.target.value
//               .replace(/^\s+/, '') // Remove leading spaces
//               .replace(/[^a-zA-Z\s]/g, ''); // Remove numeric and special characters

//             // Update the field value
//             field.onChange(value);
//           }}
//         />
//       )}
//     />
//   </FormControl>
// </Grid>


//           {/* Job Location */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//             <Controller
//               name="job_location"
//               control={control}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
//                      label="Job Location"
//                     InputLabelProps={{ shrink: true }}
//                    size="small"
//                    placeholder="Enter job location" 
//                   fullWidth
//                 />
//               )}
//             />
//             </FormControl>
//           </Grid>

//           {/* Pay Range Dropdown */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Pay Range</InputLabel>
//             <Controller
//               name="pay_range"
//               control={control}
//               render={({ field }) => (
//                 <FormControl fullWidth>
               
//                   <Select {...field}
//                    size="small"
//                    >
                    
//                     <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
//                     <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
//                     <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
//                     <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
//                     <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
//                     <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
//                     <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
//                   </Select>
//                 </FormControl>
//               )}
//             />
//              </FormControl>
//           </Grid>

//           {/* Work Mode Type Dropdown */}
//           <Grid item xs={12} sm={6}>
         
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Work Mode Type</InputLabel>
//             <Controller
//               name="work_mode_type"
//               control={control}
//               render={({ field }) => (
//                 <FormControl fullWidth>

//                   <Select {...field}
//                    size="small">
//                     <MenuItem value="Onsite">Onsite</MenuItem>
//                     <MenuItem value="Hybrid">Hybrid</MenuItem>
//                     <MenuItem value="Remote">Remote</MenuItem>
//                   </Select>
//                 </FormControl>
//               )}
//             />
//              </FormControl>
//           </Grid>

//           {/* Job Type Dropdown */}
//           <Grid item xs={12} sm={6}>
            
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Job Type</InputLabel>
//             <Controller
//               name="job_type"
//               control={control}
//               render={({ field }) => (
//                 <FormControl fullWidth>
                 
//                   <Select {...field}
//                    size="small">
//                     <MenuItem value="Permanent">Permanent</MenuItem>
//                     <MenuItem value="Full-time">Full-time</MenuItem>
//                     <MenuItem value="Part-time">Part-time</MenuItem>
//                   </Select>
//                 </FormControl>
//               )}
//             />
//              </FormControl>
//           </Grid>

//           {/* Status Dropdown */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Status</InputLabel>
//             <Controller
//               name="status"
//               control={control}
//               render={({ field }) => (
//                 <FormControl fullWidth>
               
//                   <Select {...field}
//                    size="small">
//                     <MenuItem value="Active">Active</MenuItem>
//                     <MenuItem value="Closed">Closed</MenuItem>
//                     <MenuItem value="On Hold">On Hold</MenuItem>
//                   </Select>
//                 </FormControl>
//               )}
//             />
//               </FormControl>
//           </Grid>

//           {/* Primary Skills */}
//           <Grid item xs={12} sm={6}>

//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Primary Skills</InputLabel>
//             <Controller
//               name="primary_skills"
//               control={control}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
       
//                   fullWidth
//                    size="small"
//                   placeholder="e.g., JavaScript, React"
//                 />
//               )}
//             />
//              </FormControl>
//           </Grid>

//           {/* Immediate Joining */}
//           <Grid item xs={12} sm={6}>
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Immediate Joining</InputLabel>
//             <Controller
//               name="immediate_joining"
//               control={control}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
//                    size="small"
          
//                   fullWidth
//                   placeholder="Yes/No"
//                 />
//               )}
//             />
//             </FormControl>
//           </Grid>

//           {/* Job Description */}
//           <Grid item xs={12}>
//           <FormControl fullWidth>
//           <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Job Description </InputLabel>
//             <Controller
//               name="description"
//               control={control}
//               render={({ field }) => (
//                 <TextField
//                   {...field}
//                    size="small"

//                   fullWidth
//                   multiline
//                   rows={4}
//                 />
//               )}
//             />
//         </FormControl>
//           </Grid>

//           {/* File Upload */}
//           <Grid item xs={12} sm={12}>
//             <Button variant="contained" component="label" fullWidth>
//               Upload Document
//               <input
//                 type="file"
//                 hidden
//                 onChange={handleFileChange}  // Corrected file change handler
//               />
//             </Button>
//           </Grid>
//         </Grid>
//       </DialogContent>
//       <DialogActions>
//         <Button onClick={close} color="primary">Cancel</Button>
//         <Button onClick={handleSubmit(handleSubmitForm)} color="primary">Submit</Button>
//       </DialogActions>
//     </Dialog>
//   );
// }


// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import TextField from '@mui/material/TextField';
// import MenuItem from '@mui/material/MenuItem';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select from '@mui/material/Select';
// import Grid from '@mui/material/Grid';
// import { useForm, Controller } from 'react-hook-form'; // Import Controller
// import axios from 'axios';

// export default function JobRequestAddForm({ open, close, onJobRequestAdded }) {
//   const { control, handleSubmit, setValue, formState: { errors } } = useForm(); // Initialize useForm and get errors
//   const [clients, setClients] = useState([]);  // State to hold client data
//   const [selectedClientId, setSelectedClientId] = useState(''); 
//   const [selectedClientName, setSelectedClientName] = useState(''); 

//   useEffect(() => {
//     const fetchClients = async () => {
//       try {
//         const response = await axios.get('http://localhost:8080/client-page', {
//           headers: {
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`, // Authorization header
//           },
//         });
  
//         console.log(response);
//         if (Array.isArray(response.data)) {
//           setClients(response.data);
//         } else {
//           console.error('Unexpected data format:', response.data);
//         }
//       } catch (error) {
//         console.error('Failed to fetch clients:', error);
//       }
//     };
    
//     fetchClients();
//   }, []);

//   const handleClientChange = (event) => {
//     const clientId = event.target.value;
//     const clientName = clients.find(client => client.id === clientId)?.client_name || '';
//     setSelectedClientId(clientId);
//     setSelectedClientName(clientName);
//     setValue('client_id', clientId); // Set client ID in the form state
//     setValue('client_name', clientName); // Set client name in the form state
//     console.log('Selected Client ID:', clientId);
//     console.log('Selected Client Name:', clientName);
//   };

//   const handleFileChange = (event) => {
//     const file = event.target.files[0];
//     console.log('Selected file:', file);
//     setValue('document', file); // Set file in the form state
//   };

//   const handleSubmitForm = async (formData) => {
//     try {
//       // 1. Submit Job Request Data
//       const jobRequestResponse = await axios.post('http://localhost:8080/job-requests/', {
//         client_id: formData.client_id,
//         client_name: selectedClientName,
//         job_title: formData.job_title,
//         job_location: formData.job_location,
//         pay_range: formData.pay_range,
//         work_mode_type: formData.work_mode_type,
//         job_type: formData.job_type,
//         description: formData.description,
//         status: formData.status,
//         primary_skills: formData.primary_skills,
//         immediate_joining: formData.immediate_joining,
//       }, {
//         headers: {
//           'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//         },
//       });

//       console.log("Job request response:", jobRequestResponse);
//       const jobRequestId = jobRequestResponse.data.id;  // Adjust based on your API response
//       console.log("Job request ID:", jobRequestId);

//       const newJobRequest = jobRequestResponse.data;
//       console.log("New job request:", newJobRequest);
      
//       if (onJobRequestAdded) {
//         onJobRequestAdded(newJobRequest);
//       }

//       // 2. Upload Document
//       if (formData.document) {
//         const documentFormData = new FormData();
//         documentFormData.append('file', formData.document);
//         documentFormData.append('job_request_id', jobRequestId);  // Pass job request ID

//         // Log FormData contents for debugging
//         for (let pair of documentFormData.entries()) {
//           console.log(`${pair[0]}: ${pair[1]}`);
//         }
//         console.log("DocumentFormData:", documentFormData);
//         await axios.post('http://localhost:8000/resume-uploads/', documentFormData, {
//           headers: {
//             'Content-Type': 'multipart/form-data',
//             'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
//           },
//         });
//       }

//       // Reset form and close dialog
//       close(); // Close modal or perform any post-submit action
//     } catch (error) {
//       console.error('Failed to submit job request:', error);
//     }
//   };

//   return (
//     <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
//       <DialogTitle>Add Job Request</DialogTitle>
//       <DialogContent>
//         <Grid container spacing={3}>
//           {/* Client Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Client Name</InputLabel>
//               <Controller
//                 name="client_id"
//                 control={control}
//                 rules={{
//                   required: 'Client Name is required',
//                 }}
//                 render={({ field }) => (
//                   <Select
//                     {...field}
//                     labelId="client-select-label"
//                     id="client-select"
//                     value={selectedClientId}
//                     size="small"
//                     onChange={(e) => {
//                       handleClientChange(e); // Update client selection handler
//                       field.onChange(e.target.value); // Set value in form state
//                     }}
//                   >
//                     <MenuItem value="" disabled>
//                       <em>Select Client Name</em> {/* Placeholder as MenuItem */}
//                     </MenuItem>
//                     {clients.map((client) => (
//                       <MenuItem key={client.id} value={client.id}>
//                         {client.client_name} {/* Display client name in dropdown */}
//                       </MenuItem>
//                     ))}
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Job Title */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="job_title"
//                 control={control}
//                 rules={{
//                   required: 'Job Title is required', // Add validation rule for required field
//                 }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Job Title"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Enter job title"
//                     fullWidth
//                     error={Boolean(errors.job_title)} // Display error state if there's an error
//                     helperText={errors.job_title?.message} // Display error message if there's an error
//                     onChange={(e) => {
//                       // Sanitize input to prevent leading spaces, numeric, and special characters
//                       let value = e.target.value
//                         .replace(/^\s+/, '') // Remove leading spaces
//                         .replace(/[^a-zA-Z\s]/g, ''); // Remove numeric and special characters

//                       // Update the field value
//                       field.onChange(value);
//                     }}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Job Location */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <Controller
//                 name="job_location"
//                 control={control}
//                 rules={{
//                   required: 'Job Location is required', // Optional: Add validation rule for required field
//                 }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     label="Job Location"
//                     InputLabelProps={{ shrink: true }}
//                     size="small"
//                     placeholder="Enter job location"
//                     fullWidth
//                     error={Boolean(errors.job_location)} // Display error state if there's an error
//                     helperText={errors.job_location?.message} // Display error message if there's an error
//                     onChange={(e) => {
//                       // Sanitize input to prevent leading spaces, numeric, and special characters
//                       let value = e.target.value
//                         .replace(/^\s+/, '') // Remove leading spaces
//                         .replace(/[^a-zA-Z\s]/g, ''); // Remove numbers and special characters

//                       // Update the field value
//                       field.onChange(value);
//                     }}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>


//           {/* Pay Range Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="pay-range-label" shrink style={{ fontSize: '0.75rem' }}>Pay Range</InputLabel>
//               <Controller
//                 name="pay_range"
//                 control={control}
//                 render={({ field }) => (
//                   <Select {...field} size="small">
//                     <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
//                     <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
//                     <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
//                     <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
//                     <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
//                     <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
//                     <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Work Mode Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="work-mode-label" shrink style={{ fontSize: '0.75rem' }}>Work Mode Type</InputLabel>
//               <Controller
//                 name="work_mode_type"
//                 control={control}
//                 render={({ field }) => (
//                   <Select {...field} size="small">
//                     <MenuItem value="Onsite">Onsite</MenuItem>
//                     <MenuItem value="Hybrid">Hybrid</MenuItem>
//                     <MenuItem value="Remote">Remote</MenuItem>
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Job Type Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="job-type-label" shrink style={{ fontSize: '0.75rem' }}>Job Type</InputLabel>
//               <Controller
//                 name="job_type"
//                 control={control}
//                 render={({ field }) => (
//                   <Select {...field} size="small">
//                     <MenuItem value="Permanent">Permanent</MenuItem>
//                     <MenuItem value="Full-time">Full-time</MenuItem>
//                     <MenuItem value="Part-time">Part-time</MenuItem>
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Status Dropdown */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="status-label" shrink style={{ fontSize: '0.75rem' }}>Status</InputLabel>
//               <Controller
//                 name="status"
//                 control={control}
//                 render={({ field }) => (
//                   <Select {...field} size="small">
//                     <MenuItem value="Active">Active</MenuItem>
//                     <MenuItem value="Closed">Closed</MenuItem>
//                     <MenuItem value="On Hold">On Hold</MenuItem>
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>

//           {/* Primary Skills */}
//           {/* Primary Skills */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="primary-skills-label" shrink style={{ fontSize: '0.75rem' }}>Primary Skills</InputLabel>
//               <Controller
//                 name="primary_skills"
//                 control={control}
//                 rules={{
//                   required: 'Client Name is required',
//                 }}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     fullWidth
//                     size="small"
//                     placeholder="e.g., JavaScript, React"
//                     onChange={(e) => {
//                       // Sanitize input to prevent leading spaces and numeric characters
//                       let value = e.target.value
//                         .replace(/^\s+/, '') // Remove leading spaces
//                         .replace(/[0-9]/g, ''); // Remove numeric characters

//                       // Update the field value
//                       field.onChange(value);
//                     }}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>


//           {/* Immediate Joining */}
//           {/* Immediate Joining */}
//           <Grid item xs={12} sm={6}>
//             <FormControl fullWidth>
//               <InputLabel id="immediate-joining-label" shrink style={{ fontSize: '0.75rem' }}>Immediate Joining</InputLabel>
//               <Controller
//                 name="immediate_joining"
//                 control={control}
//                 render={({ field }) => (
//                   <Select
//                     {...field}
//                     labelId="immediate-joining-label"
//                     size="small"
//                     fullWidth
//                   >
//                     <MenuItem value="Yes">Yes</MenuItem>
//                     <MenuItem value="No">No</MenuItem>
//                   </Select>
//                 )}
//               />
//             </FormControl>
//           </Grid>


//           {/* Job Description */}
//           <Grid item xs={12}>
//             <FormControl fullWidth>
//               <InputLabel id="description-label" shrink style={{ fontSize: '0.75rem' }}>Job Description</InputLabel>
//               <Controller
//                 name="description"
//                 control={control}
//                 render={({ field }) => (
//                   <TextField
//                     {...field}
//                     size="small"
//                     fullWidth
//                     multiline
//                     rows={4}
//                     onChange={(e) => {
//                       // Prevent leading spaces
//                       const value = e.target.value.replace(/^\s+/, ''); // Remove leading spaces
//                       field.onChange(value); // Update the field value
//                     }}
//                   />
//                 )}
//               />
//             </FormControl>
//           </Grid>


//           {/* File Upload */}
//           <Grid item xs={12} sm={12}>
//             <Button variant="contained" component="label" fullWidth>
//               Upload Document
//               <input
//                 type="file"
//                 hidden
//                 onChange={handleFileChange}  // Corrected file change handler
//               />
//             </Button>
//           </Grid>
//         </Grid>
//       </DialogContent>
//       <DialogActions>
//         <Button onClick={close} color="primary">Cancel</Button>
//         <Button onClick={handleSubmit(handleSubmitForm)} color="primary">Submit</Button>
//       </DialogActions>
//     </Dialog>
//   );
// }

import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import Grid from '@mui/material/Grid';
import { useForm, Controller } from 'react-hook-form'; // Import Controller
import axios from 'axios';
import { Divider } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomDropdownField ';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';

export default function JobRequestAddForm({ open, close, onJobRequestAdded }) {
  const { control, handleSubmit, setValue, reset, formState: { errors } } = useForm(); // Initialize useForm and get errors
  const [clients, setClients] = useState([]);  // State to hold client data
  const [selectedClientId, setSelectedClientId] = useState(''); 
  const [selectedClientName, setSelectedClientName] = useState(''); 

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const response = await axios.get('http://localhost:8080/client-page', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`, // Authorization header
          },
        });
  
        console.log(response);
        if (Array.isArray(response.data)) {
          setClients(response.data);
        } else {
          console.error('Unexpected data format:', response.data);
        }
      } catch (error) {
        console.error('Failed to fetch clients:', error);
      }
    };
    
    fetchClients();
  }, []);

  const handleClientChange = (event) => {
    const clientId = event.target.value;
    const clientName = clients.find(client => client.id === clientId)?.client_name || '';
    setSelectedClientId(clientId);
    setSelectedClientName(clientName);
    setValue('client_id', clientId); // Set client ID in the form state
    setValue('client_name', clientName); // Set client name in the form state
    console.log('Selected Client ID:', clientId);
    console.log('Selected Client Name:', clientName);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    console.log('Selected file:', file);
    setValue('document', file); // Set file in the form state
  };

  const handleSubmitForm = async (formData) => {
    try {
      // 1. Submit Job Request Data
      const jobRequestResponse = await axios.post('http://localhost:8080/job-requests/', {
        client_id: formData.client_id,
        client_name: selectedClientName,
        job_title: formData.job_title,
        job_location: formData.job_location,
        pay_range: formData.pay_range,
        work_mode_type: formData.work_mode_type,
        job_type: formData.job_type,
        description: formData.description,
        status: formData.status,
        primary_skills: formData.primary_skills,
        immediate_joining: formData.immediate_joining,
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
        },
      });

      console.log("Job request response:", jobRequestResponse);
      const jobRequestId = jobRequestResponse.data.id;  // Adjust based on your API response
      console.log("Job request ID:", jobRequestId);

      const newJobRequest = jobRequestResponse.data;
      console.log("New job request:", newJobRequest);
      
      if (onJobRequestAdded) {
        onJobRequestAdded(newJobRequest);
      }

      // 2. Upload Document
      if (formData.document) {
        const documentFormData = new FormData();
        documentFormData.append('file', formData.document);
        documentFormData.append('job_request_id', jobRequestId);  // Pass job request ID

        // Log FormData contents for debugging
        for (let pair of documentFormData.entries()) {
          console.log(`${pair[0]}: ${pair[1]}`);
        }
        console.log("DocumentFormData:", documentFormData);
        await axios.post('http://localhost:8000/resume-uploads/', documentFormData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${localStorage.getItem('serviceToken')}`,
          },
        });
      }

      // Reset form and close dialog
      reset(); // Reset form state to initial values
      close(); // Close modal or perform any post-submit action
    } catch (error) {
      console.error('Failed to submit job request:', error);
    }
  };

  return (
    <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
      <DialogTitle>Add Job Request</DialogTitle>
      <DialogContent>
        <Grid container spacing={3}>
          {/* Client Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Client Name</InputLabel>
              <Controller
                name="client_id"
                control={control}
                rules={{
                  required: 'Client Name is required',
                }}
                render={({ field }) => (
                  <Select
                    {...field}
                    labelId="client-select-label"
                    id="client-select"
                    value={selectedClientId}
                    size="small"
                    onChange={(e) => {
                      handleClientChange(e); // Update client selection handler
                      field.onChange(e.target.value); // Set value in form state
                    }}
                  >
                    <MenuItem value="" disabled>
                      <em>Select Client Name</em> {/* Placeholder as MenuItem */}
                    </MenuItem>
                    {clients.map((client) => (
                      <MenuItem key={client.id} value={client.id}>
                        {client.client_name} {/* Display client name in dropdown */}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Title */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="job_title"
                control={control}
                rules={{
                  required: 'Job Title is required', // Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Job Title"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter job title"
                    fullWidth
                    error={Boolean(errors.job_title)} // Display error state if there's an error
                    helperText={errors.job_title?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces, numeric, and special characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[^a-zA-Z\s]/g, ''); // Remove numeric and special characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          

          {/* Job Location */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="job_location"
                control={control}
                rules={{
                  required: 'Job Location is required', // Optional: Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Job Location"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter job location"
                    fullWidth
                    error={Boolean(errors.job_location)} // Display error state if there's an error
                    helperText={errors.job_location?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces, numeric, and special characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[^a-zA-Z\s]/g, ''); // Remove numbers and special characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Pay Range Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="pay-range-label" shrink style={{ fontSize: '0.75rem' }}>Pay Range</InputLabel>
              <Controller
                name="pay_range"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
                    <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
                    <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
                    <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
                    <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
                    <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
                    <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Work Mode Type Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="work-mode-label" shrink style={{ fontSize: '0.75rem' }}>Work Mode Type</InputLabel>
              <Controller
                name="work_mode_type"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Onsite">Onsite</MenuItem>
                    <MenuItem value="Hybrid">Hybrid</MenuItem>
                    <MenuItem value="Remote">Remote</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Type Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="job-type-label" shrink style={{ fontSize: '0.75rem' }}>Job Type</InputLabel>
              <Controller
                name="job_type"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Permanent">Permanent</MenuItem>
                    <MenuItem value="Full-time">Full-time</MenuItem>
                    <MenuItem value="Part-time">Part-time</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Status Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="status-label" shrink style={{ fontSize: '0.75rem' }}>Status</InputLabel>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Closed">Closed</MenuItem>
                    <MenuItem value="On Hold">On Hold</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Primary Skills */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="primary-skills-label" shrink style={{ fontSize: '0.75rem' }}>Primary Skills</InputLabel>
              <Controller
                name="primary_skills"
                control={control}
                rules={{
                  required: 'Primary Skills are required', // Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    size="small"
                    placeholder="e.g., JavaScript, React"
                    error={Boolean(errors.primary_skills)} // Display error state if there's an error
                    helperText={errors.primary_skills?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces and numeric characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[0-9]/g, ''); // Remove numeric characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Immediate Joining */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="immediate-joining-label" shrink style={{ fontSize: '0.75rem' }}>Immediate Joining</InputLabel>
              <Controller
                name="immediate_joining"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    labelId="immediate-joining-label"
                    size="small"
                    fullWidth
                  >
                    <MenuItem value="Yes">Yes</MenuItem>
                    <MenuItem value="No">No</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Description */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id="description-label" shrink style={{ fontSize: '0.75rem' }}>Job Description</InputLabel>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size="small"
                    fullWidth
                    multiline
                    rows={4}
                    onChange={(e) => {
                      // Prevent leading spaces
                      const value = e.target.value.replace(/^\s+/, ''); // Remove leading spaces
                      field.onChange(value); // Update the field value
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* File Upload */}
          <Grid item xs={12} sm={12}>
            <Button variant="contained" component="label" fullWidth>
              Upload Document
              <input
                type="file"
                hidden
                onChange={handleFileChange}  // Corrected file change handler
              />
            </Button>
          </Grid>
        </Grid>
        <Grid item mt={2}>
        <Divider />
      </Grid>

        {/* Job Location */}
        {/* <Grid item xs={12} sm={6} >
       <CustomNameField
        name={"job_location"}
        control={control}
        placeholder={"Enter name"}
        />
         </Grid> */}

         <Grid item xs={12} sm={6} mt={2}>

         <CustomDropdownField
  name="status"
  control={control}
  placeholder="Select Status"
  options={[
    { value: 'Active' },
      { value: 'Inactive' },
      { value: 'On hold'},
      { value: 'On hold'},
  ]}
/>

       </Grid>
       <Grid item xs={12} sm={6} mt={2}>
  <CustomDropdownField
    name="job_type"
    control={control}
    placeholder="Select Job Type"
    options={[
      { value: 'Permanent' },
      { value: 'Full-time' },
      { value: 'Part-time'},
      {value:'Chidhagni'},
      {value:'Chidhagni'},

    ]}
  />
</Grid>

       
 {/* Job Title */}
 <Grid item xs={12} sm={6} mt={2}>
            <FormControl fullWidth>
              <Controller
                name="job_title"
                control={control}
                rules={{
                  required: 'Job Title is required', // Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Job Title"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter job title"
                    fullWidth
                    error={Boolean(errors.job_title)} // Display error state if there's an error
                    helperText={errors.job_title?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces, numeric, and special characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[^a-zA-Z\s]/g, ''); // Remove numeric and special characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          

      </DialogContent>
      <DialogActions>
        <Button onClick={close} color="primary">Cancel</Button>
        <Button onClick={handleSubmit(handleSubmitForm)} color="primary">Submit</Button>
      </DialogActions>

      

      
    </Dialog>
  );
}
