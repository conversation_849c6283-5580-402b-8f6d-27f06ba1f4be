import { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';

export default function JobRequestEditForm({ open, close, selectedRow, onJobRequestUpdated}) {
  
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: {
      client_name: '',
      client_id: '',
      job_title: '',
      job_location: '',
      pay_range: '',
      work_mode_type: '',
      job_type: '',
      status: '',
      primary_skills: '',
      immediate_joining: '',
      description: ''
    }
  });


  // console.log('Selected Row Prop:', selectedRow);
  // Update form values when selectedRow changes
  useEffect(() => {
    if (selectedRow) {
      reset({
        client_id: selectedRow.client_id || '',
        client_name: selectedRow.client_name || '',
        job_title: selectedRow.job_title || '',
        job_location: selectedRow.job_location || '',
        pay_range: selectedRow.pay_range || '',
        work_mode_type: selectedRow.work_mode_type || '',
        job_type: selectedRow.job_type || '',
        status: selectedRow.status || '',
        primary_skills: selectedRow.primary_skills || '',
        immediate_joining: selectedRow.immediate_joining || '',
        description: selectedRow.description || ''
      });
    }
  }, [selectedRow, reset]);

  // Handle form submission
  const onSubmit = async (data) => {
    console.log('Submitting data:', data); // Log the form data
  
    try {
      // Get the token from localStorage (or wherever it's stored)
      const token = localStorage.getItem('serviceToken');  // Adjust the key based on your actual token key
  
      const response = await fetch(`http://localhost:8080/job-requests/${selectedRow.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,  // Add the token to the Authorization header
        },
        body: JSON.stringify(data)  // Send the form data
      });
  
      if (response.ok) {
        const updatedJobRequest = await response.json(); // Parse the response data
        console.log('Form data submitted:', updatedJobRequest);
  
        // Pass the updated data back to the parent component
        if (onJobRequestUpdated) {
          onJobRequestUpdated(updatedJobRequest);
        }
  
        close(); // Close the form after successful submission
      } else {
        console.error('Failed to submit form', response.statusText);
      }
    } catch (error) {
      console.error('Error during form submission:', error);
    }
  };
  
  

  return (
    <Dialog open={open} onClose={close} fullWidth maxWidth="sm">
      <DialogTitle>Edit Job Request</DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          {/* Client ID (Hidden field) */}
          <Controller
            name="client_id"
            control={control}
            render={({ field }) => (
              <input type="hidden" {...field} />
            )}
          />

          {/* Client Name - Read-only */}
          {/* Client Name - Read-only */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="client-select-label" shrink style={{ fontSize: '0.75rem' }}>Client Name</InputLabel>
              <Controller
                name="client_name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Client"
                    fullWidth
                    size="small" // Apply size prop here
                    InputProps={{ readOnly: true }} // Keep the input read-only
                  />
                )}
              />
            </FormControl>
          </Grid>


          {/* Client Dropdown */}
         

          {/* Job Title */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="job_title"
                control={control}
                rules={{
                  required: 'Job Title is required', // Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Job Title"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter job title"
                    fullWidth
                    error={Boolean(errors.job_title)} // Display error state if there's an error
                    helperText={errors.job_title?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces, numeric, and special characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[^a-zA-Z\s]/g, ''); // Remove numeric and special characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Location */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="job_location"
                control={control}
                rules={{
                  required: 'Job Location is required', // Optional: Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Job Location"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter job location"
                    fullWidth
                    error={Boolean(errors.job_location)} // Display error state if there's an error
                    helperText={errors.job_location?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces, numeric, and special characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[^a-zA-Z\s]/g, ''); // Remove numbers and special characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Pay Range Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="pay-range-label" shrink style={{ fontSize: '0.75rem' }}>Pay Range</InputLabel>
              <Controller
                name="pay_range"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="1-2 LPA">1-2 LPA</MenuItem>
                    <MenuItem value="2-3 LPA">2-3 LPA</MenuItem>
                    <MenuItem value="3-4 LPA">3-4 LPA</MenuItem>
                    <MenuItem value="4-5 LPA">4-5 LPA</MenuItem>
                    <MenuItem value="5-6 LPA">5-6 LPA</MenuItem>
                    <MenuItem value="6-7 LPA">6-7 LPA</MenuItem>
                    <MenuItem value="7-8 LPA">7-8 LPA</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Work Mode Type Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="work-mode-label" shrink style={{ fontSize: '0.75rem' }}>Work Mode Type</InputLabel>
              <Controller
                name="work_mode_type"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Onsite">Onsite</MenuItem>
                    <MenuItem value="Hybrid">Hybrid</MenuItem>
                    <MenuItem value="Remote">Remote</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Type Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="job-type-label" shrink style={{ fontSize: '0.75rem' }}>Job Type</InputLabel>
              <Controller
                name="job_type"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Permanent">Permanent</MenuItem>
                    <MenuItem value="Full-time">Full-time</MenuItem>
                    <MenuItem value="Part-time">Part-time</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Status Dropdown */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="status-label" shrink style={{ fontSize: '0.75rem' }}>Status</InputLabel>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select {...field} size="small">
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Closed">Closed</MenuItem>
                    <MenuItem value="On Hold">On Hold</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Primary Skills */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="primary-skills-label" shrink style={{ fontSize: '0.75rem' }}>Primary Skills</InputLabel>
              <Controller
                name="primary_skills"
                control={control}
                rules={{
                  required: 'Primary Skills are required', // Add validation rule for required field
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    size="small"
                    placeholder="e.g., JavaScript, React"
                    error={Boolean(errors.primary_skills)} // Display error state if there's an error
                    helperText={errors.primary_skills?.message} // Display error message if there's an error
                    onChange={(e) => {
                      // Sanitize input to prevent leading spaces and numeric characters
                      let value = e.target.value
                        .replace(/^\s+/, '') // Remove leading spaces
                        .replace(/[0-9]/g, ''); // Remove numeric characters

                      // Update the field value
                      field.onChange(value);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          {/* Immediate Joining */}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel id="immediate-joining-label" shrink style={{ fontSize: '0.75rem' }}>Immediate Joining</InputLabel>
              <Controller
                name="immediate_joining"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    labelId="immediate-joining-label"
                    size="small"
                    fullWidth
                  >
                    <MenuItem value="Yes">Yes</MenuItem>
                    <MenuItem value="No">No</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>

          {/* Job Description */}
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id="description-label" shrink style={{ fontSize: '0.75rem' }}>Job Description</InputLabel>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size="small"
                    fullWidth
                    multiline
                    rows={4}
                    onChange={(e) => {
                      // Prevent leading spaces
                      const value = e.target.value.replace(/^\s+/, ''); // Remove leading spaces
                      field.onChange(value); // Update the field value
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={close} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSubmit(onSubmit)} color="primary">
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
}
