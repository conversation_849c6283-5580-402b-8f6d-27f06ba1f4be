import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import React, { useState, useMemo, useEffect } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import Link from '@mui/material/Link';
import Button from '@mui/material/Button';
import MainCard from 'components/MainCard';
import JobRequestAddForm from './formdialog';
import JobRequestEditForm from './formdialogedit';
import ConfirmDeleteDialog from './jorequestdeletedialog';
import Box from '@mui/material/Box';

import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
import { CSVExport, DebouncedInput, IndeterminateCheckbox, SelectColumnVisibility } from 'components/third-party/react-table';
import { useMediaQuery } from '@mui/system';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  flexRender,
  useReactTable,
  sortingFns
} from '@tanstack/react-table';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
import CustomTableContainer from 'components/custom-components/CustomTableContainer';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import VisibilitySharpIcon from '@mui/icons-material/VisibilitySharp';
import Chip from '@mui/material/Chip';
import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS } from 'constants';
import { CircularProgress, Menu, MenuItem, Stack } from '@mui/material';

// import DeleteIcon from '@material-ui/icons/Delete';

const API_URL = import.meta.env.VITE_APP_API_URL;

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  // rank the item
  const itemRank = rankItem(row.getValue(columnId), value);

  // store the ranking info
  addMeta(itemRank);

  // return if the item should be filtered in/out
  return itemRank.passed;
};

function JobRequestJob() {
  const theme = useTheme();
  const navigate = useNavigate();
  const [rows, setRows] = useState([]);
  const [data, setData] = useState([]);
  console.log('data', data);
  const { canMenuPage } = useRBAC();

  const [displayRows, setDisplayRows] = useState([]); // Holds the rows to display based on pagination
  const [open, setOpen] = useState(false);
  const [editopen, setEditOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [jobRequestId, setJobrequestId] = useState(null);
  const [viewData, setViewData] = useState(null);
  console.log('view is getting clicked by ', viewData);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [isActive, setIsActive] = useState(true);

  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnVisibility, setColumnVisibility] = useState({
    client_name: true,
    job_title: true,
    job_location: false,
    pay_range: false,
    work_mode_type: true,
    job_type: true,
    status: true,
    primary_skills: true,
    immediate_joining: false,
    description: false,
    is_active: true
  });
  const [sorting, setSorting] = useState([]);

  // Pagination state
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(0); // Zero-based page index
  const [rowCount, setRowCount] = useState(0);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const canAddJobRequest = canMenuPage('Left_Menu', 'Job Request', PERMISSIONS.WRITE);
  const canEditJobRequest = canMenuPage('Left_Menu', 'Job Request', PERMISSIONS.EXECUTE);
  const canDeleteJobRequest = canMenuPage('Left_Menu', 'Job Request', PERMISSIONS.DELETE);

  // const columns=[ { header: 'client_name', accessorKey: 'name' },
  //   { header: 'Role Type', accessorKey: 'roleType' },
  //   { header: 'Description', accessorKey: 'description' },
  //   { header: 'Created By', accessorKey: 'createdBy' },
  //   { header: 'Updated By', accessorKey: 'updatedBy' },]

  const columns = useMemo(
    () => [
      {
        id: 'select',
        enableGrouping: false,
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      {
        header: 'Client Name',
        accessorKey: 'client_name',
        cell: ({ getValue, row }) => {
          const clientName = getValue();

          return (
            <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
              <Tooltip title={clientName} placement="bottom">
                <div
                  role="button"
                  tabIndex={0}
                  onClick={() =>
                    navigate('/jobrequest/view', {
                      state: { row: JSON.parse(JSON.stringify(row)) }
                    })
                  }
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      navigate('/jobrequest/view', {
                        state: { row: JSON.parse(JSON.stringify(row)) }
                      });
                    }
                  }}
                  style={{
                    display: 'inline-block',
                    maxWidth: '200px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor: 'pointer',
                    color: '#1976d2', // MUI primary color
                    textDecoration: 'underline'
                  }}
                >
                  {clientName}
                </div>
              </Tooltip>
            </Box>
          );
        }
      },
      {
        header: 'Job Title',
        accessorKey: 'job_title',
        cell: ({ getValue }) => {
          const jobTitle = getValue();
          return (
            <Tooltip title={jobTitle} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{jobTitle}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Job Location',
        accessorKey: 'job_location',
        cell: ({ getValue }) => {
          const jobLocation = getValue();
          return (
            <Tooltip title={jobLocation} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{jobLocation}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Pay Range',
        accessorKey: 'pay_range',
        cell: ({ getValue }) => {
          const payRange = getValue();
          return (
            <Tooltip title={payRange} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{payRange}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Work Mode Type',
        accessorKey: 'work_mode_type',
        cell: ({ getValue }) => {
          const workModeType = getValue();
          return (
            <Tooltip title={workModeType} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{workModeType}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Job Type',
        accessorKey: 'job_type',
        cell: ({ getValue }) => {
          const jobType = getValue();
          return (
            <Tooltip title={jobType} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{jobType}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Primary Skills',
        accessorKey: 'primary_skills',
        cell: ({ getValue }) => {
          const primarySkills = getValue();
          return (
            <Tooltip title={primarySkills} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{primarySkills}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Immediate Joining',
        accessorKey: 'immediate_joining',
        cell: ({ getValue }) => {
          const immediateJoining = getValue();
          return (
            <Tooltip title={immediateJoining} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{immediateJoining}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: ({ getValue }) => {
          const description = getValue();
          return (
            <Tooltip title={description} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{description}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Created By',
        accessorKey: 'created_by',
        cell: ({ getValue }) => {
          const createdBy = getValue();
          return (
            <Tooltip title={createdBy} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>{createdBy}</Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Status',
        accessorKey: 'is_active',
        cell: ({ getValue }) => {
          const isActive = getValue();
          const statusLabel = isActive === true ? 'Active' : isActive === false ? 'Inactive' : 'Unknown';
          const chipColor = isActive === true ? 'success' : isActive === false ? 'error' : 'warning';
          return (
            <Tooltip title={statusLabel} placement="bottom">
              <Typography sx={{ display: 'inline-block' }}>
                <Chip color={chipColor} label={statusLabel} size="small" variant="light" />
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: 'Actions',
      //   cell: ({ row }) => {
      //     return (
      //       <Box>
      //         <Tooltip title="View" placement="bottom">
      //           <IconButton
      //             sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //             onClick={() => navigate('/jobrequest/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}
      //           >
      //             <VisibilitySharpIcon />
      //           </IconButton>
      //         </Tooltip>
      //         {canEditJobRequest && (
      //           <Tooltip title="Edit" placement="bottom">
      //             <IconButton
      //               sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //               onClick={() => navigate('/jobrequest/edit', { state: { row: JSON.parse(JSON.stringify(row)) } })}
      //             >
      //               <ModeEditIcon />
      //             </IconButton>
      //           </Tooltip>
      //         )}
      //         {canDeleteJobRequest && (
      //           <Tooltip title="Delete" placement="bottom">
      //             <IconButton
      //               sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //               onClick={() => handleDeleteClick(row.original)}
      //               style={{ color: row.original.is_active ? 'red' : 'gray' }}
      //             >
      //               <DeleteIcon />
      //             </IconButton>
      //           </Tooltip>
      //         )}
      //       </Box>
      //     );
      //   },
      // },
      {
        header: 'Actions',
        id: 'menu',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);

          const handleMenuClick = (event) => {
            event.stopPropagation(); // Prevent row selection on click
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          return (
            <>
              <IconButton onClick={handleMenuClick}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
              >
                <MenuItem onClick={() => navigate('/jobrequest/edit', { state: { row: JSON.parse(JSON.stringify(row)) } })}>Edit</MenuItem>
                {/* <MenuItem onClick={() => navigate('/jobrequest/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}>View</MenuItem> */}
                <MenuItem onClick={() => handleDeleteClick(row.original)}>Delete</MenuItem>
              </Menu>
            </>
          );
        }
      }
    ],
    [canEditJobRequest, canDeleteJobRequest]
  );

  // const columns = [
  //   {
  //     accessorKey: 'id',
  //     header: 'ID',
  //     meta: { sx: { minWidth: 80 } },
  //   },
  //   {
  //     accessorKey: 'client_name',
  //     header: 'Client Name',
  //     cell: (info) => (
  //       <Typography  component="span">
  //         {info.getValue()}  {/* Directly display the client name */}
  //       </Typography>
  //     ),
  //     meta: { sx: { minWidth: 230 } },
  //   },
  //   {
  //     accessorKey: 'status',
  //     header: 'Status',
  //     meta: { sx: { minWidth: 120 } },
  //   },
  //   {
  //     accessorKey: 'job_location',
  //     header: 'Job Location',
  //     meta: { sx: { minWidth: 150 } },
  //   },
  //   {
  //     accessorKey: 'is_active',
  //     header: 'Active',
  //     cell: (info) => (
  //       <Typography  style={{ color: info.getValue() ? 'green' : 'red' }}>
  //         {info.getValue() ? 'Active' : 'Inactive'}
  //       </Typography>
  //     ),
  //     meta: { sx: { minWidth: 100 } },
  //   },
  //   {
  //     accessorKey: 'actions',
  //     header: 'Actions',
  //     cell: (info) => (
  //       <>
  //         <IconButton onClick={() => handleEditClick(info.row.original)} style={{ marginRight: '8px' }}>
  //           <EditIcon />
  //         </IconButton>
  //         <IconButton onClick={() => handleDeleteClick(info.row.original)} style={{ color: info.row.original.is_active ? 'red' : 'gray' }}>
  //           <DeleteIcon />
  //         </IconButton>
  //       </>
  //     ),
  //     meta: { sx: { minWidth: 150 } },
  //   },
  // ];

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    // onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    // onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    // globalFilterFn: fuzzyFilter,
    onGlobalFilterChange: (value) => {
      console.log('🔍 onGlobalFilterChange triggered:', value); // Debugging log
      setGlobalFilter(value);
    },
    globalFilterFn: fuzzyFilter,
    onRowSelectionChange: setRowSelection,
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      },
      revertData: (rowIndex, revert) => {
        if (revert) {
          setData((old) => old.map((row, index) => (index === rowIndex ? originalData[rowIndex] : row)));
        } else {
          setOriginalData((old) => old.map((row, index) => (index === rowIndex ? data[rowIndex] : row)));
        }
      }
    }
  });

  let headers = [];
  table.getVisibleLeafColumns().map(
    (columns) =>
      // @ts-ignore
      columns.columnDef.accessorKey &&
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        // @ts-ignore
        key: columns.columnDef.accessorKey
      })
  );
  const [loading, setLoading] = useState(true);

  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey) // ✅ Keep only columns with data keys
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean); // ✅ Clean out undefined/empty headers
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000); // ⏱ change every 2 seconds (you can adjust)

    return () => clearInterval(interval); // Cleanup
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const fetchJobRequests = async () => {
    try {
      setLoading(true); // Start loading

      const token = localStorage.getItem('serviceToken');
      const response = await fetch(`${API_URL}/job-requests/`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      const data = await response.json();

      // Simulate loader delay (optional)
      setTimeout(() => {
        setRows(data);
        setData(data);
        setRowCount(data.length);
        setDisplayRows(data.slice(0, pageSize));
      }); // 1s delay for loader (optional)
    } catch (error) {
      console.error('Error fetching job requests:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobRequests();
  }, []);

  const handleJobRequestUpdated = (updatedJobRequest) => {
    setRows((prevRows) => prevRows.map((row) => (row.id === updatedJobRequest.id ? updatedJobRequest : row)));
  };

  const handleJobRequestAdded = (newJobRequest) => {
    setRows((prevRows) => [...prevRows, newJobRequest]);
  };

  const handleAddClick = () => {
    setOpen(true);
  };

  const handleEditClick = (rowData) => {
    setEditOpen(true);
    console.log('this is job request', rowData);
    setSelectedRow(rowData);
  };

  const handleCloseClick = () => {
    setOpen(false);
  };

  const handleEditCloseClick = () => {
    setEditOpen(false);
  };

  const handleConfirmDelete = async () => {
    try {
      // Prepare the payload for the PUT request
      const payload = {
        is_active: !isActive // Toggle the is_active status
      };

      // Log the payload to the console before sending the request
      console.log('Payload being sent:', payload);

      const response = await fetch(`${API_URL}/job-requests/${jobRequestId}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('serviceToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload) // Send the payload as a JSON string
      });

      // Log the response status and body
      console.log('Response status:', response.status);
      const responseBody = await response.json(); // Read the response body
      console.log('Response body:', responseBody); // Log the response body
      fetchJobRequests();
      // if (response.ok) {
      //   // Update the local state to reflect the status change
      //   setRows((prevRows) =>
      //     prevRows.map((row) =>
      //       row.id === jobRequestId ? { ...row, is_active: payload.is_active } : row
      //     )
      //   );
      //   setIsActive(payload.is_active); // Update the isActive state
      // } else {
      //   console.error('Failed to update job request status:', responseBody);
      // }
    } catch (error) {
      console.error('Error updating job request status:', error);
    } finally {
      setConfirmDeleteOpen(false);
    }
  };

  const handleDeleteClick = (rowData) => {
    console.log('Confirming delete with isActive:', isActive);
    setJobrequestId(rowData.id);
    console.log('this is job request', rowData);
    setIsActive(rowData.is_active);
    console.log('this is job request active', rowData.is_active);
    setConfirmDeleteOpen(true);
  };

  const handleViewClick = (id) => {
    console.log('view is getting clicked');
    const row = rows.find((row) => row.id === id);

    setViewData(row);
    setJobrequestId(id);
  };

  const closeViewDialog = () => {
    setViewData(null);
  };

  const handleDownloadResumes = async () => {
    try {
      const response = await axios.get(`${API_URL}/resume-uploads/download-job/${jobRequestId}`, {
        responseType: 'blob'
      });

      if (response.data.size === 0) {
        console.error('Error: Response data is empty.');
        return;
      }

      const contentDisposition = response.headers['content-disposition'];
      let filename = 'downloaded_file.pdf';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      const url = window.URL.createObjectURL(new Blob([response.data], { type: response.headers['content-type'] }));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Error downloading resumes:', error);
    }
  };

  const secondaryActions2 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'JobRequest.csv'
        }}
      />
      {canAddJobRequest && (
        <IconButton
          onClick={() => navigate('/jobrequest/add')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'secondary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  const secondaryActions1 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
    </Box>
  );

  const secondaryActions3 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <ColumnVisibilitySelector table={table} sx={{ padding: '5px' }} />
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'JobRequest.csv'
        }}
      />

      {canAddJobRequest && (
        <IconButton
          onClick={() => navigate('/jobrequest/add')}
          sx={{
            backgroundColor: 'primary.main',
            width: 60,
            height: 30,
            borderRadius: '6px',
            border: '1px solid grey',
            '&:hover': { backgroundColor: 'secondary.main' },
            '&:focus': { backgroundColor: 'primary.main' }
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Arial, sans-serif',
              fontSize: '14px',
              color: 'white'
            }}
          >
            + Add
          </Typography>
        </IconButton>
      )}
    </Box>
  );

  const paginationbutton = (
    <TablePagination
      {...{
        setPageSize: table.setPageSize,
        setPageIndex: table.setPageIndex,
        getState: table.getState,
        getPageCount: table.getPageCount
      }}
    />
  );

  const rowbutton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );
  const secondary = (
    <Stack direction="row" alignItems="center" spacing={{ xs: 1, sm: 2 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <SelectColumnVisibility
        {...{
          getVisibleLeafColumns: table.getVisibleLeafColumns,
          getIsAllColumnsVisible: table.getIsAllColumnsVisible,
          getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
          getAllColumns: table.getAllColumns
        }}
      />
      <Button variant="contained" startIcon={<Add />} onClick={() => navigate('/jobrequest/add')} size="large">
        Add Posting
      </Button>
      <CSVExport
        {...{
          data: table.getSortedRowModel().rows.map((d) => d.original),
          headers,
          filename: 'Jobposting.csv'
        }}
      />
    </Stack>
  );

  return (
    <CustomTableContainer
      table={table}
      onAddClick={() => navigate('/jobrequest/add')}
      csvFilename="JobRequest-list.csv"
      showAddButton={canAddJobRequest}
      addLabel="Job Posting"
      data={data}
      rowSelection={rowSelection}
      theme={theme}
    />
  );
}

export default JobRequestJob;
