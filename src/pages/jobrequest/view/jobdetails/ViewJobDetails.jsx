import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewJobDetails() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      business_unit: 'IT Services',
      job_code: 'JOB-2024-001',
      job_title: 'Senior Software Engineer',
      client_bill_rate: '$120,000',
      pay_rate: '$90,000',
      job_start_date: '2024-03-01',
      job_end_date: '2024-12-31',
      respond_by: '2024-02-15',
      remote_job_expenses: 'Yes',
      country: 'USA',
      state: 'California',
      location: 'San Francisco',
      job_status: 'Active',
      job_type: 'Full-time',
      required_hours: '40',
      client: 'Tech Corp',
      client_manager: '<PERSON>',
      end_client: 'Global Tech Solutions',
      client_job_id: 'CTS-2024-001',
      priority: 'High',
      client_category: 'Enterprise',
      duration: '12 months',
      additional_details: 'Remote work available',
      work_authorization: 'US Citizen/GC',
      ceipal_ref: 'CEI-2024-001',
      interview_mode: 'Virtual',
      application_form: 'Standard',
      address: '123 Tech Street, San Francisco, CA 94105',
      placement_fee: '15%',
      placement_type: 'Direct Hire',
      commercial_model: 'Fixed Fee',
      tvop: 'Yes',
      domain: 'Software Development',
      project_type: 'Product Development',
      notice_period: '2 weeks',
      job_category: 'Engineering'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'business_unit', label: 'Business Unit' },
    { name: 'job_code', label: 'Job Code' },
    { name: 'job_title', label: 'Job Title' },
    { name: 'client_bill_rate', label: 'Client Bill Rate/Salary' },
    { name: 'pay_rate', label: 'Pay Rate/Salary' },
    { name: 'job_start_date', label: 'Job Start Date' },
    { name: 'job_end_date', label: 'Job End Date' },
    { name: 'respond_by', label: 'Respond By' },
    { name: 'remote_job_expenses', label: 'Remote Job Expenses Paid' },
    { name: 'country', label: 'Country' },
    { name: 'state', label: 'State' },
    { name: 'location', label: 'Location' },
    { name: 'job_status', label: 'Job Status' },
    { name: 'job_type', label: 'Job Type' },
    { name: 'required_hours', label: 'Required Hours/Week' },
    { name: 'client', label: 'Client' },
    { name: 'client_manager', label: 'Client Manager' },
    { name: 'end_client', label: 'End Client' },
    { name: 'client_job_id', label: 'Client Job ID' },
    { name: 'priority', label: 'Priority' },
    { name: 'client_category', label: 'Client Category' },
    { name: 'duration', label: 'Duration' },
    { name: 'additional_details', label: 'Additional Details' },
    { name: 'work_authorization', label: 'Work Authorization' },
    { name: 'ceipal_ref', label: 'Ceipal Ref' },
    { name: 'interview_mode', label: 'Interview Mode' },
    { name: 'application_form', label: 'Application Form' },
    { name: 'address', label: 'Address' },
    { name: 'placement_fee', label: 'Placement Fee Percentage' },
    { name: 'placement_type', label: 'Placement Type' },
    { name: 'commercial_model', label: 'Commercial Model' },
    { name: 'tvop', label: 'TVOP' },
    { name: 'domain', label: 'Domain' },
    { name: 'project_type', label: 'Project Type' },
    { name: 'notice_period', label: 'Notice Period' },
    { name: 'job_category', label: 'Job Category' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Job Details</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewJobDetails;