import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewOrganizationalInformation() {
  const {
    getValues,
    handleSubmit
  } = useForm({
    defaultValues: {
      industry: 'Information Technology',
      degree: 'Bachelor of Science',
      experience: '5+ years',
      primary_skills: 'React, Node.js, TypeScript',
      evaluation_template: 'Technical Assessment',
      secondary_skills: 'AWS, Docker, Kubernetes',
      languages: 'English, Spanish'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'industry', label: 'Industry' },
    { name: 'degree', label: 'Degree' },
    { name: 'experience', label: 'Experience' },
    { name: 'primary_skills', label: 'Primary Skills' },
    { name: 'evaluation_template', label: 'Evaluation Template' },
    { name: 'secondary_skills', label: 'Secondary Skills' },
    { name: 'languages', label: 'Languages' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Organizational Information</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewOrganizationalInformation;
