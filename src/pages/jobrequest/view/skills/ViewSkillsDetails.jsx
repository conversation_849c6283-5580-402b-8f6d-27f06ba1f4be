import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewSkillsDetails() {
  const {
    getValues,
    handleSubmit
  } = useForm({
    defaultValues: {
      number_of_positions: '2',
      max_allowed_submissions: '10',
      tax_terms: 'W2',
      sales_manager: '<PERSON>',
      department: 'Engineering',
      recruitment_manager: '<PERSON>',
      account_manager: '<PERSON>',
      assigned_to: '<PERSON>',
      primary_recruiter: '<PERSON>',
      comments: 'Urgent hiring for critical project',
      career_portal_date: '2024-02-01',
      additional_notifications: 'Weekly status updates required',
      job_description: 'Senior Full Stack Developer with 5+ years of experience in React and Node.js'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'number_of_positions', label: 'Number of Positions' },
    { name: 'max_allowed_submissions', label: 'Max Allowed Submissions' },
    { name: 'tax_terms', label: 'Tax Terms' },
    { name: 'sales_manager', label: 'Sales Manager' },
    { name: 'department', label: 'Department' },
    { name: 'recruitment_manager', label: 'Recruitment Manager' },
    { name: 'account_manager', label: 'Account Manager' },
    { name: 'assigned_to', label: 'Assigned To' },
    { name: 'primary_recruiter', label: 'Primary Recruiter' },
    { name: 'comments', label: 'Comments' },
    { name: 'career_portal_date', label: 'Career Portal Published Date' },
    { name: 'additional_notifications', label: 'Additional Notifications' },
    { name: 'job_description', label: 'Job Description' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Skills Details</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewSkillsDetails;
