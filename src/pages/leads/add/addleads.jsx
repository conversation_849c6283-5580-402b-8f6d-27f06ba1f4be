// AddLead.jsx

import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, CardActions, Typography, InputLabel } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { useForm, Controller } from 'react-hook-form';
import Stack from '@mui/material/Stack';
import ReactDraft from 'pages/client-page/component/ReactDraft';
import axios from 'axios';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import MainCard from 'components/MainCard';
import CustomClientName from 'components/custom-components/CustomClientName';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomFederalIDField from 'components/custom-components/CustomFederalIDField';
import CustomWebsiteField from 'components/custom-components/CustomWebsiteField';
import CustomAdd<PERSON>Field from 'components/custom-components/CustomAddressField';
import CustomPostal<PERSON>odeField from 'components/custom-components/CustomPostalCode';
import CustomAboutCompanyField from 'components/custom-components/CustomAboutCompanyField ';
import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useTheme } from '@mui/material/styles';
const API_URL = import.meta.env.VITE_APP_API_URL;
const HEADER_HEIGHT = 0;
const AddLead = ({}) => {
  const [industries, setIndustries] = useState([]);
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const theme = useTheme();
  const [selectedCountryId, setSelectedCountryId] = useState('');
  const [selectedStateId, setSelectedStateId] = useState('');

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    trigger,
    formState: { errors }
  } = useForm({});

  const handleInputChange = () => {
    setFormData(getValues());
  };
  const industryOptions = industries.map((industry) => ({
    value: industry.name // Adjust based on actual API response
  }));

  // Fetch dropdown data
  useEffect(() => {
    axios.get(`${API_URL}/industry`).then((res) => setIndustries(res.data));
    axios.get(`${API_URL}/api/countries`).then((res) => setCountries(res.data));
  }, []);

  useEffect(() => {
    if (selectedCountryId) {
      axios.get(`${API_URL}/api/states/${selectedCountryId}`).then((res) => setStates(res.data));
    }
  }, [selectedCountryId]);

  useEffect(() => {
    if (selectedStateId) {
      axios.get(`${API_URL}/api/cities/${selectedStateId}`).then((res) => setCities(res.data));
    }
  }, [selectedStateId]);

  const countriesOptions = countries.map((country) => {
    console.log('Selected Country:', country.name); // Logging correctly
    return {
      value: country.name
      // label: country.name
    };
  });

  console.log('SelectedCountry', countriesOptions);

  const statesOptions = states.map((state) => {
    console.log('Selected State:', state.name); // Logging correctly
    return {
      value: state.name
      // label: state.name
    };
  });

  console.log('SelectedState', statesOptions);

  const cityOptions = cities.map((city) => {
    console.log('Selected City:', city.name); // Logging correctly
    return {
      value: city.name
      // label: city.name
    };
  });

  console.log('SelectedCity', cityOptions);

  //   const handleCountryChange = (selectedValue) => {
  //     console.log("selected value", selectedValue)
  //     const selectedCountry= countries.find(country => country.id === selectedValue);

  //     if (selectedCountry) {
  //       setSelectedCountry(selectedCountry.id);  // Store only client ID

  //       setValue("country", selectedCountry.name);
  //     }
  //     console.log("Selected Name:", selectedCountry.name);
  //     console.log("Selected Country ID:", selectedCountry?.id);
  //     // console.log("Selected Client Name:", selectedClient?.client_name);
  // };
  const handleCountryChange = (selectedValue) => {
    console.log('selected value', selectedValue);
    const selectedCountry = countries.find((country) => country.name === selectedValue);
    if (selectedCountry) {
      setSelectedCountry(selectedCountry.id); // Store only client ID
      setValue('country', selectedCountry?.name); // Make sure form value is set
      console.log('Selected Name:', selectedCountry?.name);
    }

    console.log('Selected Country ID:', selectedCountry?.id);
  };

  const handleStateChange = (selectedValue) => {
    console.log('selected value', selectedValue);
    const selectedState = states.find((state) => state.name === selectedValue);
    if (selectedState) {
      setSelectedStateName(selectedState?.id);
      setValue('state', selectedState?.name);
      console.log('Selected State ID******:', selectedState?.id);
      // setSelectedState(selectedState.id);  // Store only client ID
      // setSelectedClientName(selectedClient.client_name);  // Store client name for display
    }
    console.log('Selected State ID:', selectedState?.id);
    // console.log("Selected Client Name:", selectedClient?.client_name);
  };

  const handleCityChange = (selectedValue) => {
    console.log('Selected City Name:', selectedValue);
    const selectedCity = cities.find((city) => city.name === selectedValue);
    if (selectedCity) {
      setSelectedStateName(selectedCity.id); // Store the ID of the selected city
      setValue('city', selectedCity?.name); // Set the name of the selected city in form
      console.log('Mapped City ID:', selectedCity?.id);
    }
  };

  return (
    <>
      <CardActions
        sx={{
          position: 'sticky',
          top: HEADER_HEIGHT, // ✅ Always stick at top
          bgcolor: 'background.default',
          zIndex: 1100, // ✅ Higher than tabs
          borderBottom: '1px solid',
          borderBottomColor: theme.palette.divider,
          padding: '8px 16px'
        }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
          <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
            Add Leads
          </Typography>
          <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
            <Button variant="outlined" size="small" component={Link} to="/leads">
              Cancel
            </Button>
            <Button
              variant="contained"
              size="small"
              color="primary"
              // onClick={handleFormSubmit}
              type="submit"
            >
              Submit
            </Button>
          </Stack>
        </Stack>
      </CardActions>
      <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible', mt: -3 }}>
        <MainCard
          // title="Add Leads"
          sx={{ borderRadius: '1%', backgroundColor: 'white', border: 'none', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 1 }}
        >
          <Grid container spacing={10} columnSpacing={10} rowSpacing={2}>
            {/* Primary Business Unit */}

            {/* Client Name */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="client_name">
                  Lead Name <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomClientName
                  name="lead_name"
                  control={control}
                  placeholder="Enter Lead Name"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="contact_number">
                  Contact Number <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomContactNumberField
                  name="contacts_number"
                  control={control}
                  placeholder="Enter Contact Number"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Client Visibility */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="visibility">
                  Ownership <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="visibility"
                  control={control}
                  placeholder="Select Ownership"
                  onBlur={handleInputChange}
                  rules={{ required: 'Ownership is required' }}
                  options={[{ value: 'prudhvi' }, { value: 'Radhika' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="visibility">
                  Source <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="Source"
                  control={control}
                  placeholder="Select Source"
                  onBlur={handleInputChange}
                  options={[
                    { value: 'Internal Seminar' },
                    { value: 'Employee Referral' },
                    { value: 'External Referral' },
                    { value: 'Cold Call' },
                    { value: 'By Sales Email' },
                    { value: 'Online Search Chat' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="status">
                  Status <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="status"
                  control={control}
                  placeholder="Select Status"
                  onBlur={handleInputChange}
                  options={[
                    { value: 'Attempted to Call' },
                    { value: 'Contact in Future' },
                    { value: 'Contacted' },
                    { value: 'Lead' },
                    { value: 'Not Contacted' },
                    { value: 'Prequalified' }
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="address">
                  Address 1 <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAddressField
                  multiline
                  minRows={3}
                  name="address_one"
                  control={control}
                  placeholder="Enter Address one"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="address">
                  Address 2 <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAddressField
                  multiline
                  minRows={3}
                  name="address_two"
                  control={control}
                  placeholder="Enter Address 2"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="Country">
                  Country <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="country"
                  control={control}
                  placeholder="Select Country"
                  options={countriesOptions}
                  onBlur={handleInputChange}
                  rules={{ required: 'Country is required' }}
                  onChange={(e) => handleCountryChange(e.target.value)}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            {/* <Grid item xs={12} sm={6} xl={4}>
                      <Stack spacing={1}>
                        < InputLabel htmlFor="Country">Country</ InputLabel>
                        <CustomDropdownFields
                          name="country"
                          control={control}
                          placeholder="Select Country"
                          options={countriesOptions}
                          onBlur={handleInputChange}
                     
                          onChange={(e) => handleCountryChange(e.target.value)}
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                        />
                      </Stack>
                    </Grid> */}

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="state">
                  State <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="state"
                  control={control}
                  placeholder="Select State"
                  options={statesOptions}
                  onBlur={handleInputChange}
                  rules={{ required: 'State is required' }}
                  onChange={(e) => handleStateChange(e.target.value)}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="City">
                  City <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="city"
                  control={control}
                  placeholder="Select City"
                  options={cityOptions}
                  onBlur={handleInputChange}
                  rules={{ required: 'City is required' }}
                  onChange={(e) => handleCityChange(e.target.value)}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Postal Code */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="postal_code">
                  Postal Code <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomPostalCodeField
                  name="postal_code"
                  control={control}
                  placeholder="Enter Postal Code"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Website */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="website">
                  Website <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomWebsiteField
                  name="website"
                  control={control}
                  placeholder="Enter Website"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  Metro System <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomNameField
                  name="metro_system"
                  control={control}
                  placeholder="Enter Metro System"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  Linked IN URL <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomNameField
                  name="linkedin"
                  control={control}
                  placeholder="Enter linkedin"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  number of Employees <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="employees"
                  control={control}
                  placeholder="Enter number of employees"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  Revenue <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="Revenue"
                  control={control}
                  placeholder="Enter Revenue"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  Fiscal Year End <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="Revenue"
                  control={control}
                  placeholder="Enter Revenue"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Industry */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="industry">
                  Industry type<span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomDropdownField
                  name="industry"
                  control={control}
                  placeholder="Select Industry"
                  onBlur={handleInputChange}
                  rules={{ required: 'Industry is required' }}
                  options={industryOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            {/* Category */}
            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="category">Ownership Type</InputLabel>
                <CustomDropdownField
                  name="Ownership type"
                  control={control}
                  placeholder="Select Ownership type"
                  onBlur={handleInputChange}
                  options={[{ value: 'Educational' }, { value: 'Government' }, { value: 'Private' }, { value: 'Public' }]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  SIC <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="sic"
                  control={control}
                  placeholder="Enter SIC"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  NAICS <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="naics"
                  control={control}
                  placeholder="Enter naics"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={6} xl={4}>
              <Stack spacing={1}>
                <InputLabel htmlFor="primary_owner">
                  Number of locations <span style={{ color: 'red' }}>*</span>
                </InputLabel>
                <CustomAllCharactersField
                  name="nooflocations"
                  control={control}
                  placeholder="Enter no of locations"
                  onBlur={handleInputChange}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
              <Stack spacing={2}>
                <InputLabel htmlFor="notify">Description</InputLabel>
                <ReactDraft />
              </Stack>
            </Grid>
          </Grid>
        </MainCard>
      </MainCard>
    </>
  );
};
export default AddLead;
