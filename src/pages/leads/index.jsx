import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import React, { useState, useMemo, useEffect } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import Link from '@mui/material/Link';
import Button from '@mui/material/Button';
import MainCard from 'components/MainCard';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
import { CSVExport, DebouncedInput, IndeterminateCheckbox, SelectColumnVisibility} from 'components/third-party/react-table';
import { useMediaQuery } from '@mui/system';
import Tooltip from '@mui/material/Tooltip';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import Spinner from 'components/custom-components/Spinner';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  flexRender,
  useReactTable,
  sortingFns
} from '@tanstack/react-table';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
import CustomTableContainer from 'components/custom-components/CustomTableContainer';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import VisibilitySharpIcon from '@mui/icons-material/VisibilitySharp';
import Chip from '@mui/material/Chip';
import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS } from 'constants';
import { CircularProgress, Menu, MenuItem, Stack } from '@mui/material';
// import DeleteIcon from '@material-ui/icons/Delete';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

function Placements() {
  const theme = useTheme();
  const navigate = useNavigate();
  const [data, setData] = useState([
    {
      id: 1,
      business_unit: 'IT Services',
      lead_name: 'John Doe',
      website: 'https://www.johndoetech.com',
      contact_number: '9876543210',
      ownership: 'Private',
      source: 'Referral',
      notify: true,
      status: 'Active',
      address_line_1: '123 Tech Park',
      address_line_2: 'Building 3, Suite 201',
      country: 'USA',
      state: 'California',
      city: 'San Francisco',
      pincode: '94105',
      metro_system: 'BART',
      linkedin_url: 'https://www.linkedin.com/in/johndoe/',
      no_of_employees: 200,
      revenue: '$5M',
      fiscal_year_end: 'December',
      industry_type: 'Software',
      ownership_type: 'Private',
      sic: '7371',
      no_of_locations: 3,
      naics: '541511',
      description: 'A leading IT services provider specializing in software development and digital transformation.'
    },
    {
      id: 2,
      business_unit: 'Manufacturing',
      lead_name: 'Jane Smith',
      website: 'https://www.janesmithmfg.com',
      contact_number: '**********',
      ownership: 'Public',
      source: 'Advertisement',
      notify: false,
      status: 'Inactive',
      address_line_1: '456 Industrial Blvd',
      address_line_2: 'Unit 12',
      country: 'USA',
      state: 'Texas',
      city: 'Houston',
      pincode: '77001',
      metro_system: 'METRO',
      linkedin_url: 'https://www.linkedin.com/in/janesmith/',
      no_of_employees: 500,
      revenue: '$20M',
      fiscal_year_end: 'June',
      industry_type: 'Manufacturing',
      ownership_type: 'Public',
      sic: '3444',
      no_of_locations: 10,
      naics: '332999',
      description: 'Manufacturer of industrial machinery and components.'
    },
    {
      id: 3,
      business_unit: 'Retail',
      lead_name: 'Alice Johnson',
      website: 'https://www.alicejohnsonretail.com',
      contact_number: '9345678901',
      ownership: 'Private',
      source: 'Online Marketing',
      notify: true,
      status: 'Active',
      address_line_1: '789 Market St',
      address_line_2: 'Floor 2',
      country: 'Canada',
      state: 'Ontario',
      city: 'Toronto',
      pincode: 'M5A 1A1',
      metro_system: 'TTC',
      linkedin_url: 'https://www.linkedin.com/in/alicejohnson/',
      no_of_employees: 100,
      revenue: '$10M',
      fiscal_year_end: 'March',
      industry_type: 'Retail',
      ownership_type: 'Private',
      sic: '5411',
      no_of_locations: 5,
      naics: '441110',
      description: 'A leading retail chain offering a variety of consumer goods and services.'
    },
    {
      id: 4,
      business_unit: 'Healthcare',
      lead_name: 'David Lee',
      website: 'https://www.davidleehc.com',
      contact_number: '**********',
      ownership: 'Non-profit',
      source: 'Conference',
      notify: false,
      status: 'Active',
      address_line_1: '101 Health Ave',
      address_line_2: 'Suite 202',
      country: 'UK',
      state: 'London',
      city: 'London',
      pincode: 'EC1A 1BB',
      metro_system: 'London Underground',
      linkedin_url: 'https://www.linkedin.com/in/davidlee/',
      no_of_employees: 50,
      revenue: '$3M',
      fiscal_year_end: 'August',
      industry_type: 'Healthcare',
      ownership_type: 'Non-profit',
      sic: '8062',
      no_of_locations: 2,
      naics: '621111',
      description: 'Non-profit healthcare provider focused on community health and wellness.'
    },
    {
      id: 5,
      business_unit: 'Finance',
      lead_name: 'Bob White',
      website: 'https://www.bobwhitefinance.com',
      contact_number: '**********',
      ownership: 'Public',
      source: 'Referral',
      notify: true,
      status: 'Active',
      address_line_1: '202 Finance St',
      address_line_2: 'Suite 303',
      country: 'Australia',
      state: 'Victoria',
      city: 'Melbourne',
      pincode: '3000',
      metro_system: 'Melbourne Metro',
      linkedin_url: 'https://www.linkedin.com/in/bobwhite/',
      no_of_employees: 150,
      revenue: '$15M',
      fiscal_year_end: 'September',
      industry_type: 'Finance',
      ownership_type: 'Public',
      sic: '6021',
      no_of_locations: 7,
      naics: '522110',
      description: 'A financial services company specializing in investment banking and wealth management.'
    }
  ]);

  console.log('data', data);
  const { canMenuPage } = useRBAC();
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnVisibility, setColumnVisibility] = useState({
    business_unit: true,
    lead_name: true,
    website: true,
    contact_number: true,
    ownership: false,
    source: false,
    notify: false,
    status: true,
    address_line_1: false,
    address_line_2: false,
    country: false,
    state: false,
    city: false,
    pincode: false,
    metro_system: false,
    linkedin_url: false,
    no_of_employees: false,
    revenue: false,
    fiscal_year_end: false,
    industry_type: false,
    ownership_type: false,
    sic: false,
    no_of_locations: false,
    naics: false,
    description: false
  });
  const [sorting, setSorting] = useState([]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const columns = useMemo(
    () => [
      {
        id: 'select',
        enableGrouping: false,
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      { header: 'Business Unit', accessorKey: 'business_unit' },
      {
        header: 'Lead Name',
        accessorKey: 'lead_name',
        cell: ({ getValue, row }) => {
          const leadName = getValue();

          return (
            <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
              <Tooltip title={leadName} placement="bottom">
                <div
                  onClick={() =>
                    navigate('/leads/view', {
                      state: { row: JSON.parse(JSON.stringify(row)) }
                    })
                  }
                  style={{
                    display: 'inline-block',
                    maxWidth: '200px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    cursor: 'pointer',
                    color: '#1976d2', // MUI primary color
                    textDecoration: 'underline'
                  }}
                >
                  {leadName}
                </div>
              </Tooltip>
            </Box>
          );
        }
      },
      { header: 'Website', accessorKey: 'website' },
      { header: 'Contact Number', accessorKey: 'contact_number' },
      { header: 'Ownership', accessorKey: 'ownership' },
      { header: 'Source', accessorKey: 'source' },
      { header: 'Notify', accessorKey: 'notify' },
      { header: 'Status', accessorKey: 'status' },
      { header: 'Address Line 1', accessorKey: 'address_line_1' },
      { header: 'Address Line 2', accessorKey: 'address_line_2' },
      { header: 'Country', accessorKey: 'country' },
      { header: 'State', accessorKey: 'state' },
      { header: 'City', accessorKey: 'city' },
      { header: 'PinCode', accessorKey: 'pincode' },
      { header: 'Metro System', accessorKey: 'metro_system' },
      { header: 'LinkedIn Url', accessorKey: 'linkedin_url' },
      { header: 'No. of Employees', accessorKey: 'no_of_employees' },
      { header: 'Revenue', accessorKey: 'revenue' },
      { header: 'Fiscal Year End', accessorKey: 'fiscal_year_end' },
      { header: 'Industry Type', accessorKey: 'industry_type' },
      { header: 'Ownership Type', accessorKey: 'ownership_type' },
      { header: 'SIC', accessorKey: 'sic' },
      { header: 'Number of locations', accessorKey: 'no_of_locations' },
      { header: 'NAICS', accessorKey: 'naics' },
      { header: 'Description', accessorKey: 'description' },

      {
        header: 'Status',
        accessorKey: 'is_active',
        cell: ({ getValue }) => {
          const isActive = getValue();
          const statusLabel = isActive === true ? 'Active' : isActive === false ? 'Inactive' : 'Unknown';
          const chipColor = isActive === true ? 'success' : isActive === false ? 'error' : 'warning';
          return (
            <Tooltip title={statusLabel} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                <Chip color={chipColor} label={statusLabel} size="small" variant="light" />
              </Typography>
            </Tooltip>
          );
        }
      },
      // {
      //   header: 'Actions',
      //   cell: ({ row }) => {
      //     return (
      //       <Box>
      //         <Tooltip title="View" placement="bottom">
      //           <IconButton
      //             sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //           // onClick={() => navigate('/jobrequest/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}
      //           >
      //             <VisibilitySharpIcon />
      //           </IconButton>
      //         </Tooltip>

      //         <Tooltip title="Edit" placement="bottom">
      //           <IconButton
      //             sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //           onClick={() => navigate('/leads/edit', { state: { row: JSON.parse(JSON.stringify(row)) } })}
      //           >
      //             <ModeEditIcon />
      //           </IconButton>
      //         </Tooltip>

      //         {/* <Tooltip title="Delete" placement="bottom">
      //           <IconButton
      //             sx={{ padding: 0.2, height: 24, width: 24, minWidth: 'unset' }}
      //           // onClick={() => handleDeleteClick(row.original)}
      //           // style={{ color: row.original.is_active ? 'red' : 'gray' }}
      //           >
      //             <DeleteIcon />
      //           </IconButton>
      //         </Tooltip> */}

      //       </Box>
      //     );
      //   },
      // },
      {
        header: 'Actions',
        id: 'menu',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);

          const handleMenuClick = (event) => {
            event.stopPropagation(); // Prevent row selection on click
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          return (
            <>
              <IconButton onClick={handleMenuClick}>
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right'
                }}
              >
                <MenuItem onClick={() => navigate('/leads/edit', { state: { row: JSON.parse(JSON.stringify(row)) } })}>Edit</MenuItem>
                {/* <MenuItem onClick={() => navigate('/leads/view', { state: { row: JSON.parse(JSON.stringify(row)) } })}>View</MenuItem> */}
                {/* <MenuItem onClick={() => handleDeleteClick(row.original)}>Delete</MenuItem> */}
              </Menu>
            </>
          );
        }
      }
    ],
    [navigate]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: (value) => {
      console.log('🔍 onGlobalFilterChange triggered:', value);
      setGlobalFilter(value);
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    globalFilterFn: fuzzyFilter,
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      }
    }
  });

  let headers = [];
  table.getVisibleLeafColumns().map(
    (columns) =>
      columns.columnDef.accessorKey &&
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        key: columns.columnDef.accessorKey
      })
  );

  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey)
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean);
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const secondaryActions2 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'Placement.csv'
        }}
      />

      <IconButton
        onClick={() => navigate('/leads/add')}
        sx={{
          backgroundColor: 'primary.main',
          width: 60,
          height: 30,
          borderRadius: '6px',
          border: '1px solid grey',
          '&:hover': { backgroundColor: 'secondary.main' },
          '&:focus': { backgroundColor: 'primary.main' }
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'white'
          }}
        >
          + Add
        </Typography>
      </IconButton>
    </Box>
  );
  const secondaryActions3 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <ColumnVisibilitySelector table={table} sx={{ padding: '5px' }} />
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'Placement.csv'
        }}
      />

      <IconButton
        onClick={() => navigate('/leads/add')}
        sx={{
          backgroundColor: 'primary.main',
          width: 60,
          height: 30,
          borderRadius: '6px',
          border: '1px solid grey',
          '&:hover': { backgroundColor: 'secondary.main' },
          '&:focus': { backgroundColor: 'primary.main' }
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'white'
          }}
        >
          + Add
        </Typography>
      </IconButton>
    </Box>
  );

  const paginationbutton = (
    <TablePagination
      {...{
        setPageSize: table.setPageSize,
        setPageIndex: table.setPageIndex,
        getState: table.getState,
        getPageCount: table.getPageCount
      }}
    />
  );

  const rowbutton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );
  const secondary=(
    <Stack direction="row" alignItems="center" spacing={{ xs: 1, sm: 2 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <SelectColumnVisibility
        {...{
          getVisibleLeafColumns: table.getVisibleLeafColumns,
          getIsAllColumnsVisible: table.getIsAllColumnsVisible,
          getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
          getAllColumns: table.getAllColumns
        }}
      />
      <Button variant="contained" startIcon={<Add />} onClick={() => navigate('/leads/add')} size="large">
        Add Leads
      </Button>
      <CSVExport
        {...{
          data: table.getSortedRowModel().rows.map((d) => d.original),
          headers,
          filename: 'leads.csv'
        }}
      />
    </Stack>
  )
  const canAddJobRequest = true

  return (
    <CustomTableContainer table={table} onAddClick={() =>navigate('/leads/add')}  csvFilename="Leads-list.csv" showAddButton={canAddJobRequest} addLabel="Leads" data ={ data}rowSelection={rowSelection} theme={theme} />
  );
}

export default Placements;
