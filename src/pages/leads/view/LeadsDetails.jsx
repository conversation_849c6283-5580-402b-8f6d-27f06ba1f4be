import { Card, Divider, Grid } from '@mui/material';
import Typography from '@mui/material/Typography';
import { Box } from '@mui/system';

// project-imports

import Notes from 'pages/applicant-new/ViewApplicantInfo/ViewNotesInfo/Notes';
import LeadsPersonalDetails from '../view/ViewPersonalDetails';
import MeetingSchedule from 'pages/client-page/view/versiontwo/ViewSnapshotInfo/MeetingScheduleInfo/MeetingSchedule';
import ViewTasks from 'pages/client-page/view/versiontwo/ViewSnapshotInfo/ViewTasksInfo/ViewTasks';
// ==============================|| SAMPLE PAGE ||============================== //

export default function ViewLandDetails() {
  // This is the default export
  return (
 
      <>
        <LeadsPersonalDetails />
        <Notes />
        <MeetingSchedule />
        <ViewTasks/>
        {/* <Submissions/>
        <Einterviews/>
        <CallLogs/> */}
        
      </>
     
      
  
  );
}