import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON>, Divider, But<PERSON>, CardActions } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';
import { Link } from 'react-router-dom';

function ViewLeadsInfo() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      lead_name: '<PERSON>',
      contact_number: '+1 (800) 555-1234',
      ownership: 'Private',
      source: 'Referral',
      address_1: '123 Business Park Ave',
      address_2: 'Suite 400',
      country: 'USA',
      state: 'California',
      city: 'San Francisco',
      postal_code: '94107',
      website: 'https://examplebusiness.com',
      metro_system: 'Bay Area Rapid Transit',
      linkedin_url: 'https://linkedin.com/company/examplebusiness',
      number_of_employees: '250',
      revenue: '$25M',
      fiscal_year_end: 'December',
      industry_type: 'Technology',
      category: 'Software Services',
      sic: '7371',
      naics: '541511',
      number_of_locations: '3',
      description: 'A leading provider of enterprise software solutions with a focus on innovation and scalability.'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'lead_name', label: 'Lead Name' },
    { name: 'contact_number', label: 'Contact Number' },
    { name: 'ownership', label: 'Ownership' },
    { name: 'source', label: 'Source' },
    { name: 'address_1', label: 'Address 1' },
    { name: 'address_2', label: 'Address 2' },
    { name: 'country', label: 'Country' },
    { name: 'state', label: 'State' },
    { name: 'city', label: 'City' },
    { name: 'postal_code', label: 'Postal Code' },
    { name: 'website', label: 'Website' },
    { name: 'metro_system', label: 'Metro System' },
    { name: 'linkedin_url', label: 'LinkedIn URL' },
    { name: 'number_of_employees', label: 'Number of Employees' },
    { name: 'revenue', label: 'Revenue' },
    { name: 'fiscal_year_end', label: 'Fiscal Year End' },
    { name: 'industry_type', label: 'Industry Type' },
    { name: 'category', label: 'Category' },
    { name: 'sic', label: 'SIC' },
    { name: 'naics', label: 'NAICS' },
    { name: 'number_of_locations', label: 'Number of Locations' },
    { name: 'description', label: 'Description' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
     
   
        <CardActions
          sx={{
            position: 'sticky',
            top: 0,
            zIndex: 1,
            bgcolor: 'background.paper',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: 1,
            borderBottom: '1px solid #e0e0e0'
          }}
        >
          <Typography variant="h5" sx={{ m: 1, pl: 1.5 }}>
            View Leads
          </Typography>
          <Button variant="outlined" size="small" component={Link} to="/leads">
            Cancel
          </Button>
        </CardActions>
        <MainCard
        sx={{
          borderRadius: '0px',
          backgroundColor: 'white',
          border: 'none',
          maxHeight: '100vh',
          overflowY: 'auto' // Scrollable container
        }}>
        <Grid container spacing={3} sx={{ p: 2 }}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={0.2}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewLeadsInfo;