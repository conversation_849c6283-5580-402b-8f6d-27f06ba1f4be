import React, { useState, useEffect } from 'react';
 import {
  TextField,
  Button,
  Box,
  Grid,
  Typography,
  Divider,
  IconButton,
 } from '@mui/material';
 import { useForm, Controller, useWatch } from 'react-hook-form';
 import MainCard from 'components/MainCard';
 import Stack from '@mui/material/Stack';
 import axios from 'axios';
 import CustomInputLabel from 'components/custom-components/CustomInputLabel';
 import CustomNameField from 'components/custom-components/CustomNameField';
 import CustomEmailField from 'components/custom-components/CustomEmailField';
 import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
 import CustomOfficeNumberField from 'components/custom-components/CustomOfficeNumberField';
 import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
 import CustomAddressField from 'components/custom-components/CustomAddressField';
 import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
 import CustomAllCharactersField from 'components/custom-components/CustomAllCharactersField';
 import CustomDropdownFields from 'pages/jobrequest/CustomDropDown';
 import CustomAadharCardInput from 'components/custom-components/CustomAadharCardInput';
 import CustomPanCardInput from 'components/custom-components/CustomPanCardInput';
 import CustomCgpaInput from 'components/custom-components/CustomCgpaInput';
 import EditIcon from '@mui/icons-material/Edit';
 import SaveIcon from '@mui/icons-material/Save';
 import CancelIcon from '@mui/icons-material/Cancel';

 const LeadsPersonalDetails = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue, reset, getValues } = useForm({
    defaultValues: {
      no_of_employees: 'shashank',
      revenue: '20',
      sic: 'ss',
      fiscal_year_end: '2025',
      naics: '8888',
      number_of_locations: 'hyderabad',
      industry: 'IT',
      ownership_type: '-',
    },
  });
  console.log("Value of 'control' immediately after useForm:", control);
  const [industries, setIndustries] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);

  const onSubmit = (data) => {
    console.log('Form Data (Submitted):', data);
    setIsEditMode(false);
    // You can perform further actions here, such as sending the data to an API
  };

  const handleEditClick = () => {
    setIsEditMode(true);
  };

  const handleCancelClick = () => {
    setIsEditMode(false);
    reset(); // Reset form to the initially loaded dummy data
  };

  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        const response = await axios.get(`${API_URL}/industry`);
        console.log('📥 Industry API Response:', response.data);
        setIndustries(response.data);
      } catch (error) {
        console.error('❌ Error fetching industries:', error);
      }
    };
    fetchIndustries();
  }, []);

  const industryOptions = industries.map((industry) => ({
    value: industry.name, // Adjust based on actual API response
    label: industry.name,
  }));

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '0px', backgroundColor: 'white', border: 'none' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Lead Details</Typography>
          {!isEditMode ? (
            <IconButton onClick={handleEditClick}>
              <EditIcon />
            </IconButton>
          ) : (
            <Box>
              <IconButton type="submit" color="primary">
                <SaveIcon />
              </IconButton>
              <IconButton onClick={handleCancelClick}>
                <CancelIcon />
              </IconButton>
            </Box>
          )}
        </Box>
        <Divider sx={{ marginBottom: 2 }} />
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>
                Number of Employees
              </CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="no_of_employees"
                  control={control}
                  placeholder="Enter Number of Employees"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('no_of_employees')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Revenue</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="revenue"
                  control={control}
                  placeholder="Enter Revenue"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('revenue')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>SIC</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="sic"
                  control={control}
                  placeholder="Enter SIC"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('sic')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Fiscal Year End</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="fiscal_year_end"
                  control={control}
                  placeholder="Enter Fiscal Year End"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('fiscal_year_end')}</Typography>
              )}
          </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>NAICS</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="naics"
                  control={control}
                  placeholder="Enter NAICS"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('naics')}</Typography>
              )}
          </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Number of Locations</CustomInputLabel>
              {isEditMode ? (
                <CustomNameField
                  name="number_of_locations"
                  control={control}
                  placeholder="Enter Number of Locations"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('number_of_locations')}</Typography>
              )}
          </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Industry</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="industry"
                  control={control}
                  placeholder="Select Industry"
                  options={industryOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('industry')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={4}>
            <Stack spacing={0.2}>
              <CustomInputLabel>Ownership Type</CustomInputLabel>
              {isEditMode ? (
                <CustomDropdownField
                  name="ownership_type"
                  control={control}
                  placeholder="Select Ownership Type"
                  options={[
                    { value: 'Sole Proprietorship' },
                    { value: 'Partnership' },
                    { value: 'Corporation' },
                    { value: 'LLC' },
                    { value: 'Other' },
                  ]}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              ) : (
                <Typography>{getValues('ownership_type')}</Typography>
              )}
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
    </form>
  );
 };

 export default LeadsPersonalDetails;