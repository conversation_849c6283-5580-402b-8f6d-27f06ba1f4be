import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
// material-ui
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { Button, Box, CardActions, Typography, Stack, Snackbar, Alert, useMediaQuery, Divider } from '@mui/material';
import ContactsIcon from '@mui/icons-material/Contacts';
import UploadFileIcon from '@mui/icons-material/UploadFile';
// project-imports
import MainCard from 'components/MainCard';
import { Profile } from 'iconsax-react';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';
import JWTContext from 'contexts/JWTContext';
import LeadsPersonalDetails from './view/ViewLeadsInfo';
import Notes from 'pages/applicant-new/ViewApplicantInfo/ViewNotesInfo/Notes';
const API_URL = import.meta.env.VITE_APP_API_URL;
const HEADER_HEIGHT = 0; // Adjust if your header has a different height
import ViewLandDetails from './ViewLeads/LeadsDetails';
import ViewLeadsInfo from './view/ViewLeadsInfo';
import ViewActivities from 'pages/client-page/view/versiontwo/ViewSnapshotInfo/ViewActivitiesInfo/ViewActivities';
import ViewEmailActivity from 'pages/client-page/view/versiontwo/ViewSnapshotInfo/ViewEmailActivity/ViewEmailActivity';
function capitalizeFirstLetters(name) {
  return name
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
export default function ViewLeads() {
  const navigate = useNavigate();
  const theme = useTheme();
  const [selectedTab, setSelectedTab] = useState(0);
  const [file, setFileData] = useState(null);

  console.log('fileData*****', file);
  const [clientData, setClientData] = useState({});
  const [contacts, setContacts] = useState([]);
  const [submitForm, setSubmitForm] = useState(null);

  // ✅ Snackbar States
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };
  const handleChange = async (event, newValue) => {
    setSelectedTab(newValue);
  };
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useContext(JWTContext);
  const [userName, setUserName] = useState(capitalizeFirstLetters(user.full_name));
  const [validateClientForm, setValidateClientForm] = useState(null); // ✅ Store validation function

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <ViewLandDetails />;
      case 1:
        return <ViewActivities />;

      case 2:
        return <ViewEmailActivity />;
      default:
        return null;
    }
  };

  return (
    <>
      <>
        <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
          {isMobile && (
            <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h5" sx={{ m: 0 }}>
                  View Job Requisition
                </Typography>

                <Button variant="outlined" size="small" component={Link} to="/job_requisition">
                  Cancel
                </Button>
              </Box>

              <Divider sx={{ mt: 2 }} />
            </MainCard>
          )}

          {/* Sticky Card Actions (Submit & Cancel Buttons) */}
          <CardActions
            sx={{
              position: 'sticky',
              top: HEADER_HEIGHT, // ✅ Always stick at top
              bgcolor: 'background.default',
              zIndex: 1100, // ✅ Higher than tabs
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Box
                sx={{
                  width: '100%',
                  mt: {
                    xs: -3, // No margin top on extra-small screens
                    sm: '8px' // Apply from small screens and up
                  }
                }}
              >
                <Tabs
                  value={selectedTab}
                  onChange={handleChange}
                  aria-label="client information navigation"
                  variant="scrollable" // This is the key change to enable scrolling
                  scrollButtons="auto"
                >
                  <Tab label="Snapshot" icon={<Profile />} iconPosition="start" />
                  <Tab label="Activity " icon={<ContactsIcon />} iconPosition="start" />
                  <Tab label="Emails " icon={<ContactsIcon />} iconPosition="start" />
                </Tabs>
              </Box>
              {!isMobile && (
                <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                  <Button variant="outlined" size="small" component={Link} to="/leads">
                    Cancel
                  </Button>
                </Stack>
              )}
            </Stack>
          </CardActions>

          {/* Tabs as Navigation */}

          <ViewLeadsInfo />

          {/* Tab Content */}
          <Box sx={{ mt: 2.0 }}>{renderTabContent()}</Box>
        </MainCard>
        {/* ✅ Snackbar for Success/Error Messages */}
        <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
          <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </>
      {/* )} */}
    </>
  );
}
