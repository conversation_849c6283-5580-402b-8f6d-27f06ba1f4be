// // import React from 'react';
// // import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem } from '@mui/material';

// // const ITSkillsDialog = ({ open, onClose, skill, onSkillChange, onSave }) => {
// //   const yearsRange = Array.from({ length: 25 }, (_, i) => 2024 - i);

// //   const handleSave = () => {
// //     console.log('Skill Data:', skill);
// //     onSave(); // Call the onSave prop function to handle the actual save logic
// //   };

// //   return (
// //     <Dialog open={open} onClose={onClose}>
// //       <DialogTitle>
// //         <Typography variant="h6" style={{ color: '#000' }}>IT skills</Typography>
// //       </DialogTitle>
// //       <DialogContent>
// //         <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
// //           Mention skills like programming languages (Java, Python), softwares (Microsoft Word, Excel) and more, to show your technical expertise.
// //         </Typography>

// //         <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Skill / Software name</Typography>
// //         <TextField
// //           fullWidth
// //           variant="outlined"
// //           margin="dense"
// //           name="name"
// //           value={skill.name}
// //           onChange={onSkillChange}
// //         />

// //         <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
// //           <div style={{ flex: 1, marginRight: '10px' }}>
// //             <Typography variant="body2" style={{ color: '#000' }}>Software version</Typography>
// //             <TextField
// //               fullWidth
// //               variant="outlined"
// //               margin="dense"
// //               name="version"
// //               value={skill.version}
// //               onChange={onSkillChange}
// //             />
// //           </div>
// //           <div style={{ flex: 1 }}>
// //             <Typography variant="body2" style={{ color: '#000' }}>Last used</Typography>
// //             <TextField
// //               select
// //               fullWidth
// //               variant="outlined"
// //               margin="dense"
// //               name="lastUsed"
// //               value={skill.lastUsed}
// //               onChange={onSkillChange}
// //             >
// //               {yearsRange.map((year) => (
// //                 <MenuItem key={year} value={year}>{year}</MenuItem>
// //               ))}
// //             </TextField>
// //           </div>
// //         </div>

// //         <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Experience</Typography>
// //         <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
// //           <div style={{ flex: 1, marginRight: '10px' }}>
// //             <Typography variant="body2" style={{ color: '#000' }}>Years</Typography>
// //             <TextField
// //               select
// //               fullWidth
// //               variant="outlined"
// //               margin="dense"
// //               name="experienceYears"
// //               value={skill.experience.years}
// //               onChange={(event) => onSkillChange({ target: { name: 'experienceYears', value: event.target.value } })}
// //             >
// //               {Array.from({ length: 51 }, (_, i) => (
// //                 <MenuItem key={i} value={i}>{i}</MenuItem>
// //               ))}
// //             </TextField>
// //           </div>
// //           <div style={{ flex: 1 }}>
// //             <Typography variant="body2" style={{ color: '#000' }}>Months</Typography>
// //             <TextField
// //               select
// //               fullWidth
// //               variant="outlined"
// //               margin="dense"
// //               name="experienceMonths"
// //               value={skill.experience.months}
// //               onChange={(event) => onSkillChange({ target: { name: 'experienceMonths', value: event.target.value } })}
// //             >
// //               {Array.from({ length: 12 }, (_, i) => (
// //                 <MenuItem key={i} value={i}>{i}</MenuItem>
// //               ))}
// //             </TextField>
// //           </div>
// //         </div>

// //         <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
// //           <Button onClick={onClose} color="primary">
// //             Cancel
// //           </Button>
// //           <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
// //             Save
// //           </Button>
// //         </div>
// //       </DialogContent>
// //     </Dialog>
// //   );
// // };

// // export default ITSkillsDialog;

// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';

// const ITSkillsDialog = ({ open, onClose, onSave }) => {
//   const yearsRange = Array.from({ length: 25 }, (_, i) => 2024 - i);
//   const [skill, setITSkill] = useState({ 
//     name: '', 
//     version: '', 
//     last_used: null, 
//     experience_years: null, 
//     experience_months: null,
//     id: null 
//   });
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   // Fetch IT skills data when the dialog opens
//   useEffect(() => {
//     const fetchITSkills = async () => {
//       if (open) {
//         try {
//           const token = localStorage.getItem('serviceToken');
//           const response = await axios.get('http://127.0.0.1:8000/users/get/it-skills', {
//             headers: {
//               Authorization: `Bearer ${token}`,
//             },
//           });

//           if (response.status === 200 && response.data.length > 0) {
//             const fetchedSkill = response.data[0];
//             setITSkill({
//               name: fetchedSkill.name || '',
//               version: fetchedSkill.version || '',
//               last_used: fetchedSkill.last_used || null,
//               experience_years: fetchedSkill.experience_years || null,
//               experience_months: fetchedSkill.experience_months || null,
//               id: fetchedSkill.id || null
//             });
//           }
//         } catch (error) {
//           console.error('Error fetching IT skills:', error);
//         }
//       }
//     };

//     fetchITSkills();
//   }, [open]);

//   const handleSave = async () => {
//     console.log('Skill Data Before Sending:', skill);
  
//     try {
//       const token = localStorage.getItem('serviceToken');
  
//       // Construct the payload
//       const payload = {
//         id: skill.id,
//         name: skill.name,
//         version: skill.version,
//         last_used: skill.last_used,
//         experience_years: skill.experience_years,
//         experience_months: skill.experience_months,
//       };
  
//       // Log the payload to ensure it's correctly populated
//       console.log('Payload to be sent:', [payload]);
  
//       // Ensure that the endpoint matches exactly what the backend expects
//       const response = await axios.patch(
//         `http://127.0.0.1:8000/users/update/me/it-skills`,  // Correct endpoint for IT skills
//         [payload],  // Send as an array since the backend expects a list
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );
  
//       // Check the response
//       if (response.status === 200) {
//         setSnackbarMessage('Skill saved successfully!');
//         setSnackbarSeverity('success');
//         onSave(); // Call the onSave prop function to handle any additional logic after saving
//       } else {
//         console.error('Failed to save IT skill:', response);
//         setSnackbarMessage('Failed to save skill!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skill:', error);
//       setSnackbarMessage('Error saving skill!');
//       setSnackbarSeverity('error');
//     }
  
//     setSnackbarOpen(true);
//     onClose();
//   };
  

//   const handleSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => ({
//       ...prevSkill,
//       [name]: value,
//     }));
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   return (
//     <>
//       <Dialog open={open} onClose={onClose}>
//         <DialogTitle>
//           <Typography variant="h6" style={{ color: '#000' }}>IT skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
//             Mention skills like programming languages (Java, Python), software (Microsoft Word, Excel) and more, to show your technical expertise.
//           </Typography>

//           <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Skill / Software name</Typography>
//           <TextField
//             fullWidth
//             variant="outlined"
//             margin="dense"
//             name="name"
//             value={skill.name}
//             onChange={handleSkillChange}
//           />

//           <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
//             <div style={{ flex: 1, marginRight: '10px' }}>
//               <Typography variant="body2" style={{ color: '#000' }}>Software version</Typography>
//               <TextField
//                 fullWidth
//                 variant="outlined"
//                 margin="dense"
//                 name="version"
//                 value={skill.version}
//                 onChange={handleSkillChange}
//               />
//             </div>
//             <div style={{ flex: 1 }}>
//               <Typography variant="body2" style={{ color: '#000' }}>Last used</Typography>
//               <TextField
//                 select
//                 fullWidth
//                 variant="outlined"
//                 margin="dense"
//                 name="last_used"
//                 value={skill.last_used}
//                 onChange={handleSkillChange}
//               >
//                 {yearsRange.map((year) => (
//                   <MenuItem key={year} value={year}>{year}</MenuItem>
//                 ))}
//               </TextField>
//             </div>
//           </div>

//           <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Experience</Typography>
//           <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
//             <div style={{ flex: 1, marginRight: '10px' }}>
//               <Typography variant="body2" style={{ color: '#000' }}>Years</Typography>
//               <TextField
//                 select
//                 fullWidth
//                 variant="outlined"
//                 margin="dense"
//                 name="experience_years"
//                 value={skill.experience_years}
//                 onChange={(event) => handleSkillChange({ target: { name: 'experience_years', value: event.target.value } })}
//               >
//                 {Array.from({ length: 51 }, (_, i) => (
//                   <MenuItem key={i} value={i}>{i}</MenuItem>
//                 ))}
//               </TextField>
//             </div>
//             <div style={{ flex: 1 }}>
//               <Typography variant="body2" style={{ color: '#000' }}>Months</Typography>
//               <TextField
//                 select
//                 fullWidth
//                 variant="outlined"
//                 margin="dense"
//                 name="experience_months"
//                 value={skill.experience_months}
//                 onChange={(event) => handleSkillChange({ target: { name: 'experience_months', value: event.target.value } })}
//               >
//                 {Array.from({ length: 12 }, (_, i) => (
//                   <MenuItem key={i} value={i}>{i}</MenuItem>
//                 ))}
//               </TextField>
//             </div>
//           </div>

//           <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={onClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <MuiAlert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </MuiAlert>
//       </Snackbar>
//     </>
//   );
// };

// export default ITSkillsDialog;



import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem, Snackbar } from '@mui/material';
import MuiAlert from '@mui/material/Alert';
import axios from 'axios';

const ITSkillsDialog = ({ open, onClose, onSave }) => {
  const yearsRange = Array.from({ length: 25 }, (_, i) => 2024 - i);
  const [skill, setITSkill] = useState({ 
    id: null,
    name: '', 
    version: '', 
    last_used: null, 
    experience_years: null, 
    experience_months: null
  });
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  useEffect(() => {
    const fetchITSkills = async () => {
      if (open) {
        try {
          const token = localStorage.getItem('serviceToken');
          const response = await axios.get('http://localhost:8080/users/get/it-skills', {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.status === 200 && response.data.length > 0) {
            const fetchedSkill = response.data[0];
            setITSkill({
              id: fetchedSkill.id || null,
              name: fetchedSkill.name || '',
              version: fetchedSkill.version || '',
              last_used: fetchedSkill.last_used || null,
              experience_years: fetchedSkill.experience_years || null,
              experience_months: fetchedSkill.experience_months || null
            });
          }
        } catch (error) {
          console.error('Error fetching IT skills:', error);
        }
      }
    };

    fetchITSkills();
  }, [open]);

  const handleSave = async () => {
    console.log('Skill Data Before Sending:', skill);
  
    try {
      const token = localStorage.getItem('serviceToken');
  
      // Construct the payload
      const payload = [
        {
          id: skill.id,
          name: skill.name,
          version: skill.version,
          last_used: skill.last_used,
          experience_years: skill.experience_years,
          experience_months: skill.experience_months
        }
      ];
  
      console.log('Payload to be sent:', JSON.stringify(payload, null, 2));
  
      const response = await axios.patch(
        `http://localhost:8080/users/update/me/it-skills`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
  
      if (response.status === 200) {
        setSnackbarMessage('Skill saved successfully!');
        setSnackbarSeverity('success');
        onSave(); 
      } else {
        console.error('Failed to save IT skill:', response);
        setSnackbarMessage('Failed to save skill!');
        setSnackbarSeverity('error');
      }
    } catch (error) {
      console.error('Error saving IT skill:', error);
      setSnackbarMessage('Error saving skill!');
      setSnackbarSeverity('error');
    }
  
    setSnackbarOpen(true);
    onClose();
  };

  const handleSkillChange = (event) => {
    const { name, value } = event.target;
    setITSkill((prevSkill) => ({
      ...prevSkill,
      [name]: value,
    }));
  };

  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>
          <Typography variant="h6" style={{ color: '#000' }}>IT skills</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
            Mention skills like programming languages (Java, Python), software (Microsoft Word, Excel) and more, to show your technical expertise.
          </Typography>

          <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Skill / Software name</Typography>
          <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="name"
          value={skill.name}
          onChange={handleSkillChange}
          disabled={!!skill.id}  // Disable the name field if the skill already exists
        />


          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
            <div style={{ flex: 1, marginRight: '10px' }}>
              <Typography variant="body2" style={{ color: '#000' }}>Software version</Typography>
              <TextField
                fullWidth
                variant="outlined"
                margin="dense"
                name="version"
                value={skill.version}
                onChange={handleSkillChange}
              />
            </div>
            <div style={{ flex: 1 }}>
              <Typography variant="body2" style={{ color: '#000' }}>Last used</Typography>
              <TextField
                select
                fullWidth
                variant="outlined"
                margin="dense"
                name="last_used"
                value={skill.last_used}
                onChange={handleSkillChange}
              >
                {yearsRange.map((year) => (
                  <MenuItem key={year} value={year}>{year}</MenuItem>
                ))}
              </TextField>
            </div>
          </div>

          <Typography variant="body2" style={{ marginTop: '10px', color: '#000' }}>Experience</Typography>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
            <div style={{ flex: 1, marginRight: '10px' }}>
              <Typography variant="body2" style={{ color: '#000' }}>Years</Typography>
              <TextField
                select
                fullWidth
                variant="outlined"
                margin="dense"
                name="experience_years"
                value={skill.experience_years}
                onChange={(event) => handleSkillChange({ target: { name: 'experience_years', value: event.target.value } })}
              >
                {Array.from({ length: 51 }, (_, i) => (
                  <MenuItem key={i} value={i}>{i}</MenuItem>
                ))}
              </TextField>
            </div>
            <div style={{ flex: 1 }}>
              <Typography variant="body2" style={{ color: '#000' }}>Months</Typography>
              <TextField
                select
                fullWidth
                variant="outlined"
                margin="dense"
                name="experience_months"
                value={skill.experience_months}
                onChange={(event) => handleSkillChange({ target: { name: 'experience_months', value: event.target.value } })}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <MenuItem key={i} value={i}>{i}</MenuItem>
                ))}
              </TextField>
            </div>
          </div>

          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button onClick={onClose} color="primary">
              Cancel
            </Button>
            <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
        <MuiAlert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </MuiAlert>
      </Snackbar>
    </>
  );
};

export default ITSkillsDialog;
