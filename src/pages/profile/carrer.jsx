import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem, RadioGroup, FormControlLabel, Radio } from '@mui/material';
import axios from 'axios';

const statesInIndia = [
  "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh",
  "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka",
  "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram",
  "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu",
  "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal",
  "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu",
  "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir"
];

const industries = [
  "Analytics / KPO / Research", "BPO / Call Centre", "IT Services", "IT Services & Consulting", 
  "Technology", "Electronic Components / Semiconductors", "Electronics Manufacturing", 
  "Electronic Manufacturing Services (EMS)", "Emerging Technologies", "3D Printing", 
  "AI/ML", "AR/VR", "Blockchain", "Cloud", "Cybersecurity", "Drones/Robotics", "IoT", 
  "Nanotechnology", "Hardware & Networking", "Internet", "E-Commerce", "OTT", "Software Product", 
  "BFSI", "Banking", "Financial Services", "Asset Management", "Broking", "FinTech / Payments", 
  "Insurance", "Investment Banking / Venture Capital / Private Equity", "NBFC", "Micro Finance", 
  "Education", "Education / Training", "E-Learning / EdTech", "Manufacturing & Production", 
  "Auto Components", "Tyre", "Automobile", "Automobile Dealers", "Electric Vehicle (EV)", 
  "Building Material", "Cement", "Ceramic", "Glass", "Chemicals", "Paints", "Defence & Aerospace", 
  "Electrical Equipment", "Fertilizers / Pesticides / Agro chemicals", "Industrial Automation", 
  "Industrial Equipment / Machinery", "Construction Equipment", "Machine Tools", "Iron & Steel", 
  "Metals & Mining", "Packaging & Containers", "Petrochemical / Plastics / Rubber", 
  "Pulp & Paper", "Infrastructure, Transport & Real Estate", "Aviation", "Courier / Logistics", 
  "Logistics Tech", "Engineering & Construction", "Oil & Gas", "Ports & Shipping", 
  "Shipbuilding", "Power", "Hydro", "Nuclear", "Solar", "Wind"
];

const departments = [
  "BFSI, Investments & Trading", "Customer Success, Service & Operations", "Data Science & Analytics", 
  "Engineering - Hardware & Networks", "Engineering - Software & QA", "Finance & Accounting", 
  "Human Resources", "IT & Information Security", "Marketing & Communication", 
  "Product Management", "Production, Manufacturing & Engineering", "Project & Program Management", 
  "Quality Assurance", "Sales & Business Development", "UX, Design & Architecture", 
  "Administration & Facilities", "Aviation & Aerospace", "Construction & Site Engineering", 
  "Consulting", "Content, Editorial & Journalism", "CSR & Social Service", "Energy & Mining", 
  "Environment Health & Safety", "Food, Beverage & Hospitality", "Healthcare & Life Sciences", 
  "Legal & Regulatory", "Media Production & Entertainment", "Merchandising, Retail & eCommerce", 
  "Procurement & Supply Chain", "Research & Development", "Risk Management & Compliance", 
  "Security Services", "Shipping & Maritime", "Sports, Fitness & Personal Care", 
  "Strategic & Top Management", "Teaching & Training", "Other"
];

const CareerProfileDialog = ({ open, onClose, onSave }) => {
  const [careerProfile, setCareerProfile] = useState({
    currentIndustry: '',
    department: '',
    jobRole: '',
    jobType: 'Permanent',
    employmentType: 'Full time',
    shift: '',
    preferredLocation: '',
    salaryCurrency: '₹',
    expectedSalary: ''
  });

  // Fetch career profile data when the dialog opens
  useEffect(() => {
    if (open) {
      const fetchCareerProfile = async () => {
        try {
          const token = localStorage.getItem('serviceToken');
          const response = await axios.get('http://localhost:8080/users/get/career-profile', {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          if (response.status === 200 && response.data) {
            setCareerProfile({
              ...response.data,
              currentIndustry: response.data.current_industry || '', // Map backend fields to frontend state
              jobRole: response.data.job_role || '',
              jobType: response.data.job_type || 'Permanent',
              employmentType: response.data.employment_type || 'Full time',
              preferredLocation: response.data.preferred_location || ''
            });
          }
        } catch (error) {
          console.error('Error fetching career profile:', error);
        }
      };

      fetchCareerProfile();
    }
  }, [open]);

  const handleCareerProfileChange = (event) => {
    const { name, value } = event.target;
    setCareerProfile(prevProfile => ({
      ...prevProfile,
      [name]: value
    }));
  };

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      const response = await axios.patch('http://localhost:8080/users/update/me/career-profile', {
        current_industry: careerProfile.currentIndustry,
        department: careerProfile.department,
        job_role: careerProfile.jobRole,
        job_type: careerProfile.jobType,
        employment_type: careerProfile.employmentType,
        shift: careerProfile.shift,
        preferred_location: careerProfile.preferredLocation,
        salary_currency: careerProfile.salaryCurrency,
        expected_salary: careerProfile.expectedSalary
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      if (response.status === 200) {
        console.log('Career profile saved:', response.data);
        onSave(response.data); // Pass the updated profile to the parent component
        onClose(); // Close the dialog
      }
    } catch (error) {
      console.error('Error saving career profile:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6" style={{ color: '#000' }}>Career profile</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
          Add details about your current and preferred job profile. This helps us personalize your job recommendations.
        </Typography>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px' }}>Current industry</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="currentIndustry"
          value={careerProfile.currentIndustry}
          onChange={handleCareerProfileChange}
        >
          {industries.map((industry, index) => (
            <MenuItem key={index} value={industry}>{industry}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Department</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="department"
          value={careerProfile.department}
          onChange={handleCareerProfileChange}
        >
          {departments.map((department, index) => (
            <MenuItem key={index} value={department}>{department}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Job role</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="jobRole"
          value={careerProfile.jobRole}
          onChange={handleCareerProfileChange}
          placeholder="Select your job role"
        />

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Desired job type</Typography>
        <RadioGroup
          name="jobType"
          value={careerProfile.jobType}
          onChange={handleCareerProfileChange}
          row
        >
          <FormControlLabel value="Permanent" control={<Radio />} label="Permanent" />
          <FormControlLabel value="Contractual" control={<Radio />} label="Contractual" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Desired employment type</Typography>
        <RadioGroup
          name="employmentType"
          value={careerProfile.employmentType}
          onChange={handleCareerProfileChange}
          row
        >
          <FormControlLabel value="Full time" control={<Radio />} label="Full time" />
          <FormControlLabel value="Part time" control={<Radio />} label="Part time" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Preferred shift</Typography>
        <RadioGroup
          name="shift"
          value={careerProfile.shift}
          onChange={handleCareerProfileChange}
          row
        >
          <FormControlLabel value="Day" control={<Radio />} label="Day" />
          <FormControlLabel value="Night" control={<Radio />} label="Night" />
          <FormControlLabel value="Flexible" control={<Radio />} label="Flexible" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Preferred work location (Max 10)</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="preferredLocation"
          value={careerProfile.preferredLocation}
          onChange={handleCareerProfileChange}
        >
          {statesInIndia.map((state, index) => (
            <MenuItem key={index} value={state}>{state}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000', marginBottom: '5px', marginTop: '15px' }}>Expected salary</Typography>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            select
            variant="outlined"
            margin="dense"
            name="salaryCurrency"
            value={careerProfile.salaryCurrency}
            onChange={handleCareerProfileChange}
            style={{ width: '80px', marginRight: '10px' }}
          >
            <MenuItem value="₹">₹</MenuItem>
            <MenuItem value="$">$</MenuItem>
            <MenuItem value="€">€</MenuItem>
          </TextField>
          <TextField
            fullWidth
            variant="outlined"
            margin="dense"
            name="expectedSalary"
            value={careerProfile.expectedSalary}
            onChange={handleCareerProfileChange}
            placeholder="Eg. 4,50,000"
          />
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
          <Button onClick={onClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CareerProfileDialog;
