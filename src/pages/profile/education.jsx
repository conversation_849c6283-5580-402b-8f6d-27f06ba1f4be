// import React, { useState } from 'react';
// import {
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   TextField,
//   Button,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   FormControl,
//   Typography,
//   Select,
//   MenuItem,
//   InputLabel,
//   Grid,
//   FormLabel
// } from '@mui/material';

// const styles = {
//   dialogContent: {
//     display: 'flex',
//     flexDirection: 'column',
//     gap: '15px',
//   },
//   formControl: {
//     marginTop: '15px',
//     minWidth: 120,
//   },
// };

// const years = Array.from(new Array(25), (val, index) => 2024 - index);

// const EducationDialog = ({ open, handleClose }) => {
//   const [education, setEducation] = useState('');
//   const [board, setBoard] = useState('');
//   const [passingOutYear, setPassingOutYear] = useState('');
//   const [schoolMedium, setSchoolMedium] = useState('');
//   const [marks, setMarks] = useState('');
//   const [englishMarks, setEnglishMarks] = useState('');
//   const [mathsMarks, setMathsMarks] = useState('');
//   const [institute, setInstitute] = useState('');
//   const [course, setCourse] = useState('');
//   const [specialization, setSpecialization] = useState('');
//   const [courseType, setCourseType] = useState('Full time');
//   const [startYear, setStartYear] = useState('');
//   const [endYear, setEndYear] = useState('');
//   const [gradingSystem, setGradingSystem] = useState('');

//   const handleSave = () => {
//     // Save logic here
//     handleClose();
//   };

//   const handleEducationChange = (event) => {
//     setEducation(event.target.value);
//   };

//   const handleGradingSystemChange = (event) => {
//     setGradingSystem(event.target.value);
//   };

//   return (
//     <Dialog open={open} onClose={handleClose}>
//       <DialogTitle>
//         <Typography variant="h6">Education</Typography>
//       </DialogTitle>
//       <DialogContent style={styles.dialogContent}>
//         <Grid container spacing={1}>
//           <Grid item xs={12} sm={12}>
//             <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//               Education<span style={{ color: 'red' }}>*</span>
//             </Typography>
//             <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//               <Select
//                 value={education}
//                 onChange={handleEducationChange}
//                 displayEmpty
//               >
//                 <MenuItem value="" disabled>Select education</MenuItem>
//                 <MenuItem value="10th">10th</MenuItem>
//                 <MenuItem value="12th">12th</MenuItem>
//                 <MenuItem value="doctorate">Doctorate/PhD</MenuItem>
//                 <MenuItem value="masters">Masters/Post-Graduation</MenuItem>
//                 <MenuItem value="graduation">Graduation/Diploma</MenuItem>
//               </Select>
//             </FormControl>
//           </Grid>
//           {education === '10th' && (
//             <>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Board<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={board}
//                     onChange={(e) => setBoard(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select board</MenuItem>
//                     <MenuItem value="CBSE">CBSE</MenuItem>
//                     <MenuItem value="ICSE">ICSE</MenuItem>
//                     <MenuItem value="State Board">State Board</MenuItem>
//                     <MenuItem value="Other">Other</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Passing Out Year<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={passingOutYear}
//                     onChange={(e) => setPassingOutYear(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select passing out year</MenuItem>
//                     {years.map((year) => (
//                       <MenuItem key={year} value={year}>{year}</MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   School Medium<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={schoolMedium}
//                     onChange={(e) => setSchoolMedium(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select medium</MenuItem>
//                     <MenuItem value="English">English</MenuItem>
//                     <MenuItem value="Hindi">Hindi</MenuItem>
//                     <MenuItem value="Other">Other</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Marks<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   placeholder="% marks of 100 maximum"
//                   value={marks}
//                   onChange={(e) => setMarks(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//             </>
//           )}
//           {education === '12th' && (
//             <>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Board<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={board}
//                     onChange={(e) => setBoard(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select board</MenuItem>
//                     <MenuItem value="CBSE">CBSE</MenuItem>
//                     <MenuItem value="ICSE">ICSE</MenuItem>
//                     <MenuItem value="State Board">State Board</MenuItem>
//                     <MenuItem value="Other">Other</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Passing Out Year<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={passingOutYear}
//                     onChange={(e) => setPassingOutYear(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select passing out year</MenuItem>
//                     {years.map((year) => (
//                       <MenuItem key={year} value={year}>{year}</MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   School Medium<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={schoolMedium}
//                     onChange={(e) => setSchoolMedium(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select medium</MenuItem>
//                     <MenuItem value="English">English</MenuItem>
//                     <MenuItem value="Hindi">Hindi</MenuItem>
//                     <MenuItem value="Other">Other</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Marks<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   placeholder="% marks of 100 maximum"
//                   value={marks}
//                   onChange={(e) => setMarks(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   English Marks
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   placeholder="Marks (out of 100)"
//                   value={englishMarks}
//                   onChange={(e) => setEnglishMarks(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Maths Marks
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   placeholder="Marks (out of 100)"
//                   value={mathsMarks}
//                   onChange={(e) => setMathsMarks(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//             </>
//           )}
//           {education !== '10th' && education !== '12th' && (
//             <>
//               <Grid item xs={12} sm={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   University/Institute<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   placeholder="Select university/institute"
//                   value={institute}
//                   onChange={(e) => setInstitute(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//               <Grid item xs={12} sm={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Course<span style={{ color: 'red' }}>*</span>
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={course}
//                     onChange={(e) => setCourse(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select course</MenuItem>
//                     <MenuItem value="Btech/BE">Btech/BE</MenuItem>
//                     <MenuItem value="B.com">B.com</MenuItem>
//                     <MenuItem value="B.sc">B.sc</MenuItem>
//                     <MenuItem value="B.A">B.A</MenuItem>
//                     <MenuItem value="Diploma">Diploma</MenuItem>
//                     <MenuItem value="b.Arch">b.Arch</MenuItem>
//                     <MenuItem value="BBA/BMS">BBA/BMS</MenuItem>
//                     <MenuItem value="B.Des">B.Des</MenuItem>
//                     <MenuItem value="B.ed">B.ed</MenuItem>
//                     <MenuItem value="B.pharma">B.pharma</MenuItem>
//                     <MenuItem value="BCA">BCA</MenuItem>
//                     <MenuItem value="BFA">BFA</MenuItem>
//                     <MenuItem value="BDS">BDS</MenuItem>
//                     <MenuItem value="BHMS">BHMS</MenuItem>
//                     <MenuItem value="BVSC">BVSC</MenuItem>
//                     <MenuItem value="LLB">LLB</MenuItem>
//                     <MenuItem value="MBBS">MBBS</MenuItem>
//                     <MenuItem value="ITI certificate">ITI certificate</MenuItem>
//                     <MenuItem value="Other Graduation">Other Graduation</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Specialization
//                 </Typography>
//                 <TextField
//                   fullWidth
//                   value={specialization}
//                   onChange={(e) => setSpecialization(e.target.value)}
//                   variant="outlined"
//                   sx={{ mb: 2 }}
//                 />
//               </Grid>
//               <Grid item xs={12}>
//                 <FormControl component="fieldset" sx={{ mb: 2 }}>
//                   <FormLabel component="legend" sx={{ mb: 1 }}>
//                     <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                       Course Type
//                     </Typography>
//                   </FormLabel>
//                   <RadioGroup
//                     row
//                     value={courseType}
//                     onChange={(e) => setCourseType(e.target.value)}
//                   >
//                     <FormControlLabel value="Full time" control={<Radio />} label="Full time" />
//                     <FormControlLabel value="Part time" control={<Radio />} label="Part time" />
//                   </RadioGroup>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Start Year
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={startYear}
//                     onChange={(e) => setStartYear(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select start year</MenuItem>
//                     {years.map((year) => (
//                       <MenuItem key={year} value={year}>{year}</MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   End Year
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={endYear}
//                     onChange={(e) => setEndYear(e.target.value)}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select end year</MenuItem>
//                     {years.map((year) => (
//                       <MenuItem key={year} value={year}>{year}</MenuItem>
//                     ))}
//                   </Select>
//                 </FormControl>
//               </Grid>
//               <Grid item xs={12} sm={12}>
//                 <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                   Grading System
//                 </Typography>
//                 <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
//                   <Select
//                     value={gradingSystem}
//                     onChange={handleGradingSystemChange}
//                     displayEmpty
//                   >
//                     <MenuItem value="" disabled>Select grading system</MenuItem>
//                     <MenuItem value="Scale 10 Grading System">Scale 10 Grading System</MenuItem>
//                     <MenuItem value="Scale 4 Grading System">Scale 4 Grading System</MenuItem>
//                     <MenuItem value="% Marks of 100 Maximum">% Marks of 100 Maximum</MenuItem>
//                     <MenuItem value="Course Requires a Pass">Course Requires a Pass</MenuItem>
//                   </Select>
//                 </FormControl>
//               </Grid>
//               {(gradingSystem === "Scale 10 Grading System" || gradingSystem === "Scale 4 Grading System" || gradingSystem === "% Marks of 100 Maximum") && (
//                 <Grid item xs={12} sm={12}>
//                   <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
//                     Marks<span style={{ color: 'red' }}>*</span>
//                   </Typography>
//                   <TextField
//                     fullWidth
//                     placeholder="Enter your marks"
//                     value={marks}
//                     onChange={(e) => setMarks(e.target.value)}
//                     variant="outlined"
//                     sx={{ mb: 2 }}
//                   />
//                 </Grid>
//               )}
//             </>
//           )}
//         </Grid>
//         <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
//           <Button onClick={handleClose} color="primary">
//             Cancel
//           </Button>
//           <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//             Save
//           </Button>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default EducationDialog;
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  TextField,
  Button,
  FormControl,
  Typography,
  Grid,
  IconButton,
  MenuItem,
  Divider,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const years = Array.from(new Array(25), (val, index) => 2024 - index);

const EducationDialog = ({ open, handleClose }) => {
  const [selectedEducation, setSelectedEducation] = useState(null);
  const [educationData, setEducationData] = useState({
    '10th': [],
    '12th': [],
    graduation: [],
    masters: [],
    doctorate: [],
  });

  // State for form fields
  const [board, setBoard] = useState('');
  const [passingOutYear, setPassingOutYear] = useState('');
  const [schoolMedium, setSchoolMedium] = useState('');
  const [marks, setMarks] = useState('');
  const [englishMarks, setEnglishMarks] = useState('');
  const [mathsMarks, setMathsMarks] = useState('');
  const [institute, setInstitute] = useState('');
  const [course, setCourse] = useState('');
  const [specialization, setSpecialization] = useState('');
  const [courseType, setCourseType] = useState('Full time');
  const [startYear, setStartYear] = useState('');
  const [endYear, setEndYear] = useState('');
  const [gradingSystem, setGradingSystem] = useState('');

  const token = localStorage.getItem('serviceToken'); // Assume token is stored in localStorage

  // Fetch existing education data when the component mounts
  useEffect(() => {
    const fetchEducationData = async () => {
      const response = await fetch('http://localhost:8000/users/get/education', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const formattedData = data.reduce((acc, edu) => {
          acc[edu.education_level] = acc[edu.education_level] || [];
          acc[edu.education_level].push(edu);
          return acc;
        }, {});
        setEducationData(formattedData);
      } else {
        console.error('Failed to fetch education data');
      }
    };

    fetchEducationData();
  }, [token]);

  const setFormFieldValues = (eduData) => {
    console.log('Education Data:', eduData);  // Log the entire eduData object
    console.log('Grading System:', eduData.grading_system);
    setBoard(eduData.board || '');
    setPassingOutYear(eduData.passing_out_year || '');
    setSchoolMedium(eduData.school_medium || '');
    setMarks(eduData.marks || '');
    setEnglishMarks(eduData.english_marks || '');
    setMathsMarks(eduData.maths_marks || '');
    setInstitute(eduData.institute || '');
    setCourse(eduData.course || '');
    setSpecialization(eduData.specialization || '');
    setCourseType(eduData.course_type || 'Full time');
    setStartYear(eduData.start_year || '');
    setEndYear(eduData.end_year || '');
    setGradingSystem(eduData.grading_system || '');
  };

  const resetFormFields = () => {
    setBoard('');
    setPassingOutYear('');
    setSchoolMedium('');
    setMarks('');
    setEnglishMarks('');
    setMathsMarks('');
    setInstitute('');
    setCourse('');
    setSpecialization('');
    setCourseType('Full time');
    setStartYear('');
    setEndYear('');
    setGradingSystem('');
  };

  const handleSave = async () => {
    const newEntry = {
      education_level: selectedEducation,
      board,
      passing_out_year: passingOutYear ? parseInt(passingOutYear, 10) : 0, 
      school_medium: schoolMedium,
      marks: parseFloat(marks) || null,
      english_marks: parseFloat(englishMarks) || null,
      maths_marks: parseFloat(mathsMarks) || null,
      institute,
      course,
      specialization,
      course_type: courseType,
      start_year: parseInt(startYear, 10) || null,
      end_year: parseInt(endYear, 10) || null,
      grading_system: gradingSystem,  // Ensure this field is included in the save payload
    };
  
    // Save or update logic as before
    // ...


    try {
      const token = localStorage.getItem('serviceToken');
      const existingEntry = educationData[selectedEducation]?.[0]; // Use the first entry if it exists

      let response;
      if (existingEntry) {
        // Update existing entry via PATCH
        response = await fetch('http://localhost:8000/users/update/me/education', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify([newEntry]), // Ensure the newEntry is inside an array
        });
      } else {
        // Create a new entry
        response = await fetch('http://localhost:8000/users/update/me/education', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify([newEntry]), // Ensure the newEntry is inside an array
        });
      }

      if (!response.ok) {
        throw new Error('Failed to update education details');
      }

      // Refetch the data after saving to update the UI with the latest information
      const updatedResponse = await fetch('http://localhost:8000/users/get/education', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (updatedResponse.ok) {
        const updatedData = await updatedResponse.json();
        const formattedData = updatedData.reduce((acc, edu) => {
          acc[edu.education_level] = acc[edu.education_level] || [];
          acc[edu.education_level].push(edu);
          return acc;
        }, {});
        setEducationData(formattedData);
      }

      // Reset the form fields and close the dialog
      resetFormFields();
      setSelectedEducation(null);
    } catch (error) {
      console.error('Error saving education details:', error);
    }
  };

  const handleEducationClick = (level) => {
    setSelectedEducation(level);
    if (educationData[level]?.length > 0) {
      setFormFieldValues(educationData[level][0]);
    } else {
      // Reset form if no data is found for the selected education level
      resetFormFields();
    }
  };

  const renderEducationDetails = (level) => {
    return (educationData[level] || []).map((item, index) => (
      <Typography key={index} variant="body2" style={{ marginLeft: '20px', marginTop: '10px' }}>
        {level === '10th' || level === '12th'
          ? `${item.board} | ${item.passing_out_year}`
          : `${item.institute} | ${item.start_year}-${item.end_year} | ${item.course_type}`}
      </Typography>
    ));
  };

  const renderDetailedDialog = () => (
    <Dialog open={selectedEducation !== null} onClose={() => setSelectedEducation(null)} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">Education</Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={() => setSelectedEducation(null)}
          aria-label="close"
          style={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '20px', color: '#6c757d' }}>
          Details like course, university, and more, help recruiters identify your educational background.
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
              Education<span style={{ color: 'red' }}>*</span>
            </Typography>
            <TextField
              fullWidth
              variant="outlined"
              margin="normal"
              value={selectedEducation} // Pre-set to the selected education level
              InputProps={{
                readOnly: true, // Makes the field non-editable
              }}
            />
          </Grid>

          {selectedEducation === '10th' && (
            <>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Board<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={board}
                    onChange={(e) => setBoard(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select board
                    </MenuItem>
                    <MenuItem value="CBSE">CBSE</MenuItem>
                    <MenuItem value="ICSE">ICSE</MenuItem>
                    <MenuItem value="State Board">State Board</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Passing Out Year<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={passingOutYear}
                    onChange={(e) => setPassingOutYear(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select passing out year
                    </MenuItem>
                    {years.map((year) => (
                      <MenuItem key={year} value={year}>
                        {year}
                      </MenuItem>
                    ))}
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  School Medium<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={schoolMedium}
                    onChange={(e) => setSchoolMedium(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select medium
                    </MenuItem>
                    <MenuItem value="English">English</MenuItem>
                    <MenuItem value="Hindi">Hindi</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Marks<span style={{ color: 'red' }}>*</span>
                </Typography>
                <TextField
                  fullWidth
                  placeholder="% marks of 100 maximum"
                  value={marks}
                  onChange={(e) => setMarks(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
            </>
          )}

          {selectedEducation === '12th' && (
            <>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Board<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={board}
                    onChange={(e) => setBoard(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select board
                    </MenuItem>
                    <MenuItem value="CBSE">CBSE</MenuItem>
                    <MenuItem value="ICSE">ICSE</MenuItem>
                    <MenuItem value="State Board">State Board</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Passing Out Year<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={passingOutYear}
                    onChange={(e) => setPassingOutYear(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select passing out year
                    </MenuItem>
                    {years.map((year) => (
                      <MenuItem key={year} value={year}>
                        {year}
                      </MenuItem>
                    ))}
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  School Medium<span style={{ color: 'red' }}>*</span>
                </Typography>
                <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                  <TextField
                    fullWidth
                    select
                    value={schoolMedium}
                    onChange={(e) => setSchoolMedium(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select medium
                    </MenuItem>
                    <MenuItem value="English">English</MenuItem>
                    <MenuItem value="Hindi">Hindi</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Marks<span style={{ color: 'red' }}>*</span>
                </Typography>
                <TextField
                  fullWidth
                  placeholder="% marks of 100 maximum"
                  value={marks}
                  onChange={(e) => setMarks(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  English Marks
                </Typography>
                <TextField
                  fullWidth
                  placeholder="Marks (out of 100)"
                  value={englishMarks}
                  onChange={(e) => setEnglishMarks(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Maths Marks
                </Typography>
                <TextField
                  fullWidth
                  placeholder="Marks (out of 100)"
                  value={mathsMarks}
                  onChange={(e) => setMathsMarks(e.target.value)}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
            </>
          )}

          {['graduation', 'masters', 'doctorate'].includes(selectedEducation) && (
            <>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    University/Institute<span style={{ color: 'red' }}>*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Enter university/institute"
                    value={institute}
                    onChange={(e) => setInstitute(e.target.value)}
                    variant="outlined"
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Course<span style={{ color: 'red' }}>*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Enter course"
                    value={course}
                    onChange={(e) => setCourse(e.target.value)}
                    variant="outlined"
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Specialization
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Enter specialization"
                    value={specialization}
                    onChange={(e) => setSpecialization(e.target.value)}
                    variant="outlined"
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Course Type
                  </Typography>
                  <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      select
                      value={courseType}
                      onChange={(e) => setCourseType(e.target.value)}
                      displayEmpty
                    >
                      <MenuItem value="Full time">Full time</MenuItem>
                      <MenuItem value="Part time">Part time</MenuItem>
                    </TextField>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Start Year
                  </Typography>
                  <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      select
                      value={startYear}
                      onChange={(e) => setStartYear(e.target.value)}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        Select start year
                      </MenuItem>
                      {years.map((year) => (
                        <MenuItem key={year} value={year}>
                          {year}
                        </MenuItem>
                      ))}
                    </TextField>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    End Year
                  </Typography>
                  <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
                    <TextField
                      fullWidth
                      select
                      value={endYear}
                      onChange={(e) => setEndYear(e.target.value)}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        Select end year
                      </MenuItem>
                      {years.map((year) => (
                        <MenuItem key={year} value={year}>
                          {year}
                        </MenuItem>
                      ))}
                    </TextField>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={12}>
    <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
      Grading System
    </Typography>
    <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
      <TextField
        fullWidth
        select
        value={gradingSystem}
        onChange={(e) => setGradingSystem(e.target.value)}
        displayEmpty
      >
        <MenuItem value="" disabled>Select grading system</MenuItem>
        <MenuItem value="Scale 10 Grading System">Scale 10 Grading System</MenuItem>
        <MenuItem value="Scale 4 Grading System">Scale 4 Grading System</MenuItem>
        <MenuItem value="% Marks of 100 Maximum">% Marks of 100 Maximum</MenuItem>
        <MenuItem value="Course Requires a Pass">Course Requires a Pass</MenuItem>
      </TextField>
    </FormControl>
  </Grid>

  <Grid item xs={12} sm={12}>
    <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
      Marks<span style={{ color: 'red' }}>*</span>
    </Typography>
    <TextField
      fullWidth
      placeholder="Enter your marks"
      value={marks}
      onChange={(e) => setMarks(e.target.value)}
      variant="outlined"
      sx={{ mb: 2 }}
    />
  </Grid>
              </>
            )}
          </Grid>
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
            <Button onClick={() => setSelectedEducation(null)} color="primary">
              Cancel
            </Button>
            <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );

    return (
      <>
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
          <DialogTitle>
            Add Education
            <IconButton
              edge="end"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
              style={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <div>
              <Typography
                variant="body1"
                onClick={() => handleEducationClick('10th')}
                style={{ display: 'block', marginBottom: '20px', fontSize: '18px', cursor: 'pointer', fontWeight: 'bold', color: '#0073e6' }}
              >
                10th
              </Typography>
              {renderEducationDetails('10th')}
              <Divider style={{ marginTop: '10px', marginBottom: '10px' }} />

              <Typography
                variant="body1"
                onClick={() => handleEducationClick('12th')}
                style={{ display: 'block', marginBottom: '20px', fontSize: '18px', cursor: 'pointer', fontWeight: 'bold', color: '#0073e6' }}
              >
                12th
              </Typography>
              {renderEducationDetails('12th')}
            <Divider style={{ marginTop: '10px', marginBottom: '10px' }} />

            <Typography
              variant="body1"
              onClick={() => handleEducationClick('graduation')}
              style={{ display: 'block', marginBottom: '20px', fontSize: '18px', cursor: 'pointer', fontWeight: 'bold', color: '#0073e6' }}
            >
              Graduation/Diploma
            </Typography>
            {renderEducationDetails('graduation')}
            <Divider style={{ marginTop: '10px', marginBottom: '10px' }} />

            <Typography
              variant="body1"
              onClick={() => handleEducationClick('masters')}
              style={{ display: 'block', marginBottom: '20px', fontSize: '18px', cursor: 'pointer', fontWeight: 'bold', color: '#0073e6' }}
            >
              Masters/Post-Graduation
            </Typography>
            {renderEducationDetails('masters')}
            <Divider style={{ marginTop: '10px', marginBottom: '10px' }} />

            <Typography
              variant="body1"
              onClick={() => handleEducationClick('doctorate')}
              style={{ display: 'block', marginBottom: '20px', fontSize: '18px', cursor: 'pointer', fontWeight: 'bold', color: '#0073e6' }}
            >
              Doctorate/PhD
            </Typography>
            {renderEducationDetails('doctorate')}
          </div>
        </DialogContent>
      </Dialog>

      {renderDetailedDialog()}
    </>
  );
};

export default EducationDialog;
