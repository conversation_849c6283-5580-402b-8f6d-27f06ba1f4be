import MainCard from 'components/MainCard';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  Tooltip,
  IconButton,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { FaPhone, FaMapMarkerAlt } from 'react-icons/fa';
import CloseIcon from '@mui/icons-material/Close';
import QuickLinks from './quicklink';

const ContactInfo = () => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({});
  const [uploadedImage, setUploadedImage] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('serviceToken'); // Retrieve JWT token
  
        const response = await fetch('http://localhost:8080/users/get', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });
  
        if (response.status === 401) {
          console.error('Unauthorized access. Could not validate user.');
          return;
        }
  
        const data = await response.json();
  
        // Ensure that the ID is included in formData
        setFormData(data); // Make sure the response includes 'id'
      } catch (error) {
        console.error('Error fetching initial data:', error);
      }
    };
  
    fetchData();
  }, []);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    handleSave();
  };

  const handleSave = async () => {
    const token = localStorage.getItem('serviceToken');

    // Create a copy of the formData to modify before sending
    let updatedData = { ...formData };

    // Reset fields to null if they are not applicable or not filled out
    if (formData.work_status === 'fresher') {
        updatedData.total_experience = null;
        updatedData.month = null;
        updatedData.current_salary = null;
        updatedData.currency = null;
        updatedData.salary_breakdown = null;
        updatedData.fixed_salary = null;
        updatedData.variable_salary = null;
        updatedData.fixed_salary_currency = null;
        updatedData.variable_salary_currency = null;
    }

    if (!formData.current_salary) {
        updatedData.currency = null;
        updatedData.salary_breakdown = null;
    }

    if (!formData.salary_breakdown || formData.salary_breakdown === 'Fixed') {
        updatedData.variable_salary = null;
        updatedData.variable_salary_currency = null;
    }

    try {
        const response = await fetch('http://localhost:8080/users/update/me', {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}` // Ensure token is included
            },
            body: JSON.stringify(updatedData), // Send the updated data
        });

        if (!response.ok) {
            throw new Error('Failed to update user');
        }

        const data = await response.json();
        setFormData(data); // Update the form data with the returned user data
        setOpen(false);
    } catch (error) {
        console.error('Error updating user:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => {
      const updatedData = {
        ...prevData,
        [name]: value,
      };

      // Reset experience-related fields if work status is 'fresher'
      if (name === 'work_status' && value === 'fresher') {
        updatedData.total_experience = null;
        updatedData.month = null;
        updatedData.current_salary = null;
        updatedData.currency = null;
        updatedData.salary_breakdown = null;
        updatedData.fixed_salary = null;
        updatedData.variable_salary = null;
        updatedData.fixed_salary_currency = null;
        updatedData.variable_salary_currency = null;
      }

      // Reset the state field if "Outside India" is selected
      if (name === 'current_location' && value === 'Outside India') {
        updatedData.state = null;
      }

      // Reset the country field if "India" is selected
      if (name === 'current_location' && value === 'India') {
        updatedData.country = null;
      }

      return updatedData;
    });
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    const reader = new FileReader();
    reader.onloadend = () => {
      setFormData((prevData) => ({
        ...prevData,
        avatar: reader.result,
      }));
    };
    if (file) {
      reader.readAsDataURL(file);
    }
  };

  return (
    <MainCard>
      <Card sx={{ minWidth: 400 }}>
        <Box sx={{ padding: 3, maxWidth: 800, }}>
          <div style={styles.pageContainer}>
            <div style={styles.headerContainer}>
              <Typography variant="h2" gutterBottom>
                {formData.first_name || 'Add name'} {formData.last_name}
                <Tooltip title="Edit Profile">
                  <IconButton onClick={handleOpen}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <input
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                id="avatar-upload"
                onChange={handleImageUpload}
              />
              <label htmlFor="avatar-upload" style={{ cursor: 'pointer' }}>
                <img
                  className="avatar"
                  src={formData.avatar || 'https://via.placeholder.com/70'}
                  alt={`${formData.first_name} ${formData.last_name}`}
                  style={styles.avatar}
                />
              </label>
            </div>
            <div style={styles.infoContainer}>
              <div style={styles.infoItem}>
                <FaPhone style={styles.icon} />
                <span>{formData.phone || 'Add mobile number'}</span>
              </div>
              <div style={styles.infoItem}>
                <FaMapMarkerAlt style={styles.icon} />
                <span>{formData.current_location || 'Add location'}</span>
              </div>
            </div>
          </div>
        </Box>
        <EditDetailsDialog
          open={open}
          handleClose={handleClose}
          handleSave={handleSave}
          formData={formData}
          handleChange={handleChange} // Pass the handleChange function
          handleSubmit={handleSubmit} // Pass the handleSubmit function
        />
      </Card>

      <Card sx={{ minWidth: 400, marginTop: 3 }}>
        <QuickLinks />
      </Card>
    </MainCard>
  );
};

const EditDetailsDialog = ({ open, handleClose, handleSave, formData, handleChange, handleSubmit }) => {
  const handleAvailabilityChange = (value) => {
    handleChange({ target: { name: 'availability', value } });
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Grid container alignItems="center" justifyContent="space-between">
          <Typography variant="h5">Contact Information</Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Grid>
      </DialogTitle>
      <DialogContent>
        <form noValidate autoComplete="off" onSubmit={handleSubmit}>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                First name
              </Typography>
              <TextField
                fullWidth
                margin="dense"
                name="first_name"
                value={formData.first_name || ''}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                Last name
              </Typography>
              <TextField
                fullWidth
                margin="dense"
                name="last_name"
                value={formData.last_name || ''}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                Phone number
              </Typography>
              <TextField
                fullWidth
                margin="dense"
                name="phone"
                value={formData.phone || ''}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                Work status
              </Typography>
              <FormControl component="fieldset" fullWidth>
                <RadioGroup row name="work_status" value={formData.work_status || ''} onChange={handleChange}>
                  <FormControlLabel value="fresher" control={<Radio />} label="Fresher" />
                  <FormControlLabel value="experienced" control={<Radio />} label="Experienced" />
                </RadioGroup>
              </FormControl>
            </Grid>
            {formData.work_status === 'experienced' && (
              <>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Total experience
                  </Typography>
                  <FormControl fullWidth margin="dense">
                    <Select
                      name="total_experience"
                      value={formData.total_experience || ''}
                      onChange={handleChange}
                    >
                      {[...Array(25)].map((_, index) => (
                        <MenuItem key={index} value={index + 1}>
                          {index + 1} Year{index + 1 > 1 ? 's' : ''}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Month
                  </Typography>
                  <FormControl fullWidth margin="dense">
                    <Select name="month" value={formData.month || ''} onChange={handleChange}>
                      {[...Array(12)].map((_, index) => (
                        <MenuItem key={index + 1} value={index + 1}>
                          {index + 1}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Current salary
                  </Typography>
                  <Grid container spacing={1} alignItems="center">
                    <Grid item xs={2}>
                      <FormControl fullWidth margin="dense">
                        <Select name="currency" value={formData.currency || '₹'} onChange={handleChange}>
                          <MenuItem value="$">$</MenuItem>
                          <MenuItem value="₹">₹</MenuItem>
                          <MenuItem value="€">€</MenuItem>
                          <MenuItem value="£">£</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={10}>
                      <TextField
                        fullWidth
                        margin="dense"
                        name="current_salary"
                        value={formData.current_salary || ''}
                        onChange={handleChange}
                        placeholder="Eg. 4,50,000"
                        InputLabelProps={{ shrink: true }}
                        sx={{ mb: 2 }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                    Salary breakdown
                  </Typography>
                  <FormControl fullWidth margin="dense">
                    <Select name="salary_breakdown" value={formData.salary_breakdown || ''} onChange={handleChange}>
                      <MenuItem value="Fixed">Fixed</MenuItem>
                      <MenuItem value="Fixed + Variable">Fixed + Variable</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                {formData.salary_breakdown === 'Fixed + Variable' && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                        Fixed salary
                      </Typography>
                      <Grid container spacing={1} alignItems="center">
                        <Grid item xs={2}>
                          <FormControl fullWidth margin="dense">
                            <InputLabel shrink>Currency</InputLabel>
                            <Select name="fixed_salary_currency" value={formData.fixed_salary_currency || '₹'} onChange={handleChange}>
                              <MenuItem value="$">$</MenuItem>
                              <MenuItem value="₹">₹</MenuItem>
                              <MenuItem value="€">€</MenuItem>
                              <MenuItem value="£">£</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={10}>
                          <TextField
                            fullWidth
                            margin="dense"
                            name="fixed_salary"
                            value={formData.fixed_salary || ''}
                            onChange={handleChange}
                            placeholder="Eg. 45,765"
                            InputLabelProps={{ shrink: true }}
                            sx={{ mb: 2 }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                        Variable salary
                      </Typography>
                      <Grid container spacing={1} alignItems="center">
                        <Grid item xs={2}>
                          <FormControl fullWidth margin="dense">
                            <InputLabel shrink>Currency</InputLabel>
                            <Select name="variable_salary_currency" value={formData.variable_salary_currency || '₹'} onChange={handleChange}>
                              <MenuItem value="$">$</MenuItem>
                              <MenuItem value="₹">₹</MenuItem>
                              <MenuItem value="€">€</MenuItem>
                              <MenuItem value="£">£</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={10}>
                          <TextField
                            fullWidth
                            margin="dense"
                            name="variable_salary"
                            value={formData.variable_salary || ''}
                            onChange={handleChange}
                            placeholder="Eg. 45,765"
                            InputLabelProps={{ shrink: true }}
                            sx={{ mb: 2 }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </>
                )}
              </>
            )}
            <Grid item xs={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                Current location
              </Typography>
              <FormControl component="fieldset" fullWidth>
                <RadioGroup row name="current_location" value={formData.current_location || ''} onChange={handleChange}>
                  <FormControlLabel value="India" control={<Radio />} label="India" />
                  <FormControlLabel value="Outside India" control={<Radio />} label="Outside India" />
                </RadioGroup>
              </FormControl>
            </Grid>
            {formData.current_location === 'India' && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  State
                </Typography>
                <FormControl fullWidth margin="dense">
                  <InputLabel shrink>State</InputLabel>
                  <Select name="state" value={formData.state || ''} onChange={handleChange}>
                    <MenuItem value="Andhra Pradesh">Andhra Pradesh</MenuItem>
                    <MenuItem value="Arunachal Pradesh">Arunachal Pradesh</MenuItem>
                    <MenuItem value="Assam">Assam</MenuItem>
                    <MenuItem value="Bihar">Bihar</MenuItem>
                    <MenuItem value="Chhattisgarh">Chhattisgarh</MenuItem>
                    <MenuItem value="Goa">Goa</MenuItem>
                    <MenuItem value="Gujarat">Gujarat</MenuItem>
                    <MenuItem value="Haryana">Haryana</MenuItem>
                    <MenuItem value="Himachal Pradesh">Himachal Pradesh</MenuItem>
                    <MenuItem value="Jharkhand">Jharkhand</MenuItem>
                    <MenuItem value="Karnataka">Karnataka</MenuItem>
                    <MenuItem value="Kerala">Kerala</MenuItem>
                    <MenuItem value="Madhya Pradesh">Madhya Pradesh</MenuItem>
                    <MenuItem value="Maharashtra">Maharashtra</MenuItem>
                    <MenuItem value="Manipur">Manipur</MenuItem>
                    <MenuItem value="Meghalaya">Meghalaya</MenuItem>
                    <MenuItem value="Mizoram">Mizoram</MenuItem>
                    <MenuItem value="Nagaland">Nagaland</MenuItem>
                    <MenuItem value="Odisha">Odisha</MenuItem>
                    <MenuItem value="Punjab">Punjab</MenuItem>
                    <MenuItem value="Rajasthan">Rajasthan</MenuItem>
                    <MenuItem value="Sikkim">Sikkim</MenuItem>
                    <MenuItem value="Tamil Nadu">Tamil Nadu</MenuItem>
                    <MenuItem value="Telangana">Telangana</MenuItem>
                    <MenuItem value="Tripura">Tripura</MenuItem>
                    <MenuItem value="Uttar Pradesh">Uttar Pradesh</MenuItem>
                    <MenuItem value="Uttarakhand">Uttarakhand</MenuItem>
                    <MenuItem value="West Bengal">West Bengal</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            {formData.current_location === 'Outside India' && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                  Country
                </Typography>
                <FormControl fullWidth margin="dense">
                  <InputLabel shrink>Country</InputLabel>
                  <Select name="country" value={formData.country || ''} onChange={handleChange}>
                    <MenuItem value="United States">United States</MenuItem>
                    <MenuItem value="Canada">Canada</MenuItem>
                    <MenuItem value="United Kingdom">United Kingdom</MenuItem>
                    <MenuItem value="Australia">Australia</MenuItem>
                    <MenuItem value="Germany">Germany</MenuItem>
                    <MenuItem value="France">France</MenuItem>
                    <MenuItem value="China">China</MenuItem>
                    <MenuItem value="Japan">Japan</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12}>
              <Typography variant="subtitle1" component="label" sx={{ mb: 1 }} gutterBottom>
                Availability to join
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }} color="textSecondary">
                Lets recruiters know your availability to join
              </Typography>
              <Grid container spacing={1}>
                {['15 Days or less', '1 Month', '2 Months', '3 Months', 'More than 3 Months'].map((option) => (
                  <Grid item key={option}>
                    <Button
                      variant={formData.availability === option ? 'contained' : 'outlined'}
                      color="primary"
                      onClick={() => handleAvailabilityChange(option)}
                    >
                      {option}
                    </Button> 
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </form>
      </DialogContent>
      <DialogActions>
        <Button variant="contained" color="primary" onClick={handleSubmit}>
          Save
        </Button>
        <Button variant="outlined" color="secondary" onClick={handleClose}>
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const styles = {
  pageContainer: {
    fontFamily: 'Arial, sans-serif',
    padding: '20px',
  },
  headerContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  avatar: {
    borderRadius: '100%',
    width: '70px',
    height: '80px',
  },
  infoContainer: {
    display: 'flex',
    alignItems: 'center',
    padding: '10px 0',
  },
  infoItem: {
    display: 'flex',
    alignItems: 'center',
    marginRight: '15px',
  },
  icon: {
    marginRight: '8px',
  },
  uploadedImage: {
    maxWidth: '100%',
    height: 'auto',
    borderRadius: '8px',
  },
};

export default ContactInfo;
