import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogTitle, DialogContent, TextField, Button, Chip, Typography } from '@mui/material';
import axios from 'axios';


const KeySkillsDialog = ({ open, onClose, onSave }) => {
  const [skills, setSkills] = useState([]);
  const [newSkill, setNewSkill] = useState('');
  const [highlightedSkill, setHighlightedSkill] = useState(null);

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const token = localStorage.getItem('serviceToken');
        const response = await axios.get(getSkillsEndpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.status === 200) {
          setSkills(response.data.map(skill => skill.name));
        } else {
          console.error('Failed to fetch skills:', response);
        }
      } catch (error) {
        console.error('Error fetching skills:', error);
      }
    };

    fetchSkills();
  }, []);

  const handleAddSkill = () => {
    if (newSkill.trim() !== '' && !skills.includes(newSkill)) {
      setSkills([...skills, newSkill]);
      setHighlightedSkill(newSkill);
      setNewSkill('');
    }
  };

  const handleSkillsSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      const response = await axios.patch(
        updateSkillsEndpoint,
        skills.map(skill => ({ name: skill })),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200) {
        setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
        onSave('Skills updated successfully!', 'success');
      } else {
        console.error('Failed to save skills:', response);
        onSave('Failed to update skills!', 'error');
      }
    } catch (error) {
      console.error('Error saving skills:', error);
      onSave('Error saving skills!', 'error');
    }

    onClose();
  };
  const getSkillsEndpoint = 'http://localhost:8080/users/get/skills';
  const updateSkillsEndpoint = 'http://localhost:8080/users/update/me/skills';
  

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6">Key skills</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px' }}>
          Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
        </Typography>
        <div style={{ marginTop: '20px' }}>
          <Typography variant="h6">Your Skills:</Typography>
          <div>
            {skills.map((skill, index) => (
              <Chip
                key={index}
                label={skill}
                style={{
                  margin: '5px',
                  backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
                  color: highlightedSkill === skill ? '#fff' : undefined,
                  fontWeight: highlightedSkill === skill ? 'bold' : undefined,
                }}
              />
            ))}
          </div>
        </div>
        <TextField
          fullWidth
          label="Add skills"
          variant="outlined"
          margin="normal"
          value={newSkill}
          onChange={(e) => setNewSkill(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleAddSkill();
              e.preventDefault(); // Prevent form submission on Enter key press
            }
          }}
        />
        <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
          Add Skill
        </Button>
        <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
          <Button onClick={onClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KeySkillsDialog;
