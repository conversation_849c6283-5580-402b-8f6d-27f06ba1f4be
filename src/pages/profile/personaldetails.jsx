// import React from 'react';

// import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem, FormControlLabel, Checkbox, RadioGroup, Radio } from '@mui/material';

// const PersonalDetailsDialog = ({ open, onClose, personalDetails, onPersonalDetailsChange, onSave, onAddLanguage, onDeleteLanguage }) => {
//   const workPermitOptions = ["H1B", "L1", "Others"];
//   const countries = ["USA", "Canada", "UK", "Australia", "Germany", "France", "Others"];
//   const proficiencyLevels = ["Basic", "Intermediate", "Fluent", "Native"];
//   const disabilityTypes = ["Blindness", "Low Vision", "Hearing Impairment", "Speech and Language Disability", "Locomotor Disability", "Leprosy Cured Person", "Cerebral Palsy", "Dwarfism", "Muscular Dystrophy", "Acid Attack Victims", "Specific Learning Disabilities", "Autism Spectrum Disorder", "Mental Illness", "Haemophilia", "Sickle Cell Disease", "Thalassemia", "Parkinson's Disease", "Intellectual Disability", "Chronic Neurological Conditions", "Multiple Sclerosis", "Multiple Disabilities including Deaf Blindness", "Others"];
//   const breakReasons = ["Child care", "Education", "Medical", "Layoff", "Personal"];

//   return (
//     <Dialog open={open} onClose={onClose}>
//       <DialogTitle>
//         <Typography variant="h6" style={{ color: '#000' }}>Personal details</Typography>
//       </DialogTitle>
//       <DialogContent>
//         <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
//           This information is important for employers to know you better
//         </Typography>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Gender</Typography>
//         <RadioGroup
//           name="gender"
//           value={personalDetails.gender}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Male" control={<Radio />} label="Male" />
//           <FormControlLabel value="Female" control={<Radio />} label="Female" />
//           <FormControlLabel value="Transgender" control={<Radio />} label="Transgender" />
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>More information</Typography>
//         <FormControlLabel
//           control={<Checkbox checked={personalDetails.moreInfo.includes('Single parent')} onChange={onPersonalDetailsChange} name="moreInfo" value="Single parent" />}
//           label="Single parent"
//         />
//         <FormControlLabel
//           control={<Checkbox checked={personalDetails.moreInfo.includes('Working mother')} onChange={onPersonalDetailsChange} name="moreInfo" value="Working mother" />}
//           label="Working mother"
//         />
//         <FormControlLabel
//           control={<Checkbox checked={personalDetails.moreInfo.includes('Served in military')} onChange={onPersonalDetailsChange} name="moreInfo" value="Served in military" />}
//           label="Served in military"
//         />
//         <FormControlLabel
//           control={<Checkbox checked={personalDetails.moreInfo.includes('Retired (60+)')} onChange={onPersonalDetailsChange} name="moreInfo" value="Retired (60+)" />}
//           label="Retired (60+)"
//         />
//         <FormControlLabel
//           control={<Checkbox checked={personalDetails.moreInfo.includes('LGBTQ+')} onChange={onPersonalDetailsChange} name="moreInfo" value="LGBTQ+" />}
//           label="LGBTQ+"
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Marital status</Typography>
//         <RadioGroup
//           name="maritalStatus"
//           value={personalDetails.maritalStatus}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Single/unmarried" control={<Radio />} label="Single/unmarried" />
//           <FormControlLabel value="Married" control={<Radio />} label="Married" />
//           <FormControlLabel value="Widowed" control={<Radio />} label="Widowed" />
//           <FormControlLabel value="Divorced" control={<Radio />} label="Divorced" />
//           <FormControlLabel value="Separated" control={<Radio />} label="Separated" />
//           <FormControlLabel value="Other" control={<Radio />} label="Other" />
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Date of birth</Typography>
//         <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobDay"
//             value={personalDetails.dobDay}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 1, marginRight: '10px' }}
//           >
//             {Array.from({ length: 31 }, (_, i) => (
//               <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//             ))}
//           </TextField>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobMonth"
//             value={personalDetails.dobMonth}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 1, marginRight: '10px' }}
//           >
//             {Array.from({ length: 12 }, (_, i) => (
//               <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//             ))}
//           </TextField>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobYear"
//             value={personalDetails.dobYear}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 2 }}
//           >
//             {Array.from({ length: 100 }, (_, i) => (
//               <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//             ))}
//           </TextField>
//         </div>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Category</Typography>
//         <RadioGroup
//           name="category"
//           value={personalDetails.category}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="General" control={<Radio />} label="General" />
//           <FormControlLabel value="Scheduled Caste (SC)" control={<Radio />} label="Scheduled Caste (SC)" />
//           <FormControlLabel value="Scheduled Tribe (ST)" control={<Radio />} label="Scheduled Tribe (ST)" />
//           <FormControlLabel value="OBC - Creamy" control={<Radio />} label="OBC - Creamy" />
//           <FormControlLabel value="OBC - Non creamy" control={<Radio />} label="OBC - Non creamy" />
//           <FormControlLabel value="Other" control={<Radio />} label="Other" />
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Are you differently abled?</Typography>
//         <RadioGroup
//           name="differentlyAbled"
//           value={personalDetails.differentlyAbled}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
//           <FormControlLabel value="No" control={<Radio />} label="No" />
//         </RadioGroup>

//         {personalDetails.differentlyAbled === 'Yes' && (
//           <>
//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Type</Typography>
//             <TextField
//               select
//               fullWidth
//               variant="outlined"
//               margin="dense"
//               name="disabilityType"
//               value={personalDetails.disabilityType}
//               onChange={onPersonalDetailsChange}
//             >
//               {disabilityTypes.map((type) => (
//                 <MenuItem key={type} value={type}>{type}</MenuItem>
//               ))}
//             </TextField>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Do you need assistance at your workplace?</Typography>
//             <TextField
//               fullWidth
//               variant="outlined"
//               margin="dense"
//               name="workplaceAssistance"
//               value={personalDetails.workplaceAssistance}
//               onChange={onPersonalDetailsChange}
//               multiline
//               rows={2}
//               placeholder="Type here (example — Need wheelchair)"
//             />
//           </>
//         )}

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Have you taken a career break?</Typography>
//         <RadioGroup
//           name="careerBreak"
//           value={personalDetails.careerBreak}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
//           <FormControlLabel value="No" control={<Radio />} label="No" />
//         </RadioGroup>

//         {personalDetails.careerBreak === 'Yes' && (
//           <>
//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Reason for break</Typography>
//             {breakReasons.map((reason) => (
//               <FormControlLabel
//                 key={reason}
//                 control={<Checkbox checked={personalDetails.breakReasons.includes(reason)} onChange={onPersonalDetailsChange} name="breakReasons" value={reason} />}
//                 label={reason}
//               />
//             ))}

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Break started from</Typography>
//             <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakStartYear"
//                 value={personalDetails.breakStartYear}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1, marginRight: '10px' }}
//               >
//                 {Array.from({ length: 50 }, (_, i) => (
//                   <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//                 ))}
//               </TextField>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakStartMonth"
//                 value={personalDetails.breakStartMonth}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1 }}
//               >
//                 {Array.from({ length: 12 }, (_, i) => (
//                   <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//                 ))}
//               </TextField>
//             </div>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Break ended in</Typography>
//             <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakEndYear"
//                 value={personalDetails.breakEndYear}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1, marginRight: '10px' }}
//                 disabled={personalDetails.currentlyOnBreak}
//               >
//                 {Array.from({ length: 50 }, (_, i) => (
//                   <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//                 ))}
//               </TextField>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakEndMonth"
//                 value={personalDetails.breakEndMonth}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1 }}
//                 disabled={personalDetails.currentlyOnBreak}
//               >
//                 {Array.from({ length: 12 }, (_, i) => (
//                   <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//                 ))}
//               </TextField>
//             </div>

//             <FormControlLabel
//               control={<Checkbox checked={personalDetails.currentlyOnBreak} onChange={onPersonalDetailsChange} name="currentlyOnBreak" />}
//               label="Currently on a break"
//             />
//           </>
//         )}

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Work permit for USA</Typography>
//         <TextField
//           select
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="workPermitUSA"
//           value={personalDetails.workPermitUSA}
//           onChange={onPersonalDetailsChange}
//         >
//           {workPermitOptions.map((option, index) => (
//             <MenuItem key={index} value={option}>{option}</MenuItem>
//           ))}
//         </TextField>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Work permit for other countries</Typography>
//         <TextField
//           select
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="workPermitOthers"
//           value={personalDetails.workPermitOthers}
//           onChange={onPersonalDetailsChange}
//           SelectProps={{ multiple: true }}
//         >
//           {countries.map((country, index) => (
//             <MenuItem key={index} value={country}>{country}</MenuItem>
//           ))}
//         </TextField>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Permanent address</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="permanentAddress"
//           value={personalDetails.permanentAddress}
//           onChange={onPersonalDetailsChange}
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Hometown</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="hometown"
//           value={personalDetails.hometown}
//           onChange={onPersonalDetailsChange}
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Pincode</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="pincode"
//           value={personalDetails.pincode}
//           onChange={onPersonalDetailsChange}
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Language Proficiency</Typography>
//         {personalDetails.languages.map((language, index) => (
//           <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
//             <TextField
//               label="Language"
//               variant="outlined"
//               margin="dense"
//               name="language"
//               value={language.name}
//               onChange={(event) => onPersonalDetailsChange(event, index, 'name')}
//               style={{ marginRight: '10px' }}
//             />
//             <TextField
//               select
//               label="Proficiency"
//               variant="outlined"
//               margin="dense"
//               name="proficiency"
//               value={language.proficiency}
//               onChange={(event) => onPersonalDetailsChange(event, index, 'proficiency')}
//               style={{ marginRight: '10px' }}
//             >
//               {proficiencyLevels.map((level) => (
//                 <MenuItem key={level} value={level}>{level}</MenuItem>
//               ))}
//             </TextField>
//             <FormControlLabel
//               control={<Checkbox checked={language.read} onChange={(event) => onPersonalDetailsChange(event, index, 'read')} name="read" />}
//               label="Read"
//             />
//             <FormControlLabel
//               control={<Checkbox checked={language.write} onChange={(event) => onPersonalDetailsChange(event, index, 'write')} name="write" />}
//               label="Write"
//             />
//             <FormControlLabel
//               control={<Checkbox checked={language.speak} onChange={(event) => onPersonalDetailsChange(event, index, 'speak')} name="speak" />}
//               label="Speak"
//             />
//             <Button onClick={() => onDeleteLanguage(index)} color="secondary" style={{ marginLeft: '10px' }}>
//               Delete
//             </Button>
//           </div>
//         ))}
//         <Button onClick={onAddLanguage} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//           Add Language
//         </Button>

//         <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
//           <Button onClick={onClose} color="primary">
//             Cancel
//           </Button>
//           <Button onClick={onSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//             Save
//           </Button>
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default PersonalDetailsDialog;


// import React, { useState } from 'react';
// import {
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   Typography,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   FormControl,
//   InputLabel,
//   Select,
//   MenuItem,
//   TextField,
//   Button
// } from '@material-ui/core';

// const disabilityTypes = [
//   'Physical disability',
//   'Sensory disability',
//   'Intellectual disability',
//   'Mental health condition',
//   'Other'
// ];

// const breakReasons = [
//   'Personal reasons',
//   'Family reasons',
//   'Health reasons',
//   'Educational reasons',
//   'Other'
// ];

// const workPermitOptions = [
//   'Yes',
//   'No'
// ];

// const PersonalDetailsDialog = ({ open, onClose }) => {
//   const [personalDetails, setPersonalDetails] = useState({
//     gender: '',
//     moreInfo: '',
//     maritalStatus: '',
//     dobDay: '',
//     dobMonth: '',
//     dobYear: '',
//     category: '',
//     differentlyAbled: '',
//     disabilityType: '',
//     workplaceAssistance: '',
//     careerBreak: '',
//     breakReasons: '',
//     breakStartMonth: '',
//     breakStartYear: '',
//     breakEndMonth: '',
//     breakEndYear: '',
//     currentlyOnBreak: '',
//     workPermitUSA: '',
//     permanentAddress: '',
//     hometown: '',
//     pincode: ''
//   });

//   const onPersonalDetailsChange = (event) => {
//     setPersonalDetails({
//       ...personalDetails,
//       [event.target.name]: event.target.value
//     });
//   };

//   const handleSelectChange = (event) => {
//     setPersonalDetails({
//       ...personalDetails,
//       [event.target.name]: event.target.value
//     });
//   };

//   const handleSave = () => {
//     // Add your save logic here
//     console.log(personalDetails);
//     onClose();
//   };

//   return (
//     <Dialog open={open} onClose={onClose}>
//       <DialogTitle>
//         <Typography variant="h6" style={{ color: '#000' }}>Personal details</Typography>
//       </DialogTitle>
//       <DialogContent>
//         <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
//           This information is important for employers to know you better
//         </Typography>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Gender</Typography>
//         <RadioGroup
//           name="gender"
//           value={personalDetails.gender}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Male" control={<Radio />} label="Male" />
//           <FormControlLabel value="Female" control={<Radio />} label="Female" />
//           <FormControlLabel value="Transgender" control={<Radio />} label="Transgender" />
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>
//           More information
//         </Typography>
//         <FormControl component="fieldset">
//           <RadioGroup
//             name="moreInfo"
//             value={personalDetails.moreInfo}
//             onChange={onPersonalDetailsChange}
//           >
//             <FormControlLabel control={<Radio />} label="Single parent" value="Single parent" />
//             <FormControlLabel control={<Radio />} label="Working mother" value="Working mother" />
//             <FormControlLabel control={<Radio />} label="Served in military" value="Served in military" />
//             <FormControlLabel control={<Radio />} label="Retired (60+)" value="Retired (60+)" />
//             <FormControlLabel control={<Radio />} label="LGBTQ+" value="LGBTQ+" />
//           </RadioGroup>
//         </FormControl>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>
//           Marital Status
//         </Typography>
//         <FormControl fullWidth>
//           <InputLabel id="marital-status-label">Marital Status</InputLabel>
//           <Select
//             labelId="marital-status-label"
//             name="maritalStatus"
//             value={personalDetails.maritalStatus || ''}
//             onChange={handleSelectChange}
//             label="Marital Status"
//           >
//             <MenuItem value="Single/unmarried">Single/unmarried</MenuItem>
//             <MenuItem value="Married">Married</MenuItem>
//             <MenuItem value="Widowed">Widowed</MenuItem>
//             <MenuItem value="Divorced">Divorced</MenuItem>
//             <MenuItem value="Separated">Separated</MenuItem>
//             <MenuItem value="Other">Other</MenuItem>
//           </Select>
//         </FormControl>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Date of birth</Typography>
//         <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobDay"
//             value={personalDetails.dobDay}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 1, marginRight: '10px' }}
//           >
//             {Array.from({ length: 31 }, (_, i) => (
//               <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//             ))}
//           </TextField>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobMonth"
//             value={personalDetails.dobMonth}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 1, marginRight: '10px' }}
//           >
//             {Array.from({ length: 12 }, (_, i) => (
//               <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//             ))}
//           </TextField>
//           <TextField
//             select
//             variant="outlined"
//             margin="dense"
//             name="dobYear"
//             value={personalDetails.dobYear}
//             onChange={onPersonalDetailsChange}
//             style={{ flex: 2 }}
//           >
//             {Array.from({ length: 100 }, (_, i) => (
//               <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//             ))}
//           </TextField>
//         </div>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Category</Typography>
//         <RadioGroup
//           name="category"
//           value={personalDetails.category}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="General" control={<Radio />} label="General" />
//           <FormControlLabel value="Scheduled Caste (SC)" control={<Radio />} label="Scheduled Caste (SC)" />
//           <FormControlLabel value="Scheduled Tribe (ST)" control={<Radio />} label="Scheduled Tribe (ST)" />
//           <FormControlLabel value="OBC - Creamy" control={<Radio />} label="OBC - Creamy" />
//           <FormControlLabel value="OBC - Non creamy" control={<Radio />} label="OBC - Non creamy" />
//           <FormControlLabel value="Other" control={<Radio />} label="Other" />
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Are you differently abled?</Typography>
//         <RadioGroup
//           name="differentlyAbled"
//           value={personalDetails.differentlyAbled}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
//           <FormControlLabel value="No" control={<Radio />} label="No" />
//         </RadioGroup>

//         {personalDetails.differentlyAbled === 'Yes' && (
//           <>
//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Type</Typography>
//             <TextField
//               select
//               fullWidth
//               variant="outlined"
//               margin="dense"
//               name="disabilityType"
//               value={personalDetails.disabilityType}
//               onChange={onPersonalDetailsChange}
//             >
//               {disabilityTypes.map((type) => (
//                 <MenuItem key={type} value={type}>{type}</MenuItem>
//               ))}
//             </TextField>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Do you need assistance at your workplace?</Typography>
//             <TextField
//               fullWidth
//               variant="outlined"
//               margin="dense"
//               name="workplaceAssistance"
//               value={personalDetails.workplaceAssistance}
//               onChange={onPersonalDetailsChange}
//               multiline
//               rows={2}
//               placeholder="Type here (example — Need wheelchair)"
//             />
//           </>
//         )}

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Have you taken a career break?</Typography>
//         <RadioGroup
//           name="careerBreak"
//           value={personalDetails.careerBreak}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
//           <FormControlLabel value="No" control={<Radio />} label="No" />
//         </RadioGroup>

//         {personalDetails.careerBreak === 'Yes' && (
//           <>
//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Reason</Typography>
//             <TextField
//               select
//               fullWidth
//               variant="outlined"
//               margin="dense"
//               name="breakReasons"
//               value={personalDetails.breakReasons}
//               onChange={handleSelectChange}
//             >
//               {breakReasons.map((reason) => (
//                 <MenuItem key={reason} value={reason}>{reason}</MenuItem>
//               ))}
//             </TextField>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Start Month & Year</Typography>
//             <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakStartMonth"
//                 value={personalDetails.breakStartMonth}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1, marginRight: '10px' }}
//               >
//                 {Array.from({ length: 12 }, (_, i) => (
//                   <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//                 ))}
//               </TextField>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakStartYear"
//                 value={personalDetails.breakStartYear}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 2 }}
//               >
//                 {Array.from({ length: 100 }, (_, i) => (
//                   <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//                 ))}
//               </TextField>
//             </div>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>End Month & Year</Typography>
//             <div style={{ display: 'flex', justifyContent: 'space-between' }}>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakEndMonth"
//                 value={personalDetails.breakEndMonth}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 1, marginRight: '10px' }}
//               >
//                 {Array.from({ length: 12 }, (_, i) => (
//                   <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
//                 ))}
//               </TextField>
//               <TextField
//                 select
//                 variant="outlined"
//                 margin="dense"
//                 name="breakEndYear"
//                 value={personalDetails.breakEndYear}
//                 onChange={onPersonalDetailsChange}
//                 style={{ flex: 2 }}
//               >
//                 {Array.from({ length: 100 }, (_, i) => (
//                   <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
//                 ))}
//               </TextField>
//             </div>

//             <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Currently on break?</Typography>
//             <RadioGroup
//               name="currentlyOnBreak"
//               value={personalDetails.currentlyOnBreak}
//               onChange={onPersonalDetailsChange}
//               row
//             >
//               <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
//               <FormControlLabel value="No" control={<Radio />} label="No" />
//             </RadioGroup>
//           </>
//         )}

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Do you have a work permit for the USA?</Typography>
//         <RadioGroup
//           name="workPermitUSA"
//           value={personalDetails.workPermitUSA}
//           onChange={onPersonalDetailsChange}
//           row
//         >
//           {workPermitOptions.map((option) => (
//             <FormControlLabel key={option} value={option} control={<Radio />} label={option} />
//           ))}
//         </RadioGroup>

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Permanent Address</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="permanentAddress"
//           value={personalDetails.permanentAddress}
//           onChange={onPersonalDetailsChange}
//           multiline
//           rows={3}
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Hometown</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="hometown"
//           value={personalDetails.hometown}
//           onChange={onPersonalDetailsChange}
//         />

//         <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Pincode</Typography>
//         <TextField
//           fullWidth
//           variant="outlined"
//           margin="dense"
//           name="pincode"
//           value={personalDetails.pincode}
//           onChange={onPersonalDetailsChange}
//         />

//         <Button
//           variant="contained"
//           color="primary"
//           onClick={handleSave}
//           style={{ marginTop: '20px' }}
//         >
//           Save
//         </Button>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default PersonalDetailsDialog;
import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, TextField, Button, Typography, MenuItem, FormControlLabel, Checkbox, RadioGroup, Radio } from '@mui/material';
import axios from 'axios';

const PersonalDetailsDialog = ({ open, onClose, onSave }) => {
  const workPermitOptions = ["H1B", "L1", "Others"];
  const countries = ["USA", "Canada", "UK", "Australia", "Germany", "France", "Others"];
  const proficiencyLevels = ["Basic", "Intermediate", "Fluent", "Native"];
  const disabilityTypes = ["Blindness", "Low Vision", "Hearing Impairment", "Speech and Language Disability", "Locomotor Disability", "Leprosy Cured Person", "Cerebral Palsy", "Dwarfism", "Muscular Dystrophy", "Acid Attack Victims", "Specific Learning Disabilities", "Autism Spectrum Disorder", "Mental Illness", "Haemophilia", "Sickle Cell Disease", "Thalassemia", "Parkinson's Disease", "Intellectual Disability", "Chronic Neurological Conditions", "Multiple Sclerosis", "Multiple Disabilities including Deaf Blindness", "Others"];
  const breakReasons = ["Child care", "Education", "Medical", "Layoff", "Personal"];

  const [personalDetails, setPersonalDetails] = useState({
    gender: '',
    more_info: [], // Updated to match backend field name
    marital_status: '',
    dob_day: '',
    dob_month: '',
    dob_year: '',
    category: '',
    differently_abled: '',
    disability_type: '',
    workplace_assistance: '',
    career_break: '',
    break_reasons: '', // Updated to match backend field name
    break_start_year: '',
    break_start_month: '',
    break_end_year: '',
    break_end_month: '',
    currently_on_break: false,
    work_permit_usa: '',
    work_permit_others: '', // Updated to match backend field name
    permanent_address: '',
    hometown: '',
    pincode: '',
  });

  useEffect(() => {
    const fetchPersonalDetails = async () => {
      try {
        const token = localStorage.getItem('serviceToken');
        const response = await axios.get('http://localhost:8080/users/get/personal-details', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
  
        if (response.data) {
          const transformedData = {
            gender: response.data.gender,
            marital_status: response.data.marital_status,
            dob_day: response.data.dob_day,
            dob_month: response.data.dob_month,
            dob_year: response.data.dob_year,
            category: response.data.category,
            differently_abled: response.data.differently_abled,
            disability_type: response.data.disability_type,
            workplace_assistance: response.data.workplace_assistance,
            career_break: response.data.career_break,
            break_reasons: response.data.break_reasons,
            break_start_year: response.data.break_start_year,
            break_start_month: response.data.break_start_month,
            break_end_year: response.data.break_end_year,
            break_end_month: response.data.break_end_month,
            currently_on_break: response.data.currently_on_break,
            work_permit_usa: response.data.work_permit_usa,
            work_permit_others: response.data.work_permit_others,
            permanent_address: response.data.permanent_address,
            hometown: response.data.hometown,
            pincode: response.data.pincode,
            more_info: response.data.more_info,
          };
          setPersonalDetails(transformedData);
        }
      } catch (error) {
        console.error('Failed to fetch personal details:', error);
      }
    };
  
    if (open) {
      fetchPersonalDetails();
    }
  }, [open]);
  
  const handlePersonalDetailsChange = (event, index = null, key = null) => {
    const { name, value, checked } = event.target;
    setPersonalDetails((prevDetails) => {
        if (name === 'more_info') {
            let updatedMoreInfo = [...(prevDetails.more_info || [])];
            if (checked) {
                updatedMoreInfo.push(value); // Add the selected value to the list
            } else {
                updatedMoreInfo = updatedMoreInfo.filter((item) => item !== value); // Remove the unselected value from the list
            }
            return { ...prevDetails, [name]: updatedMoreInfo };
        } else if (name === 'currently_on_break') {
            return {
                ...prevDetails,
                [name]: checked,
                break_end_year: checked ? '' : prevDetails.break_end_year,
                break_end_month: checked ? '' : prevDetails.break_end_month
            };
        } else {
            return { ...prevDetails, [name]: value };
        }
    });
};

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      await axios.patch('http://localhost:8080/users/update/me/personal-details', personalDetails, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      onSave(personalDetails);
      onClose();
    } catch (error) {
      console.error('Failed to save personal details:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6" style={{ color: '#000' }}>Personal details</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
          This information is important for employers to know you better
        </Typography>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Gender</Typography>
        <RadioGroup
          name="gender"
          value={personalDetails.gender}
          onChange={handlePersonalDetailsChange}
          row
        >
          <FormControlLabel value="Male" control={<Radio />} label="Male" />
          <FormControlLabel value="Female" control={<Radio />} label="Female" />
          <FormControlLabel value="Transgender" control={<Radio />} label="Transgender" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>More information</Typography>
        <FormControlLabel
          control={<Checkbox checked={(personalDetails.more_info || []).includes('Single parent')} onChange={handlePersonalDetailsChange} name="more_info" value="Single parent" />}
          label="Single parent"
        />
        <FormControlLabel
          control={<Checkbox checked={(personalDetails.more_info || []).includes('Working mother')} onChange={handlePersonalDetailsChange} name="more_info" value="Working mother" />}
          label="Working mother"
        />
        <FormControlLabel
          control={<Checkbox checked={(personalDetails.more_info || []).includes('Served in military')} onChange={handlePersonalDetailsChange} name="more_info" value="Served in military" />}
          label="Served in military"
        />
        <FormControlLabel
          control={<Checkbox checked={(personalDetails.more_info || []).includes('Retired (60+)')} onChange={handlePersonalDetailsChange} name="more_info" value="Retired (60+)" />}
          label="Retired (60+)"
        />
        <FormControlLabel
          control={<Checkbox checked={(personalDetails.more_info || []).includes('LGBTQ+')} onChange={handlePersonalDetailsChange} name="more_info" value="LGBTQ+" />}
          label="LGBTQ+"
        />

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Marital Status</Typography>
        <RadioGroup
          name="marital_status"
          value={personalDetails.marital_status}
          onChange={handlePersonalDetailsChange}
          row
        >
          <FormControlLabel value="Single/unmarried" control={<Radio />} label="Single/unmarried" />
          <FormControlLabel value="Married" control={<Radio />} label="Married" />
          <FormControlLabel value="Widowed" control={<Radio />} label="Widowed" />
          <FormControlLabel value="Divorced" control={<Radio />} label="Divorced" />
          <FormControlLabel value="Separated" control={<Radio />} label="Separated" />
          <FormControlLabel value="Other" control={<Radio />} label="Other" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Date of birth</Typography>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <TextField
            select
            variant="outlined"
            margin="dense"
            name="dob_day"
            value={personalDetails.dob_day}
            onChange={handlePersonalDetailsChange}
            style={{ flex: 1, marginRight: '10px' }}
          >
            {Array.from({ length: 31 }, (_, i) => (
              <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
            ))}
          </TextField>
          <TextField
            select
            variant="outlined"
            margin="dense"
            name="dob_month"
            value={personalDetails.dob_month}
            onChange={handlePersonalDetailsChange}
            style={{ flex: 1, marginRight: '10px' }}
          >
            {Array.from({ length: 12 }, (_, i) => (
              <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
            ))}
          </TextField>
          <TextField
            select
            variant="outlined"
            margin="dense"
            name="dob_year"
            value={personalDetails.dob_year}
            onChange={handlePersonalDetailsChange}
            style={{ flex: 2 }}
          >
            {Array.from({ length: 100 }, (_, i) => (
              <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
            ))}
          </TextField>
        </div>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Category</Typography>
        <RadioGroup
          name="category"
          value={personalDetails.category}
          onChange={handlePersonalDetailsChange}
          row
        >
          <FormControlLabel value="General" control={<Radio />} label="General" />
          <FormControlLabel value="Scheduled Caste (SC)" control={<Radio />} label="Scheduled Caste (SC)" />
          <FormControlLabel value="Scheduled Tribe (ST)" control={<Radio />} label="Scheduled Tribe (ST)" />
          <FormControlLabel value="OBC - Creamy" control={<Radio />} label="OBC - Creamy" />
          <FormControlLabel value="OBC - Non creamy" control={<Radio />} label="OBC - Non creamy" />
          <FormControlLabel value="Other" control={<Radio />} label="Other" />
        </RadioGroup>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Are you differently abled?</Typography>
        <RadioGroup
          name="differently_abled"
          value={personalDetails.differently_abled}
          onChange={handlePersonalDetailsChange}
          row
        >
          <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="No" control={<Radio />} label="No" />
        </RadioGroup>

        {personalDetails.differently_abled === 'Yes' && (
          <>
            <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Type</Typography>
            <TextField
              select
              fullWidth
              variant="outlined"
              margin="dense"
              name="disability_type"
              value={personalDetails.disability_type}
              onChange={handlePersonalDetailsChange}
            >
              {disabilityTypes.map((type) => (
                <MenuItem key={type} value={type}>{type}</MenuItem>
              ))}
            </TextField>

            <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Do you need assistance at your workplace?</Typography>
            <TextField
              fullWidth
              variant="outlined"
              margin="dense"
              name="workplace_assistance"
              value={personalDetails.workplace_assistance}
              onChange={handlePersonalDetailsChange}
              multiline
              rows={2}
              placeholder="Type here (example — Need wheelchair)"
            />
          </>
        )}

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Have you taken a career break?</Typography>
        <RadioGroup
          name="career_break"
          value={personalDetails.career_break}
          onChange={handlePersonalDetailsChange}
          row
        >
          <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
          <FormControlLabel value="No" control={<Radio />} label="No" />
        </RadioGroup>

        {personalDetails.career_break === 'Yes' && (
          <>
            <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Reason</Typography>
            <TextField
              select
              fullWidth
              variant="outlined"
              margin="dense"
              name="break_reasons" // Updated to match backend field name
              value={personalDetails.break_reasons}
              onChange={handlePersonalDetailsChange}
            >
              {breakReasons.map((reason) => (
                <MenuItem key={reason} value={reason}>{reason}</MenuItem>
              ))}
            </TextField>

            <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Break started from</Typography>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <TextField
                select
                variant="outlined"
                margin="dense"
                name="break_start_month" // Updated to match backend field name
                value={personalDetails.break_start_month}
                onChange={handlePersonalDetailsChange}
                style={{ flex: 1, marginRight: '10px' }}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
                ))}
              </TextField>
              <TextField
                select
                variant="outlined"
                margin="dense"
                name="break_start_year" // Updated to match backend field name
                value={personalDetails.break_start_year}
                onChange={handlePersonalDetailsChange}
                style={{ flex: 2 }}
              >
                {Array.from({ length: 100 }, (_, i) => (
                  <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
                ))}
              </TextField>
            </div>

            <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Break ended in</Typography>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <TextField
                select
                variant="outlined"
                margin="dense"
                name="break_end_month" // Updated to match backend field name
                value={personalDetails.break_end_month}
                onChange={handlePersonalDetailsChange}
                style={{ flex: 1, marginRight: '10px' }}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
                ))}
              </TextField>
              <TextField
                select
                variant="outlined"
                margin="dense"
                name="break_end_year" // Updated to match backend field name
                value={personalDetails.break_end_year}
                onChange={handlePersonalDetailsChange}
                style={{ flex: 2 }}
              >
                {Array.from({ length: 100 }, (_, i) => (
                  <MenuItem key={i} value={2023 - i}>{2023 - i}</MenuItem>
                ))}
              </TextField>
            </div>

            <FormControlLabel
              control={<Checkbox checked={personalDetails.currently_on_break} onChange={handlePersonalDetailsChange} name="currently_on_break" />}
              label="Currently on a break"
            />
          </>
        )}

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Work permit for USA</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="work_permit_usa" // Updated to match backend field name
          value={personalDetails.work_permit_usa}
          onChange={handlePersonalDetailsChange}
        >
          {workPermitOptions.map((option, index) => (
            <MenuItem key={index} value={option}>{option}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Work permit for other countries</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="work_permit_others" // Updated to match backend field name
          value={personalDetails.work_permit_others}
          onChange={handlePersonalDetailsChange}
        >
          {countries.map((country, index) => (
            <MenuItem key={index} value={country}>{country}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Permanent address</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="permanent_address" // Updated to match backend field name
          value={personalDetails.permanent_address}
          onChange={handlePersonalDetailsChange}
        />

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Hometown</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="hometown"
          value={personalDetails.hometown}
          onChange={handlePersonalDetailsChange}
        />

        <Typography variant="body2" style={{ fontWeight: 'bold', marginTop: '10px', color: '#000' }}>Pincode</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="pincode"
          value={personalDetails.pincode}
          onChange={handlePersonalDetailsChange}
        />
        
      </DialogContent>

      <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
          Save
        </Button>
      </div>
    </Dialog>
  );
};

export default PersonalDetailsDialog;
