import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogTitle, DialogContent, TextField, Button, Typography } from '@mui/material';
import axios from 'axios';

const ProfileSummaryDialog = ({ open, onClose, onSave }) => {
  const [profileSummary, setProfileSummary] = useState('');

  useEffect(() => {
    if (open) {
      // Fetch the existing profile summary when the dialog opens
      const fetchProfileSummary = async () => {
        try {
          const token = localStorage.getItem('serviceToken');
          const response = await axios.get('http://localhost:8080/users/get/profile-summary', {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          setProfileSummary(response.data.summary || '');
        } catch (error) {
          console.error('Failed to fetch profile summary:', error);
        }
      };

      fetchProfileSummary();
    }
  }, [open]);

  const handleProfileSummaryChange = (event) => {
    setProfileSummary(event.target.value);
  };

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      const response = await axios.patch('http://localhost:8080/users/update/me/profile-summary', {
        summary: profileSummary,
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200) {
        console.log('Profile summary saved successfully:', response.data);
        onSave(response.data.summary);
      } else {
        console.error('Failed to save profile summary:', response);
      }
    } catch (error) {
      console.error('Error saving profile summary:', error);
    }

    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6" style={{ color: '#000' }}>Profile summary</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
          Give recruiters a brief overview of the highlights of your career, key achievements, and career goals to help recruiters know your profile better.
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={4}
          variant="outlined"
          margin="normal"
          name="profileSummary"
          value={profileSummary}
          onChange={handleProfileSummaryChange}
          placeholder="Type here..."
          inputProps={{ maxLength: 1000 }}
        />
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
          <Button onClick={onClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileSummaryDialog;
