import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Dialog, DialogTitle, DialogContent, TextField, Button, Typography,
  MenuItem, Radio, RadioGroup, FormControlLabel, DialogActions
} from '@mui/material';

const ProjectDetailsDialog = ({ open, onClose }) => {
  const yearsRange = Array.from({ length: 25 }, (_, i) => 2024 - i);
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const [project, setProject] = useState({
    title: '',
    employment_education_tag: '',
    client: '',
    status: 'Active',
    worked_from_year: '',
    worked_from_month: '',
    worked_till_year: '',
    worked_till_month: '',
    location: '',
    site: 'Offsite',
    employment_nature: 'Full-time',
    team_size: '',
    role: '',
    role_description: '',
    skills_used: '',
    details: ''
  });

  const token = localStorage.getItem('serviceToken');

  // Fetch project details when the dialog opens
  useEffect(() => {
    if (open) {
      axios.get('http://localhost:8080/users/me/project-details', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then(response => {
        setProject(response.data);
      })
      .catch(error => {
        if (error.response && error.response.status === 404) {
          console.log('Project details not found.');
        } else {
          console.error('Error fetching project details:', error);
        }
      });
    }
  }, [open, token]);

  const handleProjectChange = (event) => {
    const { name, value } = event.target;
    setProject((prevProject) => ({
      ...prevProject,
      [name]: value
    }));
  };

  const handleProjectSave = async () => {
    const updatedProject = { ...project };
  
    if (updatedProject.status !== 'Finished') {
      updatedProject.worked_till_year = null;
      updatedProject.worked_till_month = null;
    }
  
    try {
      await axios.patch('http://localhost:8080/users/update/me/project-details', updatedProject, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      console.log('Project details updated successfully');
      onClose();
    } catch (error) {
      console.error('Error updating project details:', error);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6" style={{ color: '#000' }}>Project</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
          Stand out for employers by adding details about projects you have done in college, internships, or at work.
        </Typography>

        <Typography variant="body2" style={{ fontWeight: 'bold', color: '#000' }}>Project title</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="title"
          value={project.title}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Tag this project with your employment/education</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="employment_education_tag"
          value={project.employment_education_tag}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Client</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="client"
          value={project.client}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Project status</Typography>
        <RadioGroup
          name="status"
          value={project.status}
          onChange={handleProjectChange}
          row
        >
          <FormControlLabel value="Active" control={<Radio />} label="Active" />
          <FormControlLabel value="Finished" control={<Radio />} label="Finished" />
        </RadioGroup>

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Worked from</Typography>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ flex: 1, marginRight: '10px' }}>
            <TextField
              select
              fullWidth
              variant="outlined"
              margin="dense"
              name="worked_from_year"
              value={project.worked_from_year}
              onChange={handleProjectChange}
            >
              {yearsRange.map((year) => (
                <MenuItem key={year} value={year}>{year}</MenuItem>
              ))}
            </TextField>
          </div>
          <div style={{ flex: 1 }}>
            <TextField
              select
              fullWidth
              variant="outlined"
              margin="dense"
              name="worked_from_month"
              value={project.worked_from_month}
              onChange={handleProjectChange}
            >
              {months.map((month, index) => (
                <MenuItem key={index} value={index + 1}>{month}</MenuItem>
              ))}
            </TextField>
          </div>
        </div>

        {project.status === 'Finished' && (
          <>
            <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Worked till</Typography>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div style={{ flex: 1, marginRight: '10px' }}>
                <TextField
                  select
                  fullWidth
                  variant="outlined"
                  margin="dense"
                  name="worked_till_year"
                  value={project.worked_till_year}
                  onChange={handleProjectChange}
                >
                  {yearsRange.map((year) => (
                    <MenuItem key={year} value={year}>{year}</MenuItem>
                  ))}
                </TextField>
              </div>
              <div style={{ flex: 1 }}>
                <TextField
                  select
                  fullWidth
                  variant="outlined"
                  margin="dense"
                  name="worked_till_month"
                  value={project.worked_till_month}
                  onChange={handleProjectChange}
                >
                  {months.map((month, index) => (
                    <MenuItem key={index} value={index + 1}>{month}</MenuItem>
                  ))}
                </TextField>
              </div>
            </div>
          </>
        )}

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Project location</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="location"
          value={project.location}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Project site</Typography>
        <RadioGroup
          name="site"
          value={project.site}
          onChange={handleProjectChange}
          row
        >
          <FormControlLabel value="Offsite" control={<Radio />} label="Offsite" />
          <FormControlLabel value="Onsite" control={<Radio />} label="Onsite" />
        </RadioGroup>

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Nature of employment</Typography>
        <RadioGroup
          name="employment_nature"
          value={project.employment_nature}
          onChange={handleProjectChange}
          row
        >
          <FormControlLabel value="Full-time" control={<Radio />} label="Full-time" />
          <FormControlLabel value="Part-time" control={<Radio />} label="Part-time" />
          <FormControlLabel value="Contractual" control={<Radio />} label="Contractual" />
        </RadioGroup>

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Team size</Typography>
        <TextField
          select
          fullWidth
          variant="outlined"
          margin="dense"
          name="team_size"
          value={project.team_size}
          onChange={handleProjectChange}
        >
          {Array.from({ length: 20 }, (_, i) => (
            <MenuItem key={i} value={i + 1}>{i + 1}</MenuItem>
          ))}
        </TextField>

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Role</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="role"
          value={project.role}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Role description</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          multiline
          rows={3}
          name="role_description"
          value={project.role_description}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Skills used</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          name="skills_used"
          value={project.skills_used}
          onChange={handleProjectChange}
        />

        <Typography variant="body2" style={{ marginTop: '10px', fontWeight: 'bold', color: '#000' }}>Details</Typography>
        <TextField
          fullWidth
          variant="outlined"
          margin="dense"
          multiline
          rows={3}
          name="details"
          value={project.details}
          onChange={handleProjectChange}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel} color="secondary" variant="outlined">
          Cancel
        </Button>
        <Button onClick={handleProjectSave} color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectDetailsDialog;
