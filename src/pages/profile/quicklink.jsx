// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);
//   const [ITSkill, setITSkill] = useState({ name: '', version: '', lastUsed: '', experience: { years: '', months: '' } });
//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });
//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });
//   const [personalDetails, setPersonalDetails] = useState({
//     gender: '',
//     moreInfo: [],
//     maritalStatus: '',
//     dobDay: '',
//     dobMonth: '',
//     dobYear: '',
//     category: '',
//     differentlyAbled: '',
//     disabilityType: '',
//     workplaceAssistance: '',
//     careerBreak: '',
//     breakReasons: [],
//     breakStartYear: '',
//     breakStartMonth: '',
//     breakEndYear: '',
//     breakEndMonth: '',
//     currentlyOnBreak: false,
//     workPermitUSA: '',
//     workPermitOthers: [],
//     permanentAddress: '',
//     hometown: '',
//     pincode: '',
//     languages: []
//   });
//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   // Fetch the user's skills when the component mounts
//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = () => {
//     setITSkillsOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsChange = (event, index, key) => {
//     const { name, value, checked } = event.target;
//     setPersonalDetails((prevDetails) => {
//       const newLanguages = prevDetails.languages.map((language, i) => {
//         if (i === index) {
//           return { ...language, [key]: key === 'read' || key === 'write' || key === 'speak' ? checked : value };
//         }
//         return language;
//       });
//       if (name === 'moreInfo' || name === 'workPermitOthers' || name === 'breakReasons') {
//         const newValue = checked
//           ? [...prevDetails[name], value]
//           : prevDetails[name].filter((item) => item !== value);
//         return { ...prevDetails, [name]: newValue };
//       } else if (name === 'currentlyOnBreak') {
//         return { ...prevDetails, [name]: checked, breakEndYear: checked ? '' : prevDetails.breakEndYear, breakEndMonth: checked ? '' : prevDetails.breakEndMonth };
//       } else {
//         return { ...prevDetails, [name]: value, languages: newLanguages };
//       }
//     });
//   };

//   const handleAddLanguage = () => {
//     setPersonalDetails((prevDetails) => ({
//       ...prevDetails,
//       languages: [...prevDetails.languages, { name: '', proficiency: '', read: false, write: false, speak: false }]
//     }));
//   };

//   const handleDeleteLanguage = (index) => {
//     setPersonalDetails((prevDetails) => ({
//       ...prevDetails,
//       languages: prevDetails.languages.filter((_, i) => i !== index)
//     }));
//   };

//   const handlePersonalDetailsSave = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         personalDetails={personalDetails}
//         onPersonalDetailsChange={handlePersonalDetailsChange}
//         onAddLanguage={handleAddLanguage}
//         onDeleteLanguage={handleDeleteLanguage}
//         onSave={handlePersonalDetailsSave}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };

// export default QuickLinks;



// new version after it skills 

// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';

// import ResumeHeadlineDialog from './resumeheadline';
// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);
//   const [ITSkill, setITSkill] = useState({ name: '', version: '', lastUsed: '', experience: { years: '', months: '' } });
//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });
//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });
//   const [personalDetails, setPersonalDetails] = useState({
//     gender: '',
//     moreInfo: ' ',
//     maritalStatus: '',
//     dobDay: '',
//     dobMonth: '',
//     dobYear: '',
//     category: '',
//     differentlyAbled: '',
//     disabilityType: '',
//     workplaceAssistance: '',
//     careerBreak: '',
//     breakReasons: ' ',
//     breakStartYear: '',
//     breakStartMonth: '',
//     breakEndYear: '',
//     breakEndMonth: '',
//     currentlyOnBreak: false,
//     workPermitUSA: '',
//     workPermitOthers: ' ',
//     permanentAddress: '',
//     hometown: '',
//     pincode: '',
  
//   });
//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   // Fetch the user's skills when the component mounts
//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };
//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };
//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   // Function to handle the closing of the Profile Summary dialog
//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

  

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handlePersonalDetailsChange = (event) => {
//     const { name, value, checked } = event.target;
//     setPersonalDetails((prevDetails) => {
//       if (name === 'moreInfo' || name === 'workPermitOthers' || name === 'breakReasons') {
//         const newValue = checked
//           ? [...prevDetails[name], value]
//           : prevDetails[name].filter((item) => item !== value);
//         return { ...prevDetails, [name]: newValue };
//       } else if (name === 'currentlyOnBreak') {
//         return {
//           ...prevDetails,
//           [name]: checked,
//           breakEndYear: checked ? '' : prevDetails.breakEndYear,
//           breakEndMonth: checked ? '' : prevDetails.breakEndMonth
//         };
//       } else {
//         return { ...prevDetails, [name]: value };
//       }
//     });
//   };
  

  
//   const handlePersonalDetailsSave = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

// <PersonalDetailsDialog
//   open={personalDetailsOpen}
//   onClose={handlePersonalDetailsClose}
//   personalDetails={personalDetails}
//   onPersonalDetailsChange={handlePersonalDetailsChange}
//   onSave={handlePersonalDetailsSave}
// />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };

// export default QuickLinks;




// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';

// import ResumeHeadlineDialog from './resumeheadline';
// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);
//   const [ITSkill, setITSkill] = useState({ name: '', version: '', lastUsed: '', experience: { years: '', months: '' } });
//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });
//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });
 
//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   // Fetch the user's skills when the component mounts
//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };
//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };
//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   // Function to handle the closing of the Profile Summary dialog
//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

  

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };



//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

// <PersonalDetailsDialog
//   open={personalDetailsOpen}
//   onClose={handlePersonalDetailsClose}
//   personalDetails={personalDetails}
//   onPersonalDetailsChange={handlePersonalDetailsChange}
//   onSave={handlePersonalDetailsSave}
// />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };

// export default QuickLinks;



// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);
//   const [ITSkill, setITSkill] = useState({ name: '', version: '', lastUsed: '', experience: { years: '', months: '' } });
//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });
//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });
//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   // Fetch the user's skills when the component mounts
//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handlePersonalDetailsSave = (personalDetails) => {
//     console.log('Saved personal details:', personalDetails);
//     setSnackbarMessage('Personal details saved successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         onSave={handlePersonalDetailsSave}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };



// Here is the code for profile summary 
// export default QuickLinks;
// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);

//   const [ITSkill, setITSkill] = useState({ 
//     name: '', 
//     version: '', 
//     lastUsed: '', 
//     experience: { years: '', months: '' } 
//   });

//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });

//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });

//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handlePersonalDetailsSave = (personalDetails) => {
//     console.log('Saved personal details:', personalDetails);
//     setSnackbarMessage('Personal details saved successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         onSave={handlePersonalDetailsSave}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };



// This is after RESUMEHEADING
// export default QuickLinks;
// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);

//   const [ITSkill, setITSkill] = useState({ 
//     name: '', 
//     version: '', 
//     lastUsed: '', 
//     experience: { years: '', months: '' } 
//   });

//   const [project, setProject] = useState({
//     title: '',
//     employmentEducationTag: '',
//     client: '',
//     status: 'inProgress',
//     workedFromYear: '',
//     workedFromMonth: '',
//     location: '',
//     site: 'offsite',
//     employmentNature: 'fullTime',
//     teamSize: '',
//     role: '',
//     roleDescription: '',
//     skillsUsed: '',
//     details: ''
//   });

//   const [careerProfile, setCareerProfile] = useState({
//     currentIndustry: '',
//     department: '',
//     jobRole: '',
//     jobType: [],
//     employmentType: [],
//     shift: '',
//     preferredLocation: '',
//     salaryCurrency: '₹',
//     expectedSalary: ''
//   });

//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

  // useEffect(() => {
  //   const fetchSkills = async () => {
  //     try {
  //       const token = localStorage.getItem('serviceToken');
  //       const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //           'Content-Type': 'application/json',
  //         },
  //       });

  //       if (response.status === 200) {
  //         setSkills(response.data.map(skill => skill.name));
  //       } else {
  //         console.error('Failed to fetch skills:', response);
  //       }
  //     } catch (error) {
  //       console.error('Error fetching skills:', error);
  //     }
  //   };

  //   fetchSkills();
  // }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

  // const handleSkillsSave = async () => {
  //   try {
  //     const token = localStorage.getItem('serviceToken');
  //     const response = await axios.patch(
  //       'http://127.0.0.1:8000/users/update/me/skills',
  //       skills.map(skill => ({ name: skill })),
  //       {
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //           'Content-Type': 'application/json',
  //         },
  //       }
  //     );

  //     if (response.status === 200) {
  //       setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
  //       setSnackbarMessage('Skills updated successfully!');
  //       setSnackbarSeverity('success');
  //     } else {
  //       console.error('Failed to save skills:', response);
  //       setSnackbarMessage('Failed to update skills!');
  //       setSnackbarSeverity('error');
  //     }
  //   } catch (error) {
  //     console.error('Error saving skills:', error);
  //     setSnackbarMessage('Error saving skills!');
  //     setSnackbarSeverity('error');
  //   }

  //   setSkillsOpen(false);
  //   setSnackbarOpen(true);
  // };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleCareerProfileChange = (event) => {
//     const { name, value, checked } = event.target;
//     if (name === 'jobType' || name === 'employmentType') {
//       setCareerProfile((prevProfile) => {
//         const newValue = checked
//           ? [...prevProfile[name], value]
//           : prevProfile[name].filter((item) => item !== value);
//         return { ...prevProfile, [name]: newValue };
//       });
//     } else {
//       setCareerProfile((prevProfile) => ({
//         ...prevProfile,
//         [name]: value
//       }));
//     }
//   };

//   const handleCareerProfileSave = () => {
//     setCareerProfileOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handlePersonalDetailsSave = (personalDetails) => {
//     console.log('Saved personal details:', personalDetails);
//     setSnackbarMessage('Personal details saved successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//     setPersonalDetailsOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography> 
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography> 
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography> 
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography> 
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography> 
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography> 
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography> 
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         careerProfile={careerProfile}
//         onCareerProfileChange={handleCareerProfileChange}
//         onSave={handleCareerProfileSave}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         onSave={handlePersonalDetailsSave}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };


// this is after carrier


// export default QuickLinks;

// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);

//   const [ITSkill, setITSkill] = useState({
//     name: '',
//     version: '',
//     lastUsed: '',
//     experience: { years: '', months: '' },
//   });

  

//   const [profileSummary, setProfileSummary] = useState('');
//   const [resumeHeadline, setResumeHeadline] = useState('');

//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };
//   // Add this function to handle the opening of the Personal Details Dialog
//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };



//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '') {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleITSkillChange = (event) => {
//     const { name, value } = event.target;
//     setITSkill((prevSkill) => {
//       const updatedExperience = { ...prevSkill.experience };
//       if (name === 'experienceYears') {
//         updatedExperience.years = value;
//       } else if (name === 'experienceMonths') {
//         updatedExperience.months = value;
//       }
//       return {
//         ...prevSkill,
//         [name === 'experienceYears' || name === 'experienceMonths' ? 'experience' : name]: name === 'experienceYears' || name === 'experienceMonths' ? updatedExperience : value,
//       };
//     });
//   };

//   const handleITSkillSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/it-skills',
//         [ITSkill],
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSnackbarMessage('IT Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save IT skills:', response);
//         setSnackbarMessage('Failed to update IT skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving IT skills:', error);
//       setSnackbarMessage('Error saving IT skills!');
//       setSnackbarSeverity('error');
//     }

//     setITSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleProjectClickOpen = () => {
//     setProjectOpen(true);
//   };

//   const handleProjectClose = () => {
//     setProjectOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };


//   const handleProjectChange = (event) => {
//     const { name, value } = event.target;
//     setProject((prevProject) => ({
//       ...prevProject,
//       [name]: value
//     }));
//   };

//   const handleProjectSave = () => {
//     setProjectOpen(false);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handleResumeHeadlineChange = (event) => {
//     setResumeHeadline(event.target.value);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography>
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography>
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography>
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography>
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography>
//               <a href="#" style={styles.link} onClick={handleProjectClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography>
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography>
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography>
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//         skill={ITSkill}
//         onSkillChange={handleITSkillChange}
//         onSave={handleITSkillSave}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={handleProjectClose}
//         project={project}
//         onProjectChange={handleProjectChange}
//         onSave={handleProjectSave}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         onSave={() => setCareerProfileOpen(false)}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline={resumeHeadline}
//         onResumeHeadlineChange={handleResumeHeadlineChange}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         onSave={(personalDetails) => {
//           console.log('Saved personal details:', personalDetails);
//           setSnackbarMessage('Personal details saved successfully!');
//           setSnackbarSeverity('success');
//           setSnackbarOpen(true);
//           setPersonalDetailsOpen(false);
//         }}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };

// export default QuickLinks;
import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
import MuiAlert from '@mui/material/Alert';
import axios from 'axios';
import EducationDialog from './education';
import ITSkillsDialog from './addskils';
import ProjectDetailsDialog from './projects';
import CareerProfileDialog from './carrer';
import PersonalDetailsDialog from './personaldetails';
import ProfileSummaryDialog from './profilesummary';
import ResumeHeadlineDialog from './resumeheadline';

const styles = {
  card: {
    width: '250px',
    padding: '20px',
    borderRadius: '8px',
    backgroundColor: '#ffffff',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    fontFamily: 'Arial, sans-serif',
  },
  container: {
    fontFamily: 'Arial, sans-serif',
  },
  header: {
    fontSize: '18px',
    marginBottom: '10px',
  },
  list: {
    listStyleType: 'none',
    padding: '0',
  },
  listItem: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: '10px',
  },
  listItemText: {
    fontSize: '14px',
    color: '#333',
  },
  link: {
    fontSize: '14px',
    color: '#1a73e8',
    textDecoration: 'none',
    cursor: 'pointer',
  },
};

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

const QuickLinks = () => {
  const [skillsOpen, setSkillsOpen] = useState(false);
  const [educationOpen, setEducationOpen] = useState(false);
  const [ITSkillsOpen, setITSkillsOpen] = useState(false);
  const [careerProfileOpen, setCareerProfileOpen] = useState(false);
  const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
  const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
  const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
  const [projectOpen, setProjectOpen] = useState(false);  // "Add" button logic for project dialog
  const [resumeFile, setResumeFile] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  
  const [skills, setSkills] = useState([]);
  const [newSkill, setNewSkill] = useState('');
  const [highlightedSkill, setHighlightedSkill] = useState(null);
  const [profileSummary, setProfileSummary] = useState(''); // Added profileSummary state

  // useEffect(() => {
  //   const fetchSkills = async () => {
  //     try {
  //       const token = localStorage.getItem('serviceToken');
  //       const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
        
          
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //           'Content-Type': 'application/json',
  //         },
  //       });

  //       if (response.status === 200) {
  //         setSkills(response.data.map(skill => skill.name));
  //       } else {
  //         console.error('Failed to fetch skills:', response);
  //       }
  //     } catch (error) {
  //       console.error('Error fetching skills:', error);
  //     }
  //   };

  //   fetchSkills();
  // }, []);

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const token = localStorage.getItem('serviceToken');
        console.log('Token:', token);  // Debugging token
        const response = await axios.get('http://localhost:8080/users/get/skills', {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
  
        console.log('Response status:', response.status);  // Debugging response status
        console.log('Response data:', response.data);  // Debugging response data
  
        if (response.status === 200) {
          setSkills(response.data.map(skill => skill.name));
        } else {
          console.error('Failed to fetch skills:', response);
        }
      } catch (error) {
        console.error('Error fetching skills:', error);
      }
    };
  
    fetchSkills();
  }, []);


  const handleResumeHeadlineClickOpen = () => {
    setResumeHeadlineOpen(true);
  };

  const handleResumeHeadlineClose = () => {
    setResumeHeadlineOpen(false);
  };

  const handleSkillsClickOpen = () => {
    setSkillsOpen(true);
  };

  const handleSkillsClose = () => {
    setSkillsOpen(false);
  };

  const handleResumeHeadlineSave = () => {
    setResumeHeadlineOpen(false);
  };

  const handleProfileSummaryClickOpen = () => {
    setProfileSummaryOpen(true);
  };

  const handleProfileSummaryClose = () => {
    setProfileSummaryOpen(false);
  };

  const handlePersonalDetailsClickOpen = () => {
    setPersonalDetailsOpen(true);
  };

  const handlePersonalDetailsClose = () => {
    setPersonalDetailsOpen(false);
  };

  const handleCareerProfileClickOpen = () => {
    setCareerProfileOpen(true);
  };

  const handleCareerProfileClose = () => {
    setCareerProfileOpen(false);
  };

  const handleSkillsSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      const response = await axios.patch(
        'http://localhost:8080/users/update/me/skills',
        skills.map(skill => ({ name: skill })),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200) {
        setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
        setSnackbarMessage('Skills updated successfully!');
        setSnackbarSeverity('success');
      } else {
        console.error('Failed to save skills:', response);
        setSnackbarMessage('Failed to update skills!');
        setSnackbarSeverity('error');
      }
    } catch (error) {
      console.error('Error saving skills:', error);
      setSnackbarMessage('Error saving skills!');
      setSnackbarSeverity('error');
    }

    setSkillsOpen(false);
    setSnackbarOpen(true);
  };

  const handleAddSkill = () => {
    if (newSkill.trim() !== '') {
      setSkills([...skills, newSkill]);
      setHighlightedSkill(newSkill); // Highlight the newly added skill
      setNewSkill('');
    }
  };

  const handleSkillChange = (event) => {
    setNewSkill(event.target.value);
  };

  const handleSuggestedSkillClick = (skill) => {
    if (!skills.includes(skill)) {
      setSkills([...skills, skill]);
      setHighlightedSkill(skill); // Highlight the selected skill
    }
  };

  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  const handleEducationClickOpen = () => {
    setEducationOpen(true);
  };

  const handleEducationClose = () => {
    setEducationOpen(false);
  };

  const handleITSkillsClickOpen = () => {
    setITSkillsOpen(true);
  };

  const handleITSkillsClose = () => {
    setITSkillsOpen(false);
  };

  const handleResumeFileChange = (event) => {
    setResumeFile(event.target.files[0]);
    setSnackbarMessage('Resume uploaded successfully!');
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleProfileSummaryChange = (event) => {
    setProfileSummary(event.target.value);
  };

  const handleProfileSummarySave = () => {
    setProfileSummaryOpen(false);
  };

  return (
    <>
      <div style={styles.card}>
        <div style={styles.container}>
          <Typography variant="h6" style={styles.header}>Quick links</Typography>
          <ul style={styles.list}>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
              <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
              <input
                id="resumeUpload"
                type="file"
                style={{ display: 'none' }}
                onChange={handleResumeFileChange}
              />
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography>
              <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Key skills</Typography>
              <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Education</Typography>
              <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>IT skills</Typography>
              <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Projects</Typography>
              <a href="#" style={styles.link} onClick={() => setProjectOpen(true)}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography>
              <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Career profile</Typography>
              <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
            </li>
            <li style={styles.listItem}>
              <Typography variant="body2" style={styles.listItemText}>Personal details</Typography>
              <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
            </li>
          </ul>
        </div>
      </div>

      <Dialog open={skillsOpen} onClose={handleSkillsClose}>
        <DialogTitle>
          <Typography variant="h6">Key skills</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" style={{ marginBottom: '10px' }}>
            Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
          </Typography>
          <div style={{ marginTop: '20px' }}>
            <Typography variant="h6">Your Skills:</Typography>
            <div>
              {skills.map((skill, index) => (
                <Chip
                  key={index}
                  label={skill}
                  style={{
                    margin: '5px',
                    backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
                    color: highlightedSkill === skill ? '#fff' : undefined,
                    fontWeight: highlightedSkill === skill ? 'bold' : undefined,
                  }}
                />
              ))}
            </div>
          </div>
          <TextField
            fullWidth
            label="Add skills"
            variant="outlined"
            margin="normal"
            value={newSkill}
            onChange={handleSkillChange}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleAddSkill();
                e.preventDefault(); // Prevent form submission on Enter key press
              }
            }}
          />
          <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
            Add Skill
          </Button>
          <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
            <Button onClick={handleSkillsClose} color="primary">
              Cancel
            </Button>
            <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <ITSkillsDialog
        open={ITSkillsOpen}
        onClose={handleITSkillsClose}
      />

      <ProjectDetailsDialog
        open={projectOpen}
        onClose={() => setProjectOpen(false)}
      />

      <CareerProfileDialog
        open={careerProfileOpen}
        onClose={handleCareerProfileClose}
        onSave={() => setCareerProfileOpen(false)}
      />

      <ProfileSummaryDialog
        open={profileSummaryOpen}
        onClose={handleProfileSummaryClose}
        profileSummary={profileSummary}
        onProfileSummaryChange={handleProfileSummaryChange}
        onSave={handleProfileSummarySave}
      />

      <ResumeHeadlineDialog
        open={resumeHeadlineOpen}
        onClose={handleResumeHeadlineClose}
        resumeHeadline=""
        onResumeHeadlineChange={() => {}}
        onSave={handleResumeHeadlineSave}
      />

      <PersonalDetailsDialog
        open={personalDetailsOpen}
        onClose={handlePersonalDetailsClose}
        onSave={(personalDetails) => {
          console.log('Saved personal details:', personalDetails);
          setSnackbarMessage('Personal details saved successfully!');
          setSnackbarSeverity('success');
          setSnackbarOpen(true);
          setPersonalDetailsOpen(false);
        }}
      />

      <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default QuickLinks;


// import React, { useState, useEffect } from 'react';
// import { Dialog, DialogTitle, DialogContent, TextField, Button, Chip, Typography, Snackbar } from '@mui/material';
// import MuiAlert from '@mui/material/Alert';
// import axios from 'axios';
// import EducationDialog from './education';
// import ITSkillsDialog from './addskils';
// import ProjectDetailsDialog from './projects';
// import CareerProfileDialog from './carrer';
// import PersonalDetailsDialog from './personaldetails';
// import ProfileSummaryDialog from './profilesummary';
// import ResumeHeadlineDialog from './resumeheadline';

// const styles = {
//   card: {
//     width: '250px',
//     padding: '20px',
//     borderRadius: '8px',
//     backgroundColor: '#ffffff',
//     boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
//     fontFamily: 'Arial, sans-serif',
//   },
//   container: {
//     fontFamily: 'Arial, sans-serif',
//   },
//   header: {
//     fontSize: '18px',
//     marginBottom: '10px',
//   },
//   list: {
//     listStyleType: 'none',
//     padding: '0',
//   },
//   listItem: {
//     display: 'flex',
//     justifyContent: 'space-between',
//     marginBottom: '10px',
//   },
//   listItemText: {
//     fontSize: '14px',
//     color: '#333',
//   },
//   link: {
//     fontSize: '14px',
//     color: '#1a73e8',
//     textDecoration: 'none',
//     cursor: 'pointer',
//   },
// };

// const Alert = React.forwardRef(function Alert(props, ref) {
//   return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
// });

// const QuickLinks = () => {
//   const [skillsOpen, setSkillsOpen] = useState(false);
//   const [educationOpen, setEducationOpen] = useState(false);
//   const [ITSkillsOpen, setITSkillsOpen] = useState(false);
//   const [careerProfileOpen, setCareerProfileOpen] = useState(false);
//   const [personalDetailsOpen, setPersonalDetailsOpen] = useState(false);
//   const [profileSummaryOpen, setProfileSummaryOpen] = useState(false);
//   const [resumeHeadlineOpen, setResumeHeadlineOpen] = useState(false);
//   const [projectOpen, setProjectOpen] = useState(false);
//   const [resumeFile, setResumeFile] = useState(null);
//   const [snackbarOpen, setSnackbarOpen] = useState(false);
//   const [snackbarMessage, setSnackbarMessage] = useState('');
//   const [snackbarSeverity, setSnackbarSeverity] = useState('success');

//   const [skills, setSkills] = useState([]);
//   const [newSkill, setNewSkill] = useState('');
//   const [highlightedSkill, setHighlightedSkill] = useState(null);
//   const [profileSummary, setProfileSummary] = useState('');

//   useEffect(() => {
//     const fetchSkills = async () => {
//       try {
//         const token = localStorage.getItem('serviceToken');
//         const response = await axios.get('http://127.0.0.1:8000/users/get/skills', {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         });

//         if (response.status === 200) {
//           setSkills(response.data.map(skill => skill.name));
//         } else {
//           console.error('Failed to fetch skills:', response);
//         }
//       } catch (error) {
//         console.error('Error fetching skills:', error);
//       }
//     };

//     fetchSkills();
//   }, []);

//   const handleResumeHeadlineClickOpen = () => {
//     setResumeHeadlineOpen(true);
//   };

//   const handleResumeHeadlineClose = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleSkillsClickOpen = () => {
//     setSkillsOpen(true);
//   };

//   const handleSkillsClose = () => {
//     setSkillsOpen(false);
//   };

//   const handleResumeHeadlineSave = () => {
//     setResumeHeadlineOpen(false);
//   };

//   const handleProfileSummaryClickOpen = () => {
//     setProfileSummaryOpen(true);
//   };

//   const handleProfileSummaryClose = () => {
//     setProfileSummaryOpen(false);
//   };

//   const handlePersonalDetailsClickOpen = () => {
//     setPersonalDetailsOpen(true);
//   };

//   const handlePersonalDetailsClose = () => {
//     setPersonalDetailsOpen(false);
//   };

//   const handleCareerProfileClickOpen = () => {
//     setCareerProfileOpen(true);
//   };

//   const handleCareerProfileClose = () => {
//     setCareerProfileOpen(false);
//   };

//   const handleSkillsSave = async () => {
//     try {
//       const token = localStorage.getItem('serviceToken');
//       const response = await axios.patch(
//         'http://127.0.0.1:8000/users/update/me/skills',
//         skills.map(skill => ({ name: skill })),
//         {
//           headers: {
//             Authorization: `Bearer ${token}`,
//             'Content-Type': 'application/json',
//           },
//         }
//       );

//       if (response.status === 200) {
//         setSkills(response.data.map(skill => skill.name)); // Update skills with the returned data
//         setSnackbarMessage('Skills updated successfully!');
//         setSnackbarSeverity('success');
//       } else {
//         console.error('Failed to save skills:', response);
//         setSnackbarMessage('Failed to update skills!');
//         setSnackbarSeverity('error');
//       }
//     } catch (error) {
//       console.error('Error saving skills:', error);
//       setSnackbarMessage('Error saving skills!');
//       setSnackbarSeverity('error');
//     }

//     setSkillsOpen(false);
//     setSnackbarOpen(true);
//   };

//   const handleAddSkill = () => {
//     if (newSkill.trim() !== '' && !skills.includes(newSkill)) {
//       setSkills([...skills, newSkill]);
//       setHighlightedSkill(newSkill); // Highlight the newly added skill
//       setNewSkill('');
//     }
//   };

//   const handleSkillChange = (event) => {
//     setNewSkill(event.target.value);
//   };

//   const handleSuggestedSkillClick = (skill) => {
//     if (!skills.includes(skill)) {
//       setSkills([...skills, skill]);
//       setHighlightedSkill(skill); // Highlight the selected skill
//     }
//   };

//   const handleSnackbarClose = (event, reason) => {
//     if (reason === 'clickaway') {
//       return;
//     }
//     setSnackbarOpen(false);
//   };

//   const handleEducationClickOpen = () => {
//     setEducationOpen(true);
//   };

//   const handleEducationClose = () => {
//     setEducationOpen(false);
//   };

//   const handleITSkillsClickOpen = () => {
//     setITSkillsOpen(true);
//   };

//   const handleITSkillsClose = () => {
//     setITSkillsOpen(false);
//   };

//   const handleResumeFileChange = (event) => {
//     setResumeFile(event.target.files[0]);
//     setSnackbarMessage('Resume uploaded successfully!');
//     setSnackbarSeverity('success');
//     setSnackbarOpen(true);
//   };

//   const handleProfileSummaryChange = (event) => {
//     setProfileSummary(event.target.value);
//   };

//   const handleProfileSummarySave = () => {
//     setProfileSummaryOpen(false);
//   };

//   return (
//     <>
//       <div style={styles.card}>
//         <div style={styles.container}>
//           <Typography variant="h6" style={styles.header}>Quick links</Typography>
//           <ul style={styles.list}>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume</Typography>
//               <a href="#" style={styles.link} onClick={() => document.getElementById('resumeUpload').click()}>Upload</a>
//               <input
//                 id="resumeUpload"
//                 type="file"
//                 style={{ display: 'none' }}
//                 onChange={handleResumeFileChange}
//               />
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Resume headline</Typography>
//               <a href="#" style={styles.link} onClick={handleResumeHeadlineClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Key skills</Typography>
//               <a href="#" style={styles.link} onClick={handleSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Education</Typography>
//               <a href="#" style={styles.link} onClick={handleEducationClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>IT skills</Typography>
//               <a href="#" style={styles.link} onClick={handleITSkillsClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Projects</Typography>
//               <a href="#" style={styles.link} onClick={() => setProjectOpen(true)}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Profile summary</Typography>
//               <a href="#" style={styles.link} onClick={handleProfileSummaryClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Career profile</Typography>
//               <a href="#" style={styles.link} onClick={handleCareerProfileClickOpen}>Add</a>
//             </li>
//             <li style={styles.listItem}>
//               <Typography variant="body2" style={styles.listItemText}>Personal details</Typography>
//               <a href="#" style={styles.link} onClick={handlePersonalDetailsClickOpen}>Add</a>
//             </li>
//           </ul>
//         </div>
//       </div>

//       <Dialog open={skillsOpen} onClose={handleSkillsClose}>
//         <DialogTitle>
//           <Typography variant="h6">Key skills</Typography>
//         </DialogTitle>
//         <DialogContent>
//           <Typography variant="body2" style={{ marginBottom: '10px' }}>
//             Add skills that best define your expertise, for e.g., Direct Marketing, Oracle, Java, etc. (Minimum 1)
//           </Typography>
//           <div style={{ marginTop: '20px' }}>
//             <Typography variant="h6">Your Skills:</Typography>
//             <div>
//               {skills.map((skill, index) => (
//                 <Chip
//                   key={index}
//                   label={skill}
//                   style={{
//                     margin: '5px',
//                     backgroundColor: highlightedSkill === skill ? '#0073e6' : undefined,
//                     color: highlightedSkill === skill ? '#fff' : undefined,
//                     fontWeight: highlightedSkill === skill ? 'bold' : undefined,
//                   }}
//                 />
//               ))}
//             </div>
//           </div>
//           <TextField
//             fullWidth
//             label="Add skills"
//             variant="outlined"
//             margin="normal"
//             value={newSkill}
//             onChange={handleSkillChange}
//             onKeyDown={(e) => {
//               if (e.key === 'Enter') {
//                 handleAddSkill();
//                 e.preventDefault(); // Prevent form submission on Enter key press
//               }
//             }}
//           />
//           <Button onClick={handleAddSkill} color="primary" variant="contained" style={{ marginTop: '10px' }}>
//             Add Skill
//           </Button>
//           <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
//             <Button onClick={handleSkillsClose} color="primary">
//               Cancel
//             </Button>
//             <Button onClick={handleSkillsSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
//               Save
//             </Button>
//           </div>
//         </DialogContent>
//       </Dialog>

//       <ITSkillsDialog
//         open={ITSkillsOpen}
//         onClose={handleITSkillsClose}
//       />

//       <ProjectDetailsDialog
//         open={projectOpen}
//         onClose={() => setProjectOpen(false)}
//       />

//       <CareerProfileDialog
//         open={careerProfileOpen}
//         onClose={handleCareerProfileClose}
//         onSave={() => setCareerProfileOpen(false)}
//       />

//       <ProfileSummaryDialog
//         open={profileSummaryOpen}
//         onClose={handleProfileSummaryClose}
//         profileSummary={profileSummary}
//         onProfileSummaryChange={handleProfileSummaryChange}
//         onSave={handleProfileSummarySave}
//       />

//       <ResumeHeadlineDialog
//         open={resumeHeadlineOpen}
//         onClose={handleResumeHeadlineClose}
//         resumeHeadline=""
//         onResumeHeadlineChange={() => {}}
//         onSave={handleResumeHeadlineSave}
//       />

//       <PersonalDetailsDialog
//         open={personalDetailsOpen}
//         onClose={handlePersonalDetailsClose}
//         onSave={(personalDetails) => {
//           console.log('Saved personal details:', personalDetails);
//           setSnackbarMessage('Personal details saved successfully!');
//           setSnackbarSeverity('success');
//           setSnackbarOpen(true);
//           setPersonalDetailsOpen(false);
//         }}
//       />

//       <EducationDialog open={educationOpen} handleClose={handleEducationClose} />

//       <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleSnackbarClose}>
//         <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
//           {snackbarMessage}
//         </Alert>
//       </Snackbar>
//     </>
//   );
// };

// export default QuickLinks;




