import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogTitle, DialogContent, TextField, Button, Typography } from '@mui/material';
import axios from 'axios';

const ResumeHeadlineDialog = ({ open, onClose, onSave }) => {
  // Initialize state for the resume headline
  const [resumeHeadline, setResumeHeadline] = useState('');
  const [loading, setLoading] = useState(true);

  // Fetch the current resume headline when the dialog is opened
  useEffect(() => {
    if (open) {
      const fetchResumeHeadline = async () => {
        try {
          const token = localStorage.getItem('serviceToken');
          const response = await axios.get('http://localhost:8080/users/get/resume_headline', {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          if (response.status === 200) {
            setResumeHeadline(response.data.resume_headline);
          } else {
            console.error('Failed to fetch resume headline:', response);
          }
        } catch (error) {
          console.error('Error fetching resume headline:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchResumeHeadline();
    }
  }, [open]);

  const handleHeadlineChange = (event) => {
    setResumeHeadline(event.target.value);
    console.log('Resume Headline:', event.target.value);
  };

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('serviceToken');
      const response = await axios.patch(
        'http://localhost:8080/users/update/me/resume_headline',
        { resume_headline: resumeHeadline },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 200) {
        onSave(resumeHeadline); // Pass the headline back to the parent on save
      } else {
        console.error('Failed to save resume headline:', response);
      }
    } catch (error) {
      console.error('Error saving resume headline:', error);
    } finally {
      onClose(); // Close the dialog after saving
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>
        <Typography variant="h6" style={{ color: '#000' }}>Resume headline</Typography>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" style={{ marginBottom: '10px', color: '#000' }}>
          It is the first thing recruiters notice in your profile. Write a concise headline introducing yourself to employers. (Minimum 5 words)
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={3}
          variant="outlined"
          margin="normal"
          name="resumeHeadline"
          value={resumeHeadline}
          onChange={handleHeadlineChange}
          placeholder="Minimum 5 words. Sample headlines: Sales Manager well versed in Excel and Dynamics CRM. Senior-level Interior Designer with expertise in 3D modeling."
          inputProps={{ maxLength: 250 }}
        />
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
          <Button onClick={onClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSave} color="primary" variant="contained" style={{ marginLeft: '10px' }}>
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResumeHeadlineDialog;
