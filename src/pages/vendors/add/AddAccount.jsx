import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import CustomName<PERSON>ield from 'components/custom-components/CustomNameField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomContactN<PERSON>berField from 'components/custom-components/CustomContactNumberField';
import CustomOfficeNumberField from 'components/custom-components/CustomOfficeNumberField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';

const AddAccount = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control } = useForm();

  const onSubmit = (data) => {
    console.log('Form Data:', data);
    // You can perform further actions here, such as sending the data to an API
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title="Account"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Contact Person </InputLabel>
              <CustomNameField
                name="contact_person"
                control={control}
                placeholder="Enter Contact Person  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Mobile number </InputLabel>
              <CustomContactNumberField
                name="mobile_number"
                control={control}
                placeholder="Enter Mobile Number "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Office Number </InputLabel>
              <CustomOfficeNumberField
                name="office_number"
                control={control}
                placeholder="Enter Office Number  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Email </InputLabel>
              <CustomEmailField
                name="degree "
                control={control}
                placeholder="Enter Email  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Designation </InputLabel>
              <CustomNameField
                name="designation   "
                control={control}
                placeholder="Enter Designation"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Payment terms</InputLabel>
              <CustomDropdownField
                name="payment_terms"
                control={control}
                placeholder="Select Payment terms"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                options={[
                  { value: 'Net 7' },
                  { value: 'Net 10' },
                  { value: 'Net 15' },
                  { value: 'Net 20' },
                  { value: 'Net 30' },
                  { value: 'Net 45' },
                  { value: 'Net 60' },
                  { value: 'Net 90' },
                  { value: 'Net 120' },
                ]}
              />
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
    </form>
  );
};

export default AddAccount;
