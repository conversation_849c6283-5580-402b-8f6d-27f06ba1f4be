import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import axios from 'axios';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomFederalIDField from 'components/custom-components/CustomFederalIDField';
import CustomWebsiteField from 'components/custom-components/CustomWebsiteField';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomMultiTagInput from 'components/custom-components/CustomMultiTagInput';

const AddBusinessInfo = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue } = useForm();
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [industries, setIndustries] = useState([]);

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    setFormData(getValues());
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);

  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        const response = await axios.get(`${API_URL}/industry`);
        console.log('📥 Industry API Response:', response.data);
        setIndustries(response.data);
      } catch (error) {
        console.error('❌ Error fetching industries:', error);
      }
    };
    fetchIndustries();
  }, []);

  const industryOptions = industries.map((industry) => ({
    value: industry.name // Adjust based on actual API response
  }));

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title="Business Information"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Primary Business Unit <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="primary_business_unit"
                control={control}
                placeholder="Select"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Business Unit <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="business_unit"
                control={control}
                placeholder="Select"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor Visibility </InputLabel>
              <CustomDropdownField
                name="vendor_visibility "
                control={control}
                placeholder="Select Vendor Visibility "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                options={[{ value: 'Orginizational Level' }, { value: 'Business Level' }]}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor name </InputLabel>
              <CustomNameField
                name="vendor_name  "
                control={control}
                placeholder="Enter Vendor name    "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Federal Id </InputLabel>
              <CustomFederalIDField
                name="federal_id  "
                control={control}
                placeholder="Enter Federal Id     "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Website </InputLabel>
              <CustomWebsiteField
                name="website "
                control={control}
                placeholder="Enter Website  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Contact Number </InputLabel>
              <CustomContactNumberField
                name="contact_number  "
                control={control}
                placeholder="Enter Contact Number"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Fax</InputLabel>
              <CustomNameField name="fax" control={control} placeholder="Enter Fax" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address </InputLabel>
              <CustomAddressField
                name="address "
                control={control}
                placeholder="Enter Address "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="Country">
                Country <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="country"
                control={control}
                placeholder="Select Country"
                options={countriesOptions}
                onBlur={handleInputChange}
                rules={{ required: 'Country is required' }}
                onChange={(e) => handleCountryChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="state">
                State <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="state"
                control={control}
                placeholder="Select State"
                options={statesOptions}
                onBlur={handleInputChange}
                rules={{ required: 'State is required' }}
                onChange={(e) => handleStateChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>City </InputLabel>
              <CustomNameField
                name="city "
                control={control}
                placeholder="Enter City "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Pin Code </InputLabel>
              <CustomPostalCodeField
                name="pin_code "
                control={control}
                placeholder="Enter Pin Code  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Send Requirements</InputLabel>
              <CustomNameField
                name="requirements "
                control={control}
                placeholder="Check box"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Send Hotlist</InputLabel>
              <CustomNameField
                name="hotlist "
                control={control}
                placeholder="Check box"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Primary Owner </InputLabel>
              <CustomNameField
                name="primary_owner"
                control={control}
                placeholder="Enter Primary Owner  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Ownership</InputLabel>
              <CustomNameField
                name="owner_ship"
                control={control}
                placeholder="Enter Ownership  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor Lead</InputLabel>
              <CustomNameField
                name="vendor_lead"
                control={control}
                placeholder="Enter Vendor Lead"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Technologies </InputLabel>
              <CustomMultiTagInput
                name="technologies"
                control={control}
                placeholder="Enter Technologies "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>About Vendor </InputLabel>
              <CustomNameField
                name="vendor"
                control={control}
                placeholder="Enter About Vendor "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor Type </InputLabel>
              <CustomDropdownField
                name="vendor_type"
                control={control}
                placeholder="select Vendor Type "
                options={[{ value: 'RPO' }, { value: 'Contract/Subcon' }, { value: 'Full Time / Lateral' }]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
    </form>
  );
};

export default AddBusinessInfo;
