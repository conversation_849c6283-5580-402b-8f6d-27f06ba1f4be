import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
// import InputLabel from 'components/custom-components/InputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomAddressField from 'components/custom-components/CustomAddressField';

const AddNotes = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue } = useForm();
  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title="Notes"
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
      >
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Action </InputLabel>
              <CustomDropdownField
                name="action "
                control={control}
                placeholder="Select Action"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Notes Priority </InputLabel>
              <CustomDropdownField
                name="priority "
                control={control}
                placeholder="Select Priority"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>New Note </InputLabel>
              <CustomNameField
                name="new_note"
                control={control}
                placeholder="Enter Note  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Notify these people</InputLabel>
              <CustomDropdownField name="notify" control={control} placeholder="" sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <InputLabel>Note</InputLabel>
              <CustomAddressField
                name="note"
                control={control}
                placeholder="Enter Note"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Remind Me</InputLabel>
              <CustomNameField
                name="remind_me"
                control={control}
                placeholder="Remind Me "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </MainCard>
    </form>
  );
};

export default AddNotes;
