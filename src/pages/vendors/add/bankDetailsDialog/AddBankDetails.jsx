import React, { useState, useEffect, useMemo } from 'react';
import { Text<PERSON>ield, Button, Box, Grid, Typography, Divider, InputLabel, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import Stack from '@mui/material/Stack';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import { useTheme } from '@mui/styles';
import AddBankDetailsDialog from './AddBankDetailsDialog';
import EditBankDetailsDialog from './EditBankDetailsDialog';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import { width } from '@mui/system';

const AddBankDetails = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue, getValues } = useForm();
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [showDataGrid, setShowDataGrid] = useState(false);
  const [selectedBankDetails, setSelectedBankDetails] = useState(null);
  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    setFormData(getValues());
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };
  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const handleDialogSave = () => {
    const formData = getValues();
    const newBankDetails = {
      id: expenses.length + 1,
      beneficiaryAccount: formData.beneficiary,
      bankName: formData.bank,
      accountType: formData.account_type,
      ifscCode: formData.ifsc_code,
      accountNumber: formData.account_number,
      depositAmount: formData.deposit_amount,
      priority: formData.priority,
      state: formData.state,
      country: formData.country,
      city: formData.city,
      pincode: formData.pin_code,
      recordStatus: formData.record_status,
      comments: formData.comments,
      address1: formData.address_1,
      address2: formData.address_2
    };
    setExpenses([...expenses, newBankDetails]);
    setOpenDialog(false);
    setShowDataGrid(true);
  };

  const handleEditSave = () => {
    const formData = getValues();
    const updatedBankDetails = {
      id: selectedBankDetails.id,
      beneficiaryAccount: formData.beneficiary,
      bankName: formData.bank,
      accountType: formData.account_type,
      ifscCode: formData.ifsc_code,
      accountNumber: formData.account_number,
      depositAmount: formData.deposit_amount,
      priority: formData.priority,
      state: formData.state,
      country: formData.country,
      city: formData.city,
      pincode: formData.pin_code,
      recordStatus: formData.record_status,
      comments: formData.comments,
      address1: formData.address_1,
      address2: formData.address_2
    };
    const updatedList = expenses.map(bank => 
      bank.id === selectedBankDetails.id 
        ? updatedBankDetails
        : bank
    );
    setExpenses(updatedList);
    setOpenEditDialog(false);
    setSelectedBankDetails(null);
  };

  const [expenses, setExpenses] = useState([]);

  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [sorting, setSorting] = useState([]);

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'beneficiaryAccount',
      headerName: 'Beneficiary Account',
      flex: 1,
      minWidth: 190,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'bankName',
      headerName: 'Bank Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'accountType',
      headerName: 'Account Type',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'accountNumber',
      headerName: 'Account Number',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'state',
      headerName: 'State',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'country',
      headerName: 'Country',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          setSelectedBankDetails(params.row);
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title={
          <>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h5">Bank Account Details</Typography>

              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                  Add
                </Button>
              </Box>
            </Box>
          </>
        }
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
      >
        {showDataGrid && (
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={expenses}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={(model) => setPaginationModel(model)}
              rowCount={expenses.length}
            />
          </Box>
        )}
        <AddBankDetailsDialog
          open={openDialog}
          onClose={handleDialogClose}
          onSave={handleDialogSave}
          control={control}
        />
        <EditBankDetailsDialog
          open={openEditDialog}
          onClose={handleEditDialogClose}
          onSave={handleEditSave}
          control={control}
          bankDetails={selectedBankDetails}
        />
      </MainCard>
    </form>
  );
};

export default AddBankDetails;
