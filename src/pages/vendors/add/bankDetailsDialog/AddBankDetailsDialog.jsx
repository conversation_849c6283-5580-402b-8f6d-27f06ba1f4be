import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import ApplicantDropzonePage from 'pages/jobrequest/UploadDocument';

const AddBankDetailsDialog = ({ open, onClose, onSave, control }) => {
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Add Bank Details</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Beneficiary Name</InputLabel>
              <CustomNameField
                name="beneficiary"
                control={control}
                placeholder="Enter Beneficiary Name"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Bank Name</InputLabel>
              <CustomNameField
                name="bank"
                control={control}
                placeholder="Enter Bank Name"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Account Type</InputLabel>
              <CustomDropdownField
                name="account_type"
                control={control}
                placeholder="Select Account Type"
                options={[
                  { value: 'Savings', label: 'Savings' },
                  { value: 'Current', label: 'Current' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>IFSC Code</InputLabel>
              <CustomNameField
                name="ifsc_code"
                control={control}
                placeholder="Enter IFSC Code"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Account Number</InputLabel>
              <CustomNameField
                name="account_number"
                control={control}
                placeholder="Enter Account Number"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Deposit Amount</InputLabel>
              <CustomDropdownField
                name="deposit_amount"
                control={control}
                placeholder="Select Deposit Amount"
                options={[
                  { value: '1000', label: '1000' },
                  { value: '5000', label: '5000' },
                  { value: '10000', label: '10000' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Priority</InputLabel>
              <CustomDropdownField
                name="priority"
                control={control}
                placeholder="Select Priority"
                options={[
                  { value: 'High', label: 'High' },
                  { value: 'Medium', label: 'Medium' },
                  { value: 'Low', label: 'Low' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Country</InputLabel>
              <CustomDropdownField
                name="country"
                control={control}
                placeholder="Select Country"
                options={[
                  { value: 'India', label: 'India' },
                  { value: 'USA', label: 'USA' },
                  { value: 'UK', label: 'UK' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>State</InputLabel>
              <CustomDropdownField
                name="state"
                control={control}
                placeholder="Select State"
                options={[
                  { value: 'Maharashtra', label: 'Maharashtra' },
                  { value: 'Karnataka', label: 'Karnataka' },
                  { value: 'Tamil Nadu', label: 'Tamil Nadu' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>City</InputLabel>
              <CustomNameField
                name="city"
                control={control}
                placeholder="Enter City"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Pin Code</InputLabel>
              <CustomPostalCodeField
                name="pin_code"
                control={control}
                placeholder="Enter Pin Code"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Record Status</InputLabel>
              <CustomDropdownField
                name="record_status"
                control={control}
                placeholder="Select Record Status"
                options={[
                  { value: 'Active', label: 'Active' },
                  { value: 'Inactive', label: 'Inactive' }
                ]}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Comments</InputLabel>
              <CustomAddressField
                name="comments"
                control={control}
                placeholder="Enter Comments"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 1</InputLabel>
              <CustomAddressField
                name="address_1"
                control={control}
                placeholder="Enter Address 1"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 2</InputLabel>
              <CustomAddressField
                name="address_2"
                control={control}
                placeholder="Enter Address 2"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Attachment</Typography>
              <ApplicantDropzonePage
                name="document"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Cancelled cheque</Typography>
              <ApplicantDropzonePage
                name="cancelled_cheque"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button onClick={onSave} size="small" color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBankDetailsDialog;
