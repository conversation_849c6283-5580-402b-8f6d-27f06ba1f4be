import React, { useState, useMemo } from 'react';
import { Box, Grid, Typography, Button, IconButton, Menu, MenuItem, Tooltip } from '@mui/material';
import Stack from '@mui/material/Stack';
import MainCard from 'components/MainCard';
import CustomGrid from 'components/custom-components/CustomTableWOSerach';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import { useTheme } from '@mui/material/styles';
import { useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import AddContactDialog from './AddContactDialog';
import EditContactDialog from './EditContactDialog';
import { useForm } from 'react-hook-form';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';

const ContactTableGrid = ({ showFields, handleAddClick }) => {
  const { handleSubmit, control, setValue, getValues } = useForm();
  const theme = useTheme();
  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [showDataGrid, setShowDataGrid] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };
  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const [expenses, setExpenses] = useState([]);

  const handleDialogSave = () => {
    const formData = getValues();
    const newContact = {
      id: expenses.length + 1,
      firstName: formData.first_name,
      lastName: formData.last_name,
      description: formData.designation,
      workPhone: formData.work_phone,
      mobileNumber: formData.mobile_number
    };
    setExpenses([...expenses, newContact]);
    setOpenDialog(false);
    setShowDataGrid(true);
  };

  const handleEditSave = () => {
    const formData = getValues();
    const updatedContact = {
      id: selectedContact.id,
      firstName: formData.first_name,
      lastName: formData.last_name,
      description: formData.designation,
      workPhone: formData.work_phone,
      mobileNumber: formData.mobile_number
    };
    const updatedList = expenses.map(contact => 
      contact.id === selectedContact.id 
        ? updatedContact
        : contact
    );
    setExpenses(updatedList);
    setOpenEditDialog(false);
    setSelectedContact(null);
  };

  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [sorting, setSorting] = useState([]);

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'firstName',
      headerName: 'First Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'lastName',
      headerName: 'Last Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'workPhone',
      headerName: 'Work Phone',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'mobileNumber',
      headerName: 'Mobile Number',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <MainCard
          title={
            <>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h5">Contact</Typography>

                <Box display="flex" alignItems="center" gap={1}>
                  <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                    Add
                  </Button>
                </Box>
              </Box>
            </>
          }
          sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
        >
          {showDataGrid && (
            <Box sx={{ height: 300, width: '100%' }}>
              <CustomDataGrid
                rows={expenses}
                columns={columns}
                paginationModel={paginationModel}
                onPaginationModelChange={(model) => setPaginationModel(model)}
                rowCount={expenses.length}
              />
            </Box>
          )}
        </MainCard>
      </form>

      <AddContactDialog open={openDialog} onClose={handleDialogClose} onSave={handleDialogSave} control={control} />
      <EditContactDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleEditSave} control={control} contact={selectedContact} />
    </>
  );
};

export default ContactTableGrid;
