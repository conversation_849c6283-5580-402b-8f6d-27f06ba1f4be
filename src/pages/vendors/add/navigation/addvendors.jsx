import { useState, useEffect } from 'react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import {
  List,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Box,
  Grid,
  Typography,
  Button,
  Snackbar,
  Alert,
  CardActions,
  Stack,
  Tabs,
  Tab,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

// Icons
import { Profile } from 'iconsax-react';
import ContactsIcon from '@mui/icons-material/Contacts';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import LanguageIcon from '@mui/icons-material/Language';
import BadgeIcon from '@mui/icons-material/Badge';

// Components
import MainCard from 'components/MainCard';
import AddBusinessInfo from '../AddBusinessInfo';
import AddAccount from '../AddAccount';
import AddNotes from '../AddNotes';
import AddContacts from '../contactDialog/AddContacts';
import AddSubmissionFormat from '../AddSubmissionFormat';
import AddDocument from '../uploadDocumentDialog/AddDocument';
import AddBankDetails from '../bankDetailsDialog/AddBankDetails';

const tabLabels = [
  { label: 'Business Information', icon: <Profile size={18} /> },
  { label: 'Accounts', icon: <ContactsIcon /> },
  { label: 'Notes', icon: <WorkIcon /> },
  { label: 'Contacts', icon: <SchoolIcon /> },
  // FOr V2
  // { label: 'Vendor Submission Format', icon: <FiberManualRecordIcon /> },
  { label: 'Documents', icon: <LanguageIcon /> },
  { label: 'Bank Account Details', icon: <BadgeIcon /> }
];

export default function AddVendors() {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  console.log('isSmallScreen:', isSmallScreen);

  const [selectedTab, setSelectedTab] = useState(0);
  const [loading, setLoading] = useState(true);

  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  const [validateTabForm, setValidateTabForm] = useState(null); // For per-tab validation
  const [submitForm, setSubmitForm] = useState(false); // Trigger submit in tab component

  const handleSnackbarClose = () => setOpenSnackbar(false);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 300);
    return () => clearTimeout(timer);
  }, []);

  const handleListItemClick = async (index) => {
    if (selectedTab === 0 && validateTabForm) {
      const isValid = await validateTabForm();
      if (!isValid) {
        setSnackbarMessage('Please fix errors before proceeding.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
        return;
      }
    }
    setSelectedTab(index);
  };

  const handleTabChange = async (event, newValue) => {
    if (selectedTab === 0 && validateTabForm) {
      const isValid = await validateTabForm();
      if (!isValid) {
        setSnackbarMessage('Please fix errors before proceeding.');
        setSnackbarSeverity('error');
        setOpenSnackbar(true);
        return;
      }
    }
    setSelectedTab(newValue);
  };

  const handleFormSubmit = async () => {
    try {
      if (validateTabForm) {
        const isValid = await validateTabForm();
        if (!isValid) {
          setSnackbarMessage('Please fix form errors before submitting.');
          setSnackbarSeverity('error');
          setOpenSnackbar(true);
          return;
        }
      }

      setSubmitForm(true); // Triggers actual submission inside child
    } catch (error) {
      const errorMessage = error.message || '';
      if (errorMessage.toLowerCase().includes('client name already exists')) {
        setSnackbarMessage('Vendor name already exists! Please use a unique name.');
      } else {
        setSnackbarMessage('Failed to submit form.');
      }
      setSnackbarSeverity('error');
      setOpenSnackbar(true);
    }
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <AddBusinessInfo />;
      case 1:
        return <AddAccount />;
      case 2:
        return <AddNotes />;
      case 3:
        return <AddContacts />;
      // For V2
      // case 4:
      //   return <AddSubmissionFormat />;
      case 4:
        return <AddDocument />;
      case 5:
        return <AddBankDetails />;
      default:
        return null;
    }
  };

  return (
    <>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }} />
      ) : (
        <>
          {/* Sticky Header Actions */}
          <CardActions
            sx={{
              position: 'sticky',
              top: 0,
              bgcolor: 'background.default',
              zIndex: 1100,
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                Add Vendor Information
              </Typography>
              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                <Button variant="outlined" size="small" component={Link} to="/vendor">
                  Cancel
                </Button>
                <Button variant="contained" size="small" onClick={handleFormSubmit}>
                  Submit
                </Button>
              </Stack>
            </Stack>
          </CardActions>

          <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
            {isSmallScreen ? (
              <>
                <Box
                  sx={{
                    bgcolor: 'background.default',
                    position: 'sticky',
                    top: 60,
                    alignSelf: 'flex-start',
                    zIndex: 80
                  }}
                >
                  <Tabs
                    value={selectedTab}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    aria-label="responsive tabs"
                  >
                    {tabLabels.map((tab, index) => (
                      <Tab key={index} label={tab.label} icon={tab.icon} iconPosition="start" />
                    ))}
                  </Tabs>
                </Box>
                <Box sx={{ mt: 2 }}>{renderTabContent()}</Box>
              </>
            ) : (
              <Grid container spacing={3}>
                {/* Left-side Tab List */}
                <Grid
                  item
                  xs={12}
                  sm={4}
                  md={3}
                  lg={3}
                  xl={3}
                  sx={{
                    position: 'sticky',
                    top: 60,
                    alignSelf: 'flex-start',
                    zIndex: 80
                  }}
                >
                  <MainCard sx={{ borderRadius: '2%' }}>
                    <List component="nav">
                      {tabLabels.map((tab, index) => (
                        <ListItemButton key={index} selected={selectedTab === index} onClick={() => handleListItemClick(index)}>
                          <ListItemIcon>{tab.icon}</ListItemIcon>
                          <ListItemText primary={tab.label} />
                        </ListItemButton>
                      ))}
                    </List>
                  </MainCard>
                </Grid>

                <Grid item xs={12} sm={8} md={9}>
                  {renderTabContent()}
                </Grid>
              </Grid>
            )}
          </MainCard>

          <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
            <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
              {snackbarMessage}
            </Alert>
          </Snackbar>
        </>
      )}
    </>
  );
}
