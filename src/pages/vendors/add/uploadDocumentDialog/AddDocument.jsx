import React, { useState } from 'react';
import { TextField, Button, Box, Typography, InputLabel, Tooltip, Menu, MenuItem, IconButton } from '@mui/material';
import { useForm } from 'react-hook-form';
import MainCard from 'components/MainCard';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AddDocumentDialog from './AddDocumentDialog';
import EditDocumentDialog from './EditDocumentDialog';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';

const AddDocument = () => {
  const { handleSubmit, control, getValues } = useForm();
  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [showDataGrid, setShowDataGrid] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [expenses, setExpenses] = useState([]);

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };
  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const handleDialogSave = () => {
    const formData = getValues();
    const newDocument = {
      id: expenses.length + 1,
      documentType: formData.document_type,
      documentTitle: formData.document_title,
      uploadedDocument: formData.document?.name || 'No file uploaded',
      description: formData.description
    };
    setExpenses([...expenses, newDocument]);
    setOpenDialog(false);
    setShowDataGrid(true);
  };

  const handleEditSave = () => {
    const formData = getValues();
    const updatedDocument = {
      id: selectedDocument.id,
      documentType: formData.document_type,
      documentTitle: formData.document_title,
      uploadedDocument: formData.document?.name || 'No file uploaded',
      description: formData.description
    };
    const updatedList = expenses.map(doc => 
      doc.id === selectedDocument.id 
        ? updatedDocument
        : doc
    );
    setExpenses(updatedList);
    setOpenEditDialog(false);
    setSelectedDocument(null);
  };

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'documentType',
      headerName: 'Document Type',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'documentTitle',
      headerName: 'Document Title',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'uploadedDocument',
      headerName: 'Uploaded Document',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2" color="blue">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          setSelectedDocument(params.row);
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <MainCard
      title={
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Upload Document</Typography>

            <Box display="flex" alignItems="center" gap={1}>
              <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                Add
              </Button>
            </Box>
          </Box>
        </>
      }
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      {showDataGrid && (
        <Box sx={{ height: 300, width: '100%' }}>
          <CustomDataGrid
            rows={expenses}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(model) => setPaginationModel(model)}
            rowCount={expenses.length}
          />
        </Box>
      )}
      <AddDocumentDialog open={openDialog} onClose={handleDialogClose} onSave={handleDialogSave} control={control} />
      <EditDocumentDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleEditSave} control={control} document={selectedDocument} />
    </MainCard>
  );
};

export default AddDocument;
