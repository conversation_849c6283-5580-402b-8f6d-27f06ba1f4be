import React from 'react';
import { Dialog, DialogTitle, DialogContent, <PERSON>alogActions, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import ApplicantDropzonePage from 'pages/jobrequest/UploadDocument';

const AddDocumentDialog = ({ open, onClose, onSave, control }) => {
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Add Document</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Document Type </InputLabel>
              <CustomDropdownField
                name="document_type"
                control={control}
                placeholder="Select Document Type  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                options={[{ value: 'PDF' }]}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Document Title</InputLabel>
              <CustomNameField
                name="document_title"
                control={control}
                placeholder="Enter Document Title"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Upload Document</Typography>
              <ApplicantDropzonePage
                name="document"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <Stack spacing={1}>
              <InputLabel>Description </InputLabel>
              <CustomAddressField
                name="description"
                control={control}
                placeholder="Enter Description "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button onClick={onSave} size="small" color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddDocumentDialog;
