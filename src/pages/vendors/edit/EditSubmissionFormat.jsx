import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ield, Button, Box, Grid, Typography, Divider, Stack, InputLabel } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField ';
import CustomNameField from 'components/custom-components/CustomNameField';
import ReactDraft from 'pages/client-page/component/ReactDraft';

const EditSubmissionFormat = () => {
  const { control } = useForm();
  return (
    <MainCard
      title="Vendor Submission Format"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
          <Stack spacing={1}>
            <InputLabel>Vendor</InputLabel>
            <CustomDropdownField
              name="vendor"
              control={control}
              placeholder="Select Vendor Contact Type"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>
        <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
          <Stack spacing={1}>
            <InputLabel>Vendor Lead</InputLabel>
            <CustomDropdownField
              name="vendor_lead"
              control={control}
              placeholder="Select Vendor Lead"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>

        <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
          <Stack spacing={1}>
            <InputLabel>Subject</InputLabel>
            <CustomDropdownField
              name="subject"
              control={control}
              placeholder="Select Subject"
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>

        <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
          <Stack spacing={1}>
            <InputLabel>Mailing Subject </InputLabel>
            <CustomNameField
              name="subjet_mail"
              control={control}
              placeholder="Enter First Name "
              sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
            />
          </Stack>
        </Grid>

        <Grid item xs={12} sm={12} xl={12} lg={10} md={12}>
          <Stack spacing={2}>
            <InputLabel htmlFor="body">Body</InputLabel>
            <ReactDraft />
          </Stack>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default EditSubmissionFormat;
