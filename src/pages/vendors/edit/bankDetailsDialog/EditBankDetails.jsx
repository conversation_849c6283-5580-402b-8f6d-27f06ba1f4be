import React, { useState, useEffect, useMemo } from 'react';
import { TextField, Button, Box, Grid, Typography, Divider, InputLabel, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useTheme } from '@mui/styles';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AddBankDetailsDialog from './AddBankDetailsDialog';
import EditBankDetailsDialog from './EditBankDetailsDialog';

const EditBankDetails = () => {
  const API_URL = import.meta.env.VITE_APP_API_URL;
  const { handleSubmit, control, setValue } = useForm();
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('');
  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));

  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  const handleStateChange = (selectedValue) => {
    const selectedStateObj = states.find((state) => state.name === selectedValue);
    if (selectedStateObj) {
      setValue('state', selectedStateObj?.name);
    }
  };
  const handleInputChange = () => {
    setFormData(getValues());
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };
  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const handleDialogSave = () => {
    // Add save logic here
    setOpenDialog(false);
  };
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      beneficiaryAccount: '************',
      bankName: 'State Bank of India',
      accountType: 'Savings',
      state: 'Maharashtra',
      country: 'India',
      city: 'Mumbai',
      pincode: '400001'
    },
    {
      id: 2,
      beneficiaryAccount: '************',
      bankName: 'HDFC Bank',
      accountType: 'Current',
      state: 'Karnataka',
      country: 'India',
      city: 'Bangalore',
      pincode: '560001'
    },
    {
      id: 3,
      beneficiaryAccount: '************',
      bankName: 'ICICI Bank',
      accountType: 'Savings',
      state: 'Tamil Nadu',
      country: 'India',
      city: 'Chennai',
      pincode: '600001'
    }
  ]);

  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [sorting, setSorting] = useState([]);

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'beneficiaryAccount',
      headerName: 'Beneficiary Account',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'bankName',
      headerName: 'Bank Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'accountType',
      headerName: 'Account Type',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'state',
      headerName: 'State',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'country',
      headerName: 'Country',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'city',
      headerName: 'City',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    // {
    //   field: 'pincode',
    //   headerName: 'Pincode',
    //   flex: 1,
    //   minWidth: 140,
    //   renderCell: (params) => (
    //     <Tooltip title={params.value} placement="bottom">
    //       <Typography variant="body2">{params.value}</Typography>
    //     </Tooltip>
    //   )
    // },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard
        title={
          <>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="h5">Bank Account Details</Typography>

              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                  Add
                </Button>
              </Box>
            </Box>
          </>
        }
        sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
      >
        <Box sx={{ height: 300, width: '100%' }}>
          <CustomDataGrid
            rows={expenses}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(model) => setPaginationModel(model)}
            rowCount={expenses.length}
          />
        </Box>
        <AddBankDetailsDialog
          open={openDialog}
          onClose={handleDialogClose}
          onSave={handleDialogSave}
          control={control}
          handleInputChange={handleInputChange}
        />
        <EditBankDetailsDialog
          open={openEditDialog}
          onClose={handleEditDialogClose}
          onSave={handleDialogSave}
          control={control}
          handleInputChange={handleInputChange}
        />
      </MainCard>
    </form>
  );
};

export default EditBankDetails;
