import React, { useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import ApplicantDropzonePage from 'pages/jobrequest/UploadDocument';

const EditBankDetailsDialog = ({ open, onClose, onSave, control, handleInputChange }) => {
  const [countries, setCountries] = useState([]);
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const [states, setStates] = useState([]);
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));
  const [selectedCountry, setSelectedCountry] = useState('');
  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Edit Bank Details</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Beneficiary Name</InputLabel>
              <CustomNameField
                name="beneficiary "
                control={control}
                placeholder="Enter Beneficiary Name"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Bank Name</InputLabel>
              <CustomNameField
                name="bank "
                control={control}
                placeholder="Enter Bank Name"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel> Account Type</InputLabel>
              <CustomDropdownField
                name="account_type"
                control={control}
                placeholder="Select Account Type"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>IFSC Code </InputLabel>
              <CustomNameField
                name="ifsc_code"
                control={control}
                placeholder="Enter IFSC Code"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Account Number</InputLabel>
              <CustomNameField
                name="account_number"
                control={control}
                placeholder="Enter Account Number"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Deposit Amount</InputLabel>
              <CustomDropdownField
                name="deposit_amount"
                control={control}
                placeholder="Select Deposit Amount"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Priority</InputLabel>
              <CustomDropdownField
                name="priority"
                control={control}
                placeholder="Select Priority"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="Country">
                Country <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="country"
                control={control}
                placeholder="Select Country"
                options={countriesOptions}
                onBlur={handleInputChange}
                rules={{ required: 'Country is required' }}
                onChange={(e) => handleCountryChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="state">
                State <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="state"
                control={control}
                placeholder="Select State"
                options={statesOptions}
                onBlur={handleInputChange}
                rules={{ required: 'State is required' }}
                onChange={(e) => handleStateChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>City </InputLabel>
              <CustomNameField
                name="city "
                control={control}
                placeholder="Enter City "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Pin Code </InputLabel>
              <CustomPostalCodeField
                name="pin_code "
                control={control}
                placeholder="Enter Pin Code  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Record Status</InputLabel>
              <CustomDropdownField
                name="record_status"
                control={control}
                placeholder="Select Record Status"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Comments</InputLabel>
              <CustomAddressField
                name="comments"
                control={control}
                placeholder="Enter Comments"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 1 </InputLabel>
              <CustomAddressField
                name="address_1"
                control={control}
                placeholder="Enter Address 1 "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 2 </InputLabel>
              <CustomAddressField
                name="address_2"
                control={control}
                placeholder="Enter Address 2 "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Attachment</Typography>
              <ApplicantDropzonePage
                name="document"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              ></ApplicantDropzonePage>
            </Stack>
          </Grid>{' '}
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Cancelled cheque</Typography>
              <ApplicantDropzonePage
                name="document"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              ></ApplicantDropzonePage>
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button onClick={onSave} size="small" color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditBankDetailsDialog;
