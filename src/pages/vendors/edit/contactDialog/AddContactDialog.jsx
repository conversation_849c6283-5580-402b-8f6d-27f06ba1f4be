import React, { useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import CustomMultiTagInput from 'components/custom-components/CustomMultiTagInput';

const AddContactDialog = ({ open, onClose, onSave, control }) => {
  const [countries, setCountries] = useState([]);
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const [states, setStates] = useState([]);
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));
  const [selectedCountry, setSelectedCountry] = useState('');
  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Add Contact</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>First Name </InputLabel>
              <CustomNameField
                name="first_name"
                control={control}
                placeholder="Enter First Name "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Last Name </InputLabel>
              <CustomNameField
                name="last_name"
                control={control}
                placeholder="Enter Last Name  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Designation</InputLabel>
              <CustomNameField
                name="designation"
                control={control}
                placeholder="Enter Designation"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Work Phone</InputLabel>
              <CustomContactNumberField
                name="work_phone"
                control={control}
                placeholder="Enter Work Phone"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Mobile Number </InputLabel>
              <CustomContactNumberField
                name="mobile_number"
                control={control}
                placeholder="Enter Mobile Number "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Email </InputLabel>
              <CustomEmailField
                name="degree "
                control={control}
                placeholder="Enter Email  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Primary Owner </InputLabel>
              <CustomNameField
                name="primary_owner"
                control={control}
                placeholder="Enter Primary Owner "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Ownership </InputLabel>
              <CustomNameField
                name="ownership"
                control={control}
                placeholder="Enter Ownership  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 1 </InputLabel>
              <CustomAddressField
                name="address_1"
                control={control}
                placeholder="Enter Address 1 "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Address 2 </InputLabel>
              <CustomAddressField
                name="address_2"
                control={control}
                placeholder="Enter Address 2 "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                Country <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="country"
                control={control}
                placeholder="Select Country"
                options={countriesOptions}
                rules={{ required: 'Country is required' }}
                onChange={(e) => handleCountryChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>
                State <span style={{ color: 'red' }}>*</span>
              </InputLabel>
              <CustomDropdownField
                name="state"
                control={control}
                placeholder="Select State"
                options={statesOptions}
                rules={{ required: 'State is required' }}
                onChange={(e) => handleStateChange(e.target.value)}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>City </InputLabel>
              <CustomNameField name="city" control={control} placeholder="Enter City " sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }} />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Pin Code</InputLabel>
              <CustomPostalCodeField
                name="pin_code"
                control={control}
                placeholder="Enter Pin Code"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>LinkedIn ProfileUrl </InputLabel>
              <CustomNameField
                name="linked_in"
                control={control}
                placeholder="Enter LinkedIn ProfileUrl"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Facebook ProfileUrl</InputLabel>
              <CustomNameField
                name="facebook"
                control={control}
                placeholder="Enter Facebook ProfileUrl"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Twitter ProfileUrl</InputLabel>
              <CustomNameField
                name="twitter"
                control={control}
                placeholder="Enter Twitter ProfileUrl"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Google ProfileUrl</InputLabel>
              <CustomNameField
                name="google"
                control={control}
                placeholder="Enter Google ProfileUrl"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Status </InputLabel>
              <CustomDropdownField
                name="status"
                control={control}
                placeholder="Select Status"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                options={[{ value: 'Active' }, { value: 'In Active' }, { value: 'On Hold' }]}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Technologies</InputLabel>
              <CustomMultiTagInput
                name="technologies"
                control={control}
                placeholder="Enter City Technologies"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor Group </InputLabel>
              <CustomNameField
                name="vendor_grp"
                control={control}
                placeholder="Enter Vendor Group "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Vendor Contact Type</InputLabel>
              <CustomDropdownField
                name="vendor_contact"
                control={control}
                placeholder="Select Vendor Contact Type"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Send Requirement</InputLabel>
              <CustomNameField
                name="send_requirement"
                control={control}
                placeholder="Checkbox "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Send HotList</InputLabel>
              <CustomNameField
                name="send_hotlist"
                control={control}
                placeholder="Checkbox "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12}>
            <Stack spacing={1}>
              <InputLabel>Comment </InputLabel>
              <CustomAddressField
                name="Comment "
                control={control}
                placeholder="Enter Comment  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button onClick={onSave} size="small" color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddContactDialog;
