import React, { useState } from 'react';
import { Box, Typography, Button, IconButton, Menu, MenuItem, Tooltip } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm } from 'react-hook-form';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AddContactDialog from './AddContactDialog';
import EditContactDialog from './EditContactDialog';

const ContactTableGrid = () => {
  const { handleSubmit, control } = useForm();
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };

  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const handleDialogSave = () => {
    setOpenDialog(false);
  };

  const handleMenuClick = (event) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleEdit = () => {
    handleEditDialogOpen();
    handleMenuClose();
  };

  // Sample data for the table
  const [expenses] = useState([
    {
      id: 1,
      firstName: 'Alice',
      lastName: 'Johnson',
      description: 'Project Manager for Team A',
      workPhone: '************',
      mobileNumber: '************'
    },
    {
      id: 2,
      firstName: 'Bob',
      lastName: 'Smith',
      description: 'Senior Developer',
      workPhone: '************',
      mobileNumber: '************'
    },
    {
      id: 3,
      firstName: 'Carol',
      lastName: 'Davis',
      description: 'HR Specialist',
      workPhone: '************',
      mobileNumber: '************'
    }
  ]);

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'firstName',
      headerName: 'First Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'lastName',
      headerName: 'Last Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'workPhone',
      headerName: 'Work Phone',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'mobileNumber',
      headerName: 'Mobile Number',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: () => (
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
      )
    }
  ];

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <MainCard
          title={
            <>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h5">Contact</Typography>

                <Box display="flex" alignItems="center" gap={1}>
                  <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                    Add
                  </Button>
                </Box>
              </Box>
            </>
          }
          sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
        >
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={expenses}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={(model) => setPaginationModel(model)}
              rowCount={expenses.length}
            />
          </Box>
        </MainCard>
      </form>

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem onClick={handleEdit}>Edit</MenuItem>
      </Menu>

      <AddContactDialog open={openDialog} onClose={handleDialogClose} onSave={handleDialogSave} control={control} />
      <EditContactDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleDialogSave} control={control} />
    </>
  );
};

export default ContactTableGrid;
