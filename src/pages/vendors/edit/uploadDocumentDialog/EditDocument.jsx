import React, { useState, useEffect, useMemo } from 'react';
import { Text<PERSON>ield, Button, Box, Grid, Typography, Divider, InputLabel, Tooltip, Menu, MenuItem, IconButton } from '@mui/material';
import { useForm, Controller, useWatch } from 'react-hook-form';
import MainCard from 'components/MainCard';
import { useTheme } from '@mui/material/styles';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AddDocumentDialog from './AddDocumentDialog';
import EditDocumentDialog from './EditDocumentDialog';

const EditDocument = () => {
  const { handleSubmit, control, setValue } = useForm();
  const onSubmit = (data) => {
    console.log('Form Data:', data);
    // You can perform further actions here, such as sending the data to an API
  };
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleDialogOpen = () => {
    setOpenDialog(true);
  };
  const handleEditDialogOpen = () => {
    setOpenEditDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
  };

  const handleDialogSave = () => {
    // Add save logic here
    setOpenDialog(false);
  };
  const [expenses, setExpenses] = useState([
    {
      id: 1,
      documentType: 'Invoice',
      documentTitle: 'March 2025 Invoice',
      uploadedDocument: 'invoice_march2025.pdf',
      description: 'Monthly invoice for services rendered'
    },
    {
      id: 2,
      documentType: 'Contract',
      documentTitle: 'Vendor Agreement',
      uploadedDocument: 'vendor_contract.pdf',
      description: 'Agreement with ABC Vendors for 2025'
    },
    {
      id: 3,
      documentType: 'Report',
      documentTitle: 'Q1 Financials',
      uploadedDocument: 'q1_financials.pdf',
      description: 'Quarterly financial report'
    }
  ]);

  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [sorting, setSorting] = useState([]);

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'documentType',
      headerName: 'Document Type',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'documentTitle',
      headerName: 'Document Title',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'uploadedDocument',
      headerName: 'Uploaded Document',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2" color="blue">
            {params.value}
          </Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <MainCard
      title={
        <>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Upload Document</Typography>

            <Box display="flex" alignItems="center" gap={1}>
              <Button variant="contained" size="small" color="primary" onClick={handleDialogOpen}>
                Add
              </Button>
            </Box>
          </Box>
        </>
      }
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      <Box sx={{ height: 300, width: '100%' }}>
        <CustomDataGrid
          rows={expenses}
          columns={columns}
          paginationModel={paginationModel}
          onPaginationModelChange={(model) => setPaginationModel(model)}
          rowCount={expenses.length}
        />
      </Box>
      <AddDocumentDialog open={openDialog} onClose={handleDialogClose} onSave={handleDialogSave} control={control} />
      <EditDocumentDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleDialogSave} control={control} />
    </MainCard>
  );
};

export default EditDocument;
