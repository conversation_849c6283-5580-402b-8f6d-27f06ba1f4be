import React, { useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Stack, InputLabel, Typography } from '@mui/material';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomAddressField from 'components/custom-components/CustomAddressField';
import CustomPostalCodeField from 'components/custom-components/CustomPostalCode';
import CustomContactNumberField from 'components/custom-components/CustomContactNumberField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import ApplicantDropzonePage from 'pages/jobrequest/UploadDocument';

const EditDocumentDialog = ({ open, onClose, onSave, control }) => {
  const [countries, setCountries] = useState([]);
  const countriesOptions = countries.map((country) => ({ value: country.name, label: country.name }));
  const [states, setStates] = useState([]);
  const statesOptions = states.map((state) => ({ value: state.name, label: state.name }));
  const [selectedCountry, setSelectedCountry] = useState('');
  const handleCountryChange = (selectedValue) => {
    const selectedCountryObj = countries.find((country) => country.name === selectedValue);
    if (selectedCountryObj) {
      setSelectedCountry(selectedCountryObj.id);
      setValue('country', selectedCountryObj?.name);
    }
  };

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await axios.get(`${API_URL}/api/countries`);
        setCountries(response.data);
      } catch (error) {
        console.error('Error fetching countries:', error);
      }
    };
    fetchCountries();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        if (!selectedCountry) return;
        const response = await axios.get(`${API_URL}/api/states/${selectedCountry}`);
        setStates(response.data);
      } catch (error) {
        console.error('Error fetching states:', error);
      }
    };
    fetchStates();
  }, [selectedCountry]);
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>Edit Document</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Document Type </InputLabel>
              <CustomDropdownField
                name="document_type"
                control={control}
                placeholder="Select Document Type  "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={6} xl={6} lg={6} md={6}>
            <Stack spacing={1}>
              <InputLabel>Document Title</InputLabel>
              <CustomNameField
                name="document_title"
                control={control}
                placeholder="Enter Document Title"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <Typography color="secondary">Upload Document</Typography>
              <ApplicantDropzonePage
                name="document"
                control={control}
                placeholder="Upload Document"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              ></ApplicantDropzonePage>
            </Stack>
          </Grid>
          <Grid item xs={12} sm={12} xl={12} lg={12} md={12}>
            <Stack spacing={1}>
              <InputLabel>Description </InputLabel>
              <CustomAddressField
                name="description"
                control={control}
                placeholder="Enter Description "
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Cancel
        </Button>
        <Button onClick={onSave} size="small" color="primary" variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditDocumentDialog;
