import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import React, { useState, useMemo, useEffect } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/Download';
import Link from '@mui/material/Link';
import Button from '@mui/material/Button';
import MainCard from 'components/MainCard';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { compareItems, rankItem } from '@tanstack/match-sorter-utils';
import { CSVExport, DebouncedInput, IndeterminateCheckbox, SelectColumnVisibility } from 'components/third-party/react-table';
import { useMediaQuery } from '@mui/system';
import Tooltip from '@mui/material/Tooltip';
import Spinner from 'components/custom-components/Spinner';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  flexRender,
  useReactTable,
  sortingFns
} from '@tanstack/react-table';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import TablePagination from 'components/custom-components/CustomPaginationItems';
import RowsPerPageSelector from 'components/custom-components/CustomRowPerPage';
import ColumnVisibilitySelector from 'components/custom-components/CustomColumnVisibilitySelector';
import CustomTableContainer from 'components/custom-components/CustomTableContainer';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import VisibilitySharpIcon from '@mui/icons-material/VisibilitySharp';
import Chip from '@mui/material/Chip';
import { useRBAC } from 'pages/permissions/RBACContext';
import { PERMISSIONS } from 'constants';
import { CircularProgress, Menu, MenuItem, Stack } from '@mui/material';
// import DeleteIcon from '@material-ui/icons/Delete';

export const fuzzyFilter = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta(itemRank);
  return itemRank.passed;
};

function TableBench() {
  const theme = useTheme();
  const navigate = useNavigate();
  const [data, setData] = useState([
    {
      id: 1,
      primary_business_unit: 'IT Services',
      vendor_visibility: true,
      vendor_name: 'Tech Solutions Ltd.',
      federal_id: '123456789',
      website: 'https://www.techsolutions.com',
      contact_number: '*********0',
      fax: '*********1',
      address: '123 Tech Park, Building 5',
      country: 'USA',
      state: 'California',
      city: 'San Francisco',
      pin_code: '94105',
      send_requirements: true,
      send_hotlist: false,
      primary_owner: 'John Smith',
      ownership: 'Private',
      vendor_lead: 'Sarah Johnson',
      technologies: 'JavaScript, React, Node.js',
      about_vendor: 'A leading software development company specializing in web applications.',
      vendor_type: 'Technology'
    },
    {
      id: 2,
      primary_business_unit: 'Consulting',
      vendor_visibility: false,
      vendor_name: 'Global Consults Inc.',
      federal_id: '*********',
      website: 'https://www.globalconsults.com',
      contact_number: '9123456789',
      fax: '9123456790',
      address: '456 Business St, Suite 100',
      country: 'Canada',
      state: 'Ontario',
      city: 'Toronto',
      pin_code: 'M5A 1A1',
      send_requirements: true,
      send_hotlist: true,
      primary_owner: 'Jane Doe',
      ownership: 'Public',
      vendor_lead: 'David White',
      technologies: 'Python, Django, AWS',
      about_vendor: 'Providing business consulting services to enterprises globally.',
      vendor_type: 'Consulting'
    },
    {
      id: 3,
      primary_business_unit: 'Manufacturing',
      vendor_visibility: true,
      vendor_name: 'SteelWorks Ltd.',
      federal_id: '*********',
      website: 'https://www.steelworks.com',
      contact_number: '9345678901',
      fax: '9345678910',
      address: '789 Industrial Ave, Building 2',
      country: 'UK',
      state: 'London',
      city: 'London',
      pin_code: 'EC1A 1BB',
      send_requirements: false,
      send_hotlist: true,
      primary_owner: 'Mike Brown',
      ownership: 'Private',
      vendor_lead: 'Emily Clark',
      technologies: 'CNC Machining, Robotics',
      about_vendor: 'Specialized in manufacturing industrial machinery for the automotive industry.',
      vendor_type: 'Manufacturing'
    },
    {
      id: 4,
      primary_business_unit: 'Healthcare',
      vendor_visibility: true,
      vendor_name: 'HealthTech Solutions',
      federal_id: '**********',
      website: 'https://www.healthtech.com',
      contact_number: '**********',
      fax: '**********',
      address: '101 Health St, Suite 303',
      country: 'Australia',
      state: 'Victoria',
      city: 'Melbourne',
      pin_code: '3000',
      send_requirements: true,
      send_hotlist: false,
      primary_owner: 'Alice White',
      ownership: 'Non-profit',
      vendor_lead: 'Chris Thompson',
      technologies: 'Telemedicine, AI Diagnostics',
      about_vendor: 'Providing telemedicine and healthcare solutions for remote patient monitoring.',
      vendor_type: 'Healthcare'
    },
    {
      id: 5,
      primary_business_unit: 'Finance',
      vendor_visibility: true,
      vendor_name: 'FinServe Ltd.',
      federal_id: '**********',
      website: 'https://www.finserve.com',
      contact_number: '**********',
      fax: '**********',
      address: '202 Finance St, Suite 404',
      country: 'Singapore',
      state: 'Central',
      city: 'Singapore',
      pin_code: '049183',
      send_requirements: false,
      send_hotlist: true,
      primary_owner: 'Bob Green',
      ownership: 'Public',
      vendor_lead: 'Rachel Adams',
      technologies: 'Blockchain, FinTech, Big Data',
      about_vendor: 'Offering blockchain-based financial services and solutions for enterprises.',
      vendor_type: 'Finance'
    }
  ]);

  console.log('data', data);
  const { canMenuPage } = useRBAC();
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnVisibility, setColumnVisibility] = useState({
    primary_business_unit: true,
    vendor_visibility: true,
    vendor_name: true,
    federal_id: true,
    website: true,
    contact_number: true,
    fax: false,
    address: false,
    country: false,
    state: false,
    city: false,
    pin_code: false,
    send_requirements: false,
    send_hotlist: false,
    primary_owner: false,
    ownership: false,
    vendor_lead: false,
    technologies: false,
    about_vendor: false,
    vendor_type: false
  });
  const [sorting, setSorting] = useState([]);
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const columns = useMemo(
    () => [
      {
        id: 'select',
        enableGrouping: false,
        header: ({ table }) => (
          <IndeterminateCheckbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      {
        header: 'Primary Business Unit',
        accessorKey: 'primary_business_unit',
        cell: ({ getValue, row }) => {
          const vendorName = getValue();

          return (
            <Box sx={{
              display: 'flex', 
              alignItems: 'center', 
              height: '100%',
              '& > .client-name-text': {
                fontWeight: 600,
                cursor: 'pointer',
                color: 'text.primary',
                textDecoration: 'none',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }
            }}>
              <Tooltip title={vendorName} placement="bottom">
                <div
                  onClick={() =>
                    navigate('/vendor/view', {
                      state: { row: JSON.parse(JSON.stringify(row)) }
                    })
                  }
                  className="client-name-text"
                >
                  {vendorName}
                </div>
              </Tooltip>
            </Box>
          );
        }
      },
      { header: 'Vendor Visibility', accessorKey: 'vendor_visibility' },
      { header: 'Vendor Name', accessorKey: 'vendor_name' },
      { header: 'Federal ID', accessorKey: 'federal_id' },
      { header: 'Website', accessorKey: 'website' },
      { header: 'Contact Number', accessorKey: 'contact_number' },
      { header: 'Fax', accessorKey: 'fax' },
      { header: 'Address', accessorKey: 'address' },
      { header: 'Country', accessorKey: 'country' },
      { header: 'State', accessorKey: 'state' },
      { header: 'City', accessorKey: 'city' },
      { header: 'Pin Code', accessorKey: 'pin_code' },
      { header: 'Send Requirements', accessorKey: 'send_requirements' },
      { header: 'Send Hotlist', accessorKey: 'send_hotlist' },
      { header: 'Primary Owner', accessorKey: 'primary_owner' },
      { header: 'Ownership', accessorKey: 'ownership' },
      { header: 'Vendor Lead', accessorKey: 'vendor_lead' },
      { header: 'Technologies', accessorKey: 'technologies' },
      { header: 'About Vendor', accessorKey: 'about_vendor' },
      { header: 'Vendor Type', accessorKey: 'vendor_type' },

      {
        header: 'Status',
        accessorKey: 'is_active',
        cell: ({ getValue }) => {
          const isActive = getValue();
          const statusLabel = isActive === true ? 'Active' : isActive === false ? 'Inactive' : 'Unknown';
          const chipColor = isActive === true ? 'success' : isActive === false ? 'error' : 'warning';
          return (
            <Tooltip title={statusLabel} placement="bottom">
              <Typography variant="body2" sx={{ display: 'inline-block' }}>
                <Chip color={chipColor} label={statusLabel} size="small" variant="light" />
              </Typography>
            </Tooltip>
          );
        }
      },
      {
        header: 'Actions',
        cell: ({ row }) => {
          const [anchorEl, setAnchorEl] = useState(null);
          const open = Boolean(anchorEl);
          const navigate = useNavigate();

          const handleMenuClick = (event) => {
            setAnchorEl(event.currentTarget);
          };

          const handleClose = () => {
            setAnchorEl(null);
          };

          const handleView = () => {
            navigate('/vendor/view', { state: { row: JSON.parse(JSON.stringify(row)) } });
            handleClose();
          };

          const handleEdit = () => {
            navigate('/vendor/edit', { state: { row: JSON.parse(JSON.stringify(row)) } });
            handleClose();
          };

          return (
            <Box>
              <Tooltip title="More actions" placement="bottom">
                <IconButton sx={{ padding: 0.2, height: 24, width: 24 }} onClick={handleMenuClick}>
                  <MoreVertIcon />
                </IconButton>
              </Tooltip>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              >
                <MenuItem onClick={handleEdit}>Edit</MenuItem>
              </Menu>
            </Box>
          );
        }
      }
    ],
    [navigate]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection,
      columnFilters,
      globalFilter,
      sorting,
      columnVisibility
    },
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: (value) => {
      console.log('🔍 onGlobalFilterChange triggered:', value);
      setGlobalFilter(value);
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    globalFilterFn: fuzzyFilter,
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData((old) =>
          old.map((row, index) => {
            if (index === rowIndex) {
              return {
                ...old[rowIndex],
                [columnId]: value
              };
            }
            return row;
          })
        );
      }
    }
  });

  let headers = [];
  table.getVisibleLeafColumns().map(
    (columns) =>
      columns.columnDef.accessorKey &&
      headers.push({
        label: typeof columns.columnDef.header === 'string' ? columns.columnDef.header : '#',
        key: columns.columnDef.accessorKey
      })
  );

  const visibleColumnHeaders = useMemo(() => {
    return table
      .getVisibleLeafColumns()
      .filter((col) => !!col.columnDef.accessorKey)
      .map((col) => (typeof col.columnDef.header === 'string' ? col.columnDef.header : ''))
      .filter(Boolean);
  }, [table.getVisibleLeafColumns()]);

  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) => (prevIndex + 1) % visibleColumnHeaders.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [visibleColumnHeaders]);

  const rotatingPlaceholder = visibleColumnHeaders.length > 0 ? `Search by ${visibleColumnHeaders[placeholderIndex]}` : 'Search';

  const secondaryActions2 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'Placement.csv'
        }}
      />

      <IconButton
        onClick={() => navigate('/vendor/add')}
        sx={{
          backgroundColor: 'primary.main',
          width: 60,
          height: 30,
          borderRadius: '6px',
          border: '1px solid grey',
          '&:hover': { backgroundColor: 'secondary.main' },
          '&:focus': { backgroundColor: 'primary.main' }
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'white'
          }}
        >
          + Add
        </Typography>
      </IconButton>
    </Box>
  );
  const secondaryActions3 = (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <ColumnVisibilitySelector table={table} sx={{ padding: '5px' }} />
      <CSVExport
        {...{
          data:
            table.getSelectedRowModel().flatRows.map((row) => row.original).length === 0
              ? data
              : table.getSelectedRowModel().flatRows.map((row) => row.original),
          headers,
          filename: 'Placement.csv'
        }}
      />

      <IconButton
        onClick={() => navigate('/vendor/add')}
        sx={{
          backgroundColor: 'primary.main',
          width: 60,
          height: 30,
          borderRadius: '6px',
          border: '1px solid grey',
          '&:hover': { backgroundColor: 'secondary.main' },
          '&:focus': { backgroundColor: 'primary.main' }
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: 'white'
          }}
        >
          + Add
        </Typography>
      </IconButton>
    </Box>
  );

  const paginationbutton = (
    <TablePagination
      {...{
        setPageSize: table.setPageSize,
        setPageIndex: table.setPageIndex,
        getState: table.getState,
        getPageCount: table.getPageCount
      }}
    />
  );

  const rowbutton = (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <RowsPerPageSelector
        {...{
          getState: table.getState,
          setPageSize: table.setPageSize
        }}
      />
      <TablePagination
        {...{
          setPageSize: table.setPageSize,
          setPageIndex: table.setPageIndex,
          getState: table.getState,
          getPageCount: table.getPageCount
        }}
      />
    </Box>
  );

  const secondary = (
    <Stack direction="row" alignItems="center" spacing={{ xs: 1, sm: 2 }}>
      <DebouncedInput
        value={globalFilter ?? ''}
        onFilterChange={(value) => setGlobalFilter(String(value))}
        placeholder={rotatingPlaceholder}
      />
      <SelectColumnVisibility
        {...{
          getVisibleLeafColumns: table.getVisibleLeafColumns,
          getIsAllColumnsVisible: table.getIsAllColumnsVisible,
          getToggleAllColumnsVisibilityHandler: table.getToggleAllColumnsVisibilityHandler,
          getAllColumns: table.getAllColumns
        }}
      />
      <Button variant="contained" startIcon={<Add />} onClick={() => navigate('/vendor/add')} size="large">
        Add Vendor
      </Button>
      <CSVExport
        {...{
          data: table.getSortedRowModel().rows.map((d) => d.original),
          headers,
          filename: 'vendor.csv'
        }}
      />
    </Stack>
  );

  const canAddJobRequest = true;

  return (
    <CustomTableContainer
      table={table}
      onAddClick={() => navigate('/vendor/add')}
      csvFilename="Vendors-list.csv"
      showAddButton={canAddJobRequest}
      addLabel="Vendors"
      data={data}
      rowSelection={rowSelection}
      theme={theme}
    />
  );
}

export default TableBench;
