import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewAccount() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      contact_person: '<PERSON>',
      mobile_number: '+****************',
      office_number: '+****************',
      email: '<EMAIL>',
      designation: 'Vendor Relationship Manager',
      payment_terms: 'Net 30'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'contact_person', label: 'Contact Person' },
    { name: 'mobile_number', label: 'Mobile Number' },
    { name: 'office_number', label: 'Office Number' },
    { name: 'email', label: 'Email' },
    { name: 'designation', label: 'Designation' },
    { name: 'payment_terms', label: 'Payment Terms' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Account</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewAccount;
