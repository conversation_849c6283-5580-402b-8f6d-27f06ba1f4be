import React, { useState } from 'react';
import { Box, Typography, IconButton, Menu, MenuItem, Toolt<PERSON>, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm } from 'react-hook-form';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import ViewBankDetailsDialog from './ViewBankDetailsDialog';

const ActionsCell = ({ onEdit }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit();
    handleClose();
  };

  return (
    <>
      <IconButton onClick={handleMenuClick}>
        <MoreVertIcon />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem onClick={handleEdit}>View</MenuItem>
      </Menu>
    </>
  );
};

const ViewBankDetails = () => {
  const { control, handleSubmit } = useForm();

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleEditDialogOpen = () => setOpenEditDialog(true);
  const handleEditDialogClose = () => setOpenEditDialog(false);
  const handleDialogSave = () => {
    // Implement save logic
    setOpenEditDialog(false);
  };

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const [expenses, setExpenses] = useState([
    {
      id: 1,
      beneficiaryName: 'Alice Johnson',
      bankName: 'First National Bank',
      accountType: 'Savings',
      ifscCode: 'FNB0001234',
      accountNumber: '**********',
      depositAmount: '15000',
      priority: 'High'
    },
    {
      id: 2,
      beneficiaryName: 'Bob Smith',
      bankName: 'City Bank',
      accountType: 'Current',
      ifscCode: 'CTB0005678',
      accountNumber: '**********',
      depositAmount: '25000',
      priority: 'Medium'
    },
    {
      id: 3,
      beneficiaryName: 'Carol Davis',
      bankName: 'State Bank',
      accountType: 'Savings',
      ifscCode: 'STB0009012',
      accountNumber: '**********',
      depositAmount: '10000',
      priority: 'Low'
    }
  ]);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount] = useState(expenses.length);

  const handlePageChange = (newPage) => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'beneficiaryName',
      headerName: 'Beneficiary Name',
      flex: 1,
      minWidth: 170,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'bankName',
      headerName: 'Bank Name',
      flex: 1,
      minWidth: 170,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'accountType',
      headerName: 'Account Type',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'ifscCode',
      headerName: 'IFSC Code',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'accountNumber',
      headerName: 'Account Number',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'depositAmount',
      headerName: 'Deposit Amount',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'priority',
      headerName: 'Priority',
      flex: 1,
      minWidth: 100,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 85,
      sortable: false,
      renderCell: () => <ActionsCell onEdit={handleEditDialogOpen} />
    }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Bank Account Details</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Box sx={{ height: 300, width: '100%' }}>
          <CustomDataGrid
            rows={expenses}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(model) => setPaginationModel(model)}
            rowCount={rowCount}
          />
        </Box>

        <ViewBankDetailsDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleDialogSave} control={control} />
      </MainCard>
    </form>
  );
};

export default ViewBankDetails;
//
