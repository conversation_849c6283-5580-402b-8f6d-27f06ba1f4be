import React from 'react';
import { Dialog, DialogTitle, DialogContent, Grid, <PERSON>ack, Typography, IconButton, DialogActions, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function ViewBankDetailsDialog({ open, onClose }) {
  const { getValues } = useForm({
    defaultValues: {
      beneficiary_name: '<PERSON>',
      bank_name: 'Bank of React',
      account_type: 'Savings',
      ifsc_code: 'REACT0001234',
      account_number: '**********',
      deposit_amount: '5000',
      priority: 'High',
      country: 'USA',
      state: 'California',
      city: 'Los Angeles',
      pincode: '90001',
      record_status: 'Active',
      address1: '123 Main St',
      address2: 'Suite 400',
      comments: 'Verified account details.',
      attachment: 'document.pdf',
      cancelled_cheque_attachment: 'cheque.jpg'
    }
  });

  const fields = [
    { name: 'beneficiary_name', label: 'Beneficiary Name' },
    { name: 'bank_name', label: 'Bank Name' },
    { name: 'account_type', label: 'Account Type' },
    { name: 'ifsc_code', label: 'IFSC Code' },
    { name: 'account_number', label: 'Account Number' },
    { name: 'deposit_amount', label: 'Deposit Amount' },
    { name: 'priority', label: 'Priority' },
    { name: 'country', label: 'Country' },
    { name: 'state', label: 'State' },
    { name: 'city', label: 'City' },
    { name: 'pincode', label: 'Pincode' },
    { name: 'record_status', label: 'Record Status' },
    { name: 'address1', label: 'Address 1' },
    { name: 'address2', label: 'Address 2' },
    { name: 'comments', label: 'Comments' },
    { name: 'attachment', label: 'Attachment' },
    { name: 'cancelled_cheque_attachment', label: 'Cancelled Cheque Attachment' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        Bank Details
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>
                  {field.name.includes('attachment') && getValues(field.name) ? (
                    <a href={`/${getValues(field.name)}`} target="_blank" rel="noopener noreferrer">
                      {getValues(field.name)}
                    </a>
                  ) : (
                    getValues(field.name)
                  )}
                </Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ViewBankDetailsDialog;
