import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewBusinessInfo() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      primary_business_unit: 'Corporate HQ',
      business_unit: 'Vendor Ops',
      vendor_visibility: 'Public',
      vendor_name: 'Acme Tech Partners',
      federal_id: '98-7654321',
      website: 'https://acmetechpartners.com',
      contact_number: '+1 (800) 123-4567',
      fax: '+1 (800) 765-4321',
      address: '500 Innovation Drive, Austin, TX 78701',
      country: 'USA',
      state: 'Texas',
      city: 'Austin',
      pincode: '78701',
      send_requirements: 'Yes',
      send_hotlist: 'Yes',
      primary_owner: '<PERSON>',
      ownership: 'Private',
      vendor_lead: '<PERSON>',
      technologies: 'JavaScript, React, Node.js',
      about_vendor: 'Leading provider of front-end development resources with a global reach.',
      vendor_type: 'Preferred'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'primary_business_unit', label: 'Primary Business Unit' },
    { name: 'business_unit', label: 'Business Unit' },
    { name: 'vendor_visibility', label: 'Vendor Visibility' },
    { name: 'vendor_name', label: 'Vendor Name' },
    { name: 'federal_id', label: 'Federal ID' },
    { name: 'website', label: 'Website' },
    { name: 'contact_number', label: 'Contact Number' },
    { name: 'fax', label: 'Fax' },
    { name: 'address', label: 'Address' },
    { name: 'country', label: 'Country' },
    { name: 'state', label: 'State' },
    { name: 'city', label: 'City' },
    { name: 'pincode', label: 'Pincode' },
    { name: 'send_requirements', label: 'Send Requirements' },
    { name: 'send_hotlist', label: 'Send Hotlist' },
    { name: 'primary_owner', label: 'Primary Owner' },
    { name: 'ownership', label: 'Ownership' },
    { name: 'vendor_lead', label: 'Vendor Lead' },
    { name: 'technologies', label: 'Technologies' },
    { name: 'about_vendor', label: 'About Vendor' },
    { name: 'vendor_type', label: 'Vendor Type' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Business Info</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewBusinessInfo;
