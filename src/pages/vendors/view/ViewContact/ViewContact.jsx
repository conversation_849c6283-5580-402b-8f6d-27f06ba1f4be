import React, { useState, useMemo } from 'react';
import { Box, Typography, IconButton, Menu, MenuItem, Tooltip, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import { useTheme } from '@mui/material/styles';
import { useForm } from 'react-hook-form';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import ViewContactDialog from './ViewContactDialog';

const ViewContact = () => {
  const { control, handleSubmit } = useForm();
  const theme = useTheme();

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleEditDialogOpen = () => setOpenEditDialog(true);
  const handleEditDialogClose = () => setOpenEditDialog(false);
  const handleDialogSave = () => {
    // Implement save logic
    setOpenEditDialog(false);
  };

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const [expenses, setExpenses] = useState([
    {
      id: 1,
      firstName: 'Alice',
      lastName: 'Johnson',
      description: 'Project Manager for Team A',
      workPhone: '************',
      mobileNumber: '************'
    },
    {
      id: 2,
      firstName: 'Bob',
      lastName: 'Smith',
      description: 'Senior Developer',
      workPhone: '************',
      mobileNumber: '************'
    },
    {
      id: 3,
      firstName: 'Carol',
      lastName: 'Davis',
      description: 'HR Specialist',
      workPhone: '************',
      mobileNumber: '************'
    }
  ]);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount] = useState(expenses.length); // or server-side count

  const handlePageChange = (newPage) => {
    setPage(newPage + 1); // DataGrid 0-indexed
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };
  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'firstName',
      headerName: 'First Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'lastName',
      headerName: 'Last Name',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'workPhone',
      headerName: 'Work Phone',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'mobileNumber',
      headerName: 'Mobile Number',
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => {
        const [anchorEl, setAnchorEl] = React.useState(null);
        const open = Boolean(anchorEl);

        const handleMenuClick = (event) => {
          event.stopPropagation();
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          handleEditDialogOpen();
          handleClose();
        };

        return (
          <>
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
              <MenuItem onClick={handleEdit}>View</MenuItem>
            </Menu>
          </>
        );
      }
    }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Contact Info</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Box sx={{ height: 300, width: '100%' }}>
          <CustomDataGrid
            rows={expenses}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(model) => setPaginationModel(model)}
            rowCount={rowCount}
          />
        </Box>

        <ViewContactDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleDialogSave} control={control} />
      </MainCard>
    </form>
  );
};

export default ViewContact;
