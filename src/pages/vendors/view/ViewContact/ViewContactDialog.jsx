import React from 'react';
import { Dialog, DialogTitle, DialogContent, <PERSON>rid, <PERSON>ack, Typography, IconButton, Divider, DialogActions, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function ViewContactDialog({ open, onClose }) {
  const { getValues } = useForm({
    defaultValues: {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      designation: 'IT Manager',
      work_phone: '+****************',
      mobile_number: '+****************',
      email: '<EMAIL>',
      primary_owner: '<PERSON>',
      ownership: 'Private',
      address1: '123 Main St',
      address2: 'Suite 400',
      country: 'USA',
      state: 'California',
      city: 'Los Angeles',
      pincode: '90001',
      linkedin_url: 'https://linkedin.com/in/johndoe',
      facebook_url: 'https://facebook.com/johndoe',
      twitter_url: 'https://twitter.com/johndoe',
      google_url: 'https://plus.google.com/johndoe',
      status: 'Active',
      technologies: 'Java, React, Node.js',
      vendor_group: 'Preferred Vendors',
      vendor_contact_type: 'Primary',
      send_requirement: 'Yes',
      send_hotlist: 'No',
      comments: 'Reliable vendor with a strong candidate pool.'
    }
  });

  const fields = [
    { name: 'first_name', label: 'First Name' },
    { name: 'last_name', label: 'Last Name' },
    { name: 'designation', label: 'Designation' },
    { name: 'work_phone', label: 'Work Phone' },
    { name: 'mobile_number', label: 'Mobile Number' },
    { name: 'email', label: 'Email' },
    { name: 'primary_owner', label: 'Primary Owner' },
    { name: 'ownership', label: 'Ownership' },
    { name: 'address1', label: 'Address 1' },
    { name: 'address2', label: 'Address 2' },
    { name: 'country', label: 'Country' },
    { name: 'state', label: 'State' },
    { name: 'city', label: 'City' },
    { name: 'pincode', label: 'Pincode' },
    { name: 'linkedin_url', label: 'LinkedIn Profile URL' },
    { name: 'facebook_url', label: 'Facebook Profile URL' },
    { name: 'twitter_url', label: 'Twitter Profile URL' },
    { name: 'google_url', label: 'Google Profile URL' },
    { name: 'status', label: 'Status' },
    { name: 'technologies', label: 'Technologies' },
    { name: 'vendor_group', label: 'Vendor Group' },
    { name: 'vendor_contact_type', label: 'Vendor Contact Type' },
    { name: 'send_requirement', label: 'Send Requirement' },
    { name: 'send_hotlist', label: 'Send Hotlist' },
    { name: 'comments', label: 'Comments' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        Vendor Contact Details
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ViewContactDialog;
