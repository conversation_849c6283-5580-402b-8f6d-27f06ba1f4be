import React, { useState } from 'react';
import { Box, Typography, IconButton, Menu, MenuItem, Toolt<PERSON>, Divider } from '@mui/material';
import MainCard from 'components/MainCard';
import { useForm } from 'react-hook-form';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import ViewDocumentDialog from './ViewDocumentDialog';

const ActionsCell = ({ onEdit }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit();
    handleClose();
  };

  return (
    <>
      <IconButton onClick={handleMenuClick}>
        <MoreVertIcon />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem onClick={handleEdit}>View</MenuItem>
      </Menu>
    </>
  );
};

const ViewDocument = () => {
  const { control, handleSubmit } = useForm();

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleEditDialogOpen = () => setOpenEditDialog(true);
  const handleEditDialogClose = () => setOpenEditDialog(false);
  const handleDialogSave = () => {
    // Implement save logic
    setOpenEditDialog(false);
  };

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const [expenses, setExpenses] = useState([
    {
      id: 1,
      documentType: 'Contract',
      documentTitle: 'Vendor Agreement 2025',
      uploadedDocument: 'vendor_agreement_2025.pdf',
      description: 'Signed agreement for 2025 services.'
    },
    {
      id: 2,
      documentType: 'Invoice',
      documentTitle: 'Invoice #12345',
      uploadedDocument: 'invoice_12345.pdf',
      description: 'Payment invoice for April 2025.'
    },
    {
      id: 3,
      documentType: 'Report',
      documentTitle: 'Annual Report 2024',
      uploadedDocument: 'annual_report_2024.pdf',
      description: 'Comprehensive year-end report.'
    }
  ]);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount] = useState(expenses.length);

  const handlePageChange = (newPage) => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };
  const [paginationModel, setPaginationModel] = useState({
    pageSize: 10,
    page: 0
  });

  const columns = [
    {
      field: 'documentType',
      headerName: 'Document Type',
      flex: 1,
      minWidth: 170,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'documentTitle',
      headerName: 'Document Title',
      flex: 2,
      minWidth: 170,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'uploadedDocument',
      headerName: 'Uploaded Document',
      flex: 2,
      minWidth: 170,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2" sx={{ textDecoration: 'underline', color: 'blue', cursor: 'pointer' }}>
            {params.value}
          </Typography>
        </Tooltip>
      )
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} placement="bottom">
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    }
    // {
    //   field: 'actions',
    //   headerName: 'Actions',
    //   flex: 0.5,
    //   minWidth: 85,
    //   sortable: false,
    //   renderCell: () => <ActionsCell onEdit={handleEditDialogOpen} />
    // }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Document Info</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Box sx={{ height: 300, width: '100%' }}>
          <CustomDataGrid
            rows={expenses}
            columns={columns}
            paginationModel={paginationModel}
            onPaginationModelChange={(model) => setPaginationModel(model)}
            rowCount={expenses.length}
          />
        </Box>

        <ViewDocumentDialog open={openEditDialog} onClose={handleEditDialogClose} onSave={handleDialogSave} control={control} />
      </MainCard>
    </form>
  );
};

export default ViewDocument;
//
