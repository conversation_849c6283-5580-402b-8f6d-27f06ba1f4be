import React from 'react';
import { Dialog, DialogTitle, DialogContent, Grid, Stack, Typography, IconButton, DialogActions, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function ViewDocumentDialog({ open, onClose }) {
  const { getValues } = useForm({
    defaultValues: {
      document_type: 'Contract',
      document_title: 'Vendor Agreement 2025',
      description: 'Signed agreement for 2025 services.',
      uploaded_document: 'vendor_agreement_2025.pdf'
    }
  });

  const fields = [
    { name: 'document_type', label: 'Document Type' },
    { name: 'document_title', label: 'Document Title' },
    { name: 'description', label: 'Description' },
    { name: 'uploaded_document', label: 'Uploaded Document' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        Document Details
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {fields.map((field) => (
            <Grid item xs={12} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} size="small" variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ViewDocumentDialog;
