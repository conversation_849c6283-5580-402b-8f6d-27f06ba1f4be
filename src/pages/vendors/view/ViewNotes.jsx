import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewNotes() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      actions: 'Follow up next week',
      notes_priority: 'High',
      new_notes: 'Request updated compliance documentation.',
      notify_people: '<EMAIL>, <EMAIL>',
      note: 'Initial contact made. Waiting for confirmation.',
      remind_me: '2025-06-01'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'actions', label: 'Actions' },
    { name: 'notes_priority', label: 'Notes Priority' },
    { name: 'new_notes', label: 'New Notes' },
    { name: 'notify_people', label: 'Notify These People' },
    { name: 'note', label: 'Note' },
    { name: 'remind_me', label: 'Remind Me' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{  borderRadius: '2%', backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Vendor Notes</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewNotes;
