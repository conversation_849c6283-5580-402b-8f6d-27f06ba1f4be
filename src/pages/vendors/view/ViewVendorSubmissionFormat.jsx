import React from 'react';
import { Grid, Stack, Typography, Box, Divider } from '@mui/material';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import MainCard from 'components/MainCard';

function ViewVendorSubmissionFormat() {
  const {
    getValues,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues: {
      vendor: 'Acme Tech Partners',
      vendor_lead: '<PERSON>',
      subject: 'Vendor Partnership Inquiry',
      mailing_subject: 'Collaboration Proposal: Acme Tech',
      body: 'We are interested in exploring a partnership to fulfill ongoing staffing needs in the tech domain. Please respond with your availability for a call.'
    }
  });

  const onSubmit = (data) => {
    console.log('Form Data:', data);
  };

  const fields = [
    { name: 'vendor', label: 'Vendor' },
    { name: 'vendor_lead', label: 'Vendor Lead' },
    { name: 'subject', label: 'Subject' },
    { name: 'mailing_subject', label: 'Mailing Subject' },
    { name: 'body', label: 'Body' }
  ];

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <MainCard sx={{ borderRadius: '0px', backgroundColor: 'white', border: 'none' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, px: 2 }}>
          <Typography variant="h5">Vendor Message Details</Typography>
        </Box>
        <Divider sx={{ marginBottom: 2 }} />

        <Grid container spacing={3}>
          {fields.map((field) => (
            <Grid item xs={12} sm={6} xl={4} key={field.name}>
              <Stack spacing={1}>
                <CustomInputLabel>{field.label}</CustomInputLabel>
                <Typography>{getValues(field.name)}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </MainCard>
    </Box>
  );
}

export default ViewVendorSubmissionFormat;
