import { useState, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
// material-ui
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { Button, Box, CardActions, Typography, Stack, Snackbar, Alert, useMediaQuery, Divider } from '@mui/material';
import ContactsIcon from '@mui/icons-material/Contacts';
import MainCard from 'components/MainCard';
import { Profile } from 'iconsax-react';
import { useTheme } from '@mui/material/styles';
import JWTContext from 'contexts/JWTContext';

import ViewBusinessInfo from '../ViewBusinessInfo';
import ViewAccount from '../ViewAccount';
import ViewNotes from '../ViewNotes';
import ViewVendorSubmissionFormat from '../ViewVendorSubmissionFormat';
import ViewContact from '../ViewContact/ViewContact';
import ViewDocument from '../ViewDocument/ViewDocument';
import ViewBankDetails from '../ViewBankAccount/ViewBankAccount';

const HEADER_HEIGHT = 0;

export default function ViewVendor() {
  const navigate = useNavigate();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useContext(JWTContext);

  const [selectedTab, setSelectedTab] = useState(0);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [openSnackbar, setOpenSnackbar] = useState(false);

  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };

  const handleChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0:
        return <ViewBusinessInfo />;
      case 1:
        return <ViewAccount />;
      case 2:
        return <ViewNotes />;
      case 3:
        return <ViewContact />;
        // For V2
      // case 4:
      //   return <ViewVendorSubmissionFormat />;
      case 4:
        return <ViewDocument />;
      case 5:
        return <ViewBankDetails />;
      default:
        return null;
    }
  };

  return (
    <>
      {isSmallScreen ? (
        <>
          <CardActions
            sx={{
              position: 'sticky',
              top: 0,
              bgcolor: 'background.default',
              zIndex: 1100,
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Typography variant="h5" sx={{ m: 0, pl: 1.5 }}>
                View Vendors
              </Typography>
              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75 }}>
                <Button variant="outlined" size="small" component={Link} to="/vendor">
                  Cancel
                </Button>
              </Stack>
            </Stack>
          </CardActions>
          <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible', mt: -3 }}>
            <Box
              sx={{
                bgcolor: 'background.default',
                position: 'sticky',
                top: 50,
                alignSelf: 'flex-start',
                zIndex: 80
              }}
            >
              <Tabs
                value={selectedTab}
                onChange={handleChange}
                aria-label="vendor information navigation"
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab label="Business Information" icon={<Profile />} iconPosition="start" />
                <Tab label="Accounts" icon={<ContactsIcon />} iconPosition="start" />
                <Tab label="Notes" icon={<ContactsIcon />} iconPosition="start" />
                <Tab label="Contacts" icon={<Profile />} iconPosition="start" />
                {/* For V2 */}
                {/* <Tab label="Vendor Submission Format" icon={<ContactsIcon />} iconPosition="start" /> */}
                <Tab label="Documents" icon={<ContactsIcon />} iconPosition="start" />
                <Tab label="Bank Account Details" icon={<Profile />} iconPosition="start" />
              </Tabs>
            </Box>
            <Box sx={{ mt: 2.0 }}>{renderTabContent()}</Box>
          </MainCard>
        </>
      ) : (
        <>
          <CardActions
            sx={{
              position: 'sticky',
              top: 0,
              bgcolor: 'background.default',
              zIndex: 1100,
              borderBottom: '1px solid',
              borderBottomColor: theme.palette.divider,
              padding: '8px 16px'
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: 1 }}>
              <Box sx={{ width: '100%', mt: '8px', overflowX: 'auto' }}>
                <Tabs
                  value={selectedTab}
                  onChange={handleChange}
                  aria-label="vendor information navigation"
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab label="Business Information" icon={<Profile />} iconPosition="start" />
                  <Tab label="Accounts" icon={<ContactsIcon />} iconPosition="start" />
                  <Tab label="Notes" icon={<ContactsIcon />} iconPosition="start" />
                  <Tab label="Contacts" icon={<Profile />} iconPosition="start" />
                  {/* For V2 */}
                  {/* <Tab label="Vendor Submission Format" icon={<ContactsIcon />} iconPosition="start" /> */}
                  <Tab label="Documents" icon={<ContactsIcon />} iconPosition="start" />
                  <Tab label="Bank Account Details" icon={<Profile />} iconPosition="start" />
                </Tabs>
              </Box>

              <Stack direction="row" spacing={1} sx={{ px: 1.5, py: 0.75, mt: 1 }}>
                <Button variant="outlined" size="small" component={Link} to="/vendor">
                  Cancel
                </Button>
              </Stack>
            </Stack>
          </CardActions>
          <MainCard border={false} sx={{ backgroundColor: 'transparent', overflow: 'visible' }}>
            <Box sx={{ mt: 2.0 }}>{renderTabContent()}</Box>
          </MainCard>
        </>
      )}
      <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={handleSnackbarClose}>
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}